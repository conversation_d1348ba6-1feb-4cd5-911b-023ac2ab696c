import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld("electronAPI", {
  getServerPort: () => ipcRenderer.invoke("get-server-port"),
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),
  quitApp: () => ipcRenderer.invoke("quit-app"),

  // Add more secure API methods as needed
  platform: process.platform,

  // Event listeners for main process events
  onWindowEvent: (callback: (event: string) => void) => {
    ipcRenderer.on("window-event", (_, event) => callback(event));
  },

  removeAllListeners: () => {
    ipcRenderer.removeAllListeners("window-event");
  },
});

// Type definitions for TypeScript
declare global {
  interface Window {
    electronAPI: {
      getServerPort: () => Promise<number>;
      getAppVersion: () => Promise<string>;
      quitApp: () => Promise<void>;
      platform: string;
      onWindowEvent: (callback: (event: string) => void) => void;
      removeAllListeners: () => void;
    };
  }
}
