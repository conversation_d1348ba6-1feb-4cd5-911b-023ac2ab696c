import { app, BrowserWindow, ipc<PERSON>ain, Menu, shell } from "electron";
import { join } from "path";
import { createServer } from "../server/index";
import { WebSocketManager } from "../server/websocket";

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require("electron-squirrel-startup")) {
  app.quit();
}

let mainWindow: BrowserWindow;
let httpServer: any;
let wsManager: WebSocketManager;

const isDevelopment = process.env.NODE_ENV === "development";
const port = process.env.PORT || 3001;

async function createWindow(): Promise<void> {
  // Create the browser window
  mainWindow = new BrowserWindow({
    height: 900,
    width: 1400,
    minWidth: 1000,
    minHeight: 700,
    show: false,
    icon: join(__dirname, "assets/icon.png"), // Add an icon if you have one
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, "preload.cjs"),
    },
    titleBarStyle: "default",
    frame: true,
  });

  // Start the Express server with WebSocket support
  try {
    httpServer = createServer();
    wsManager = new WebSocketManager(httpServer);

    await new Promise<void>((resolve, reject) => {
      httpServer.listen(port, (err: any) => {
        if (err) {
          console.error("Failed to start server:", err);
          reject(err);
        } else {
          console.log(`✅ Server running on port ${port}`);
          console.log(`🔌 WebSocket available at ws://localhost:${port}/ws`);
          resolve();
        }
      });
    });
  } catch (error) {
    console.error("Failed to start server:", error);
  }

  // Load the app
  if (isDevelopment) {
    // In development, load from Vite dev server
await mainWindow.loadURL("http://W8File.Importer.Local");
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the built files and serve them through our Express server
    await mainWindow.loadURL(`http://localhost:${port}`);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once("ready-to-show", () => {
    mainWindow.show();

    // Focus on window creation
    if (isDevelopment) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on("closed", () => {
    if (httpServer) {
      httpServer.close();
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: "deny" };
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // Set up the menu
  createMenu();
});

// Quit when all windows are closed, except on macOS
app.on("window-all-closed", () => {
  if (httpServer) {
    httpServer.close();
  }

  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("before-quit", () => {
  if (httpServer) {
    httpServer.close();
  }
});

// Security: Prevent navigation to external websites
app.on("web-contents-created", (event, contents) => {
  contents.on("will-navigate", (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    // Allow navigation to localhost and the app's domain
    if (
      parsedUrl.origin !== `http://localhost:${port}` &&
parsedUrl.origin !== "http://W8File.Importer.Local"
    ) {
      event.preventDefault();
    }
  });
});

function createMenu() {
  const template: any[] = [
    {
      label: "File",
      submenu: [
        {
          label: "Refresh",
          accelerator: "CmdOrCtrl+R",
          click: () => {
            if (mainWindow) {
              mainWindow.reload();
            }
          },
        },
        {
          label: "Toggle DevTools",
          accelerator: "F12",
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.toggleDevTools();
            }
          },
        },
        { type: "separator" },
        {
          label: "Quit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: "Edit",
      submenu: [
        { role: "undo" },
        { role: "redo" },
        { type: "separator" },
        { role: "cut" },
        { role: "copy" },
        { role: "paste" },
      ],
    },
    {
      label: "View",
      submenu: [
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "actualSize" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" },
      ],
    },
    {
      label: "Window",
      submenu: [{ role: "minimize" }, { role: "close" }],
    },
  ];

  // macOS menu adjustments
  if (process.platform === "darwin") {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: "about" },
        { type: "separator" },
        { role: "services" },
        { type: "separator" },
        { role: "hide" },
        { role: "hideOthers" },
        { role: "unhide" },
        { type: "separator" },
        { role: "quit" },
      ],
    });

    // Window menu
    template[4].submenu = [
      { role: "close" },
      { role: "minimize" },
      { role: "zoom" },
      { type: "separator" },
      { role: "front" },
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers for communication between main and renderer processes
ipcMain.handle("get-server-port", () => {
  return port;
});

ipcMain.handle("get-app-version", () => {
  return app.getVersion();
});

ipcMain.handle("quit-app", () => {
  console.log("Application quit requested due to connection timeout");

  // Close server gracefully
  if (httpServer) {
    httpServer.close();
  }

  // Quit the application
  app.quit();
});
