{"name": "fusion-starter", "private": true, "type": "module", "version": "0.0.1", "pkg": {"assets": ["dist/spa/*"], "scripts": ["dist/server/**/*.js"]}, "main": "dist/electron/main.cjs", "scripts": {"dev": "vite", "dev:electron": "concurrently \"npm run dev\" \"wait-on http://W8File.Importer.Local:5173 && npm run electron:dev\"", "dev:electron:proxy": "concurrently \"npm run dev\" \"npm run proxy\" \"wait-on http://W8File.Importer.Local && npm run electron:dev:proxy\"", "proxy": "node server/proxy.js", "electron:dev": "set NODE_ENV=development && npx electron .", "electron:dev:proxy": "set NODE_ENV=development && npx electron .", "build": "npm run build:client && npm run build:server && npm run build:electron", "build:client": "vite build", "build:server": "vite build --config vite.config.server.ts", "build:electron": "vite build --config vite.config.electron.ts", "electron:build": "npm run build && electron-builder", "electron:pack": "npm run build && electron-builder --dir", "start": "node dist/server/node-build.mjs", "start:electron": "npm run build && electron .", "test": "vitest --run", "format.fix": "prettier --write .", "typecheck": "tsc"}, "dependencies": {"@types/ws": "^8.18.1", "dotenv": "^17.2.0", "express": "^4.18.2", "http-proxy": "^1.18.1", "lottie-react": "^2.4.1", "mermaid": "^11.9.0", "ws": "^8.18.3", "zod": "^3.23.8"}, "build": {"appId": "com.fusion.w8file", "productName": "W8 File Importer", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "node_modules/**/*"], "extraFiles": [{"from": "dist/server", "to": "server"}], "mac": {"category": "public.app-category.productivity", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "devDependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^8.18.0", "@swc/core": "^1.11.24", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.56.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/three": "^0.176.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "electron-squirrel-startup": "^1.0.1", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.6.2", "globals": "^15.9.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "serverless-http": "^3.2.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "tsx": "^4.7.0", "typescript": "^5.5.3", "vaul": "^0.9.3", "vite": "^6.2.2", "vitest": "^3.1.4", "wait-on": "^8.0.4"}}