import "dotenv/config";
import express from "express";
import cors from "cors";
import { createServer as createHttpServer } from "http";
import { handleDemo } from "./routes/demo";
import {
  handleProgressUpdate,
  handleEventUpdate,
  handleBulkUpdate,
  handleWebSocketHealth,
  setWebSocketManager as setWSManager,
} from "./routes/websocket-data";
import {
  authenticateApiKey,
  rateLimitByApiKey,
  logApiUsage,
} from "./middleware/auth";
import {
  createSchedule,
  getSchedules,
  getSchedule,
  updateSchedule,
  deleteSchedule,
  toggleSchedule,
  executeSchedule,
} from "./routes/schedules";

// Re-export for external use
export { setWSManager as setWebSocketManager };
import { WebSocketManager } from "./websocket";

export function createServer() {
  const app = express();
  const httpServer = createHttpServer(app);

  // Middleware
  app.use(cors());
  app.use(express.json({ limit: "10mb" })); // Increased limit for bulk updates
  app.use(express.urlencoded({ extended: true }));

  // Example API routes
  app.get("/api/ping", (_req, res) => {
    const ping = process.env.PING_MESSAGE ?? "ping";
    res.json({ message: ping });
  });

  app.get("/api/demo", handleDemo);

  // Schedule management routes
  app.post("/api/schedules", createSchedule);
  app.get("/api/schedules", getSchedules);
  app.get("/api/schedules/:id", getSchedule);
  app.put("/api/schedules/:id", updateSchedule);
  app.delete("/api/schedules/:id", deleteSchedule);
  app.patch("/api/schedules/:id/toggle", toggleSchedule);
  app.post("/api/schedules/:id/execute", executeSchedule);

  // WebSocket data routes for external applications (with authentication)
  app.post(
    "/api/progress",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleProgressUpdate,
  );
  app.post(
    "/api/events",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleEventUpdate,
  );
  app.post(
    "/api/bulk",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleBulkUpdate,
  );
  app.get("/api/ws-health", handleWebSocketHealth); // Public health check

  // Initialize WebSocket and link it to the routes
  try {
    console.log("🚀 Initializing WebSocket manager...");
    const wsManager = new WebSocketManager(httpServer);
    setWSManager(wsManager);
    console.log("✅ WebSocket manager initialized successfully");
  } catch (error) {
    console.error("💀 FATAL: Failed to initialize WebSocket manager:", error);
    // Still set a null manager to prevent undefined errors
    setWSManager(null);
  }

  return httpServer;
}

export function createExpressApp() {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(express.json({ limit: "10mb" }));
  app.use(express.urlencoded({ extended: true }));

  // Example API routes
  app.get("/ping", (_req, res) => {
    const ping = process.env.PING_MESSAGE ?? "ping";
    res.json({ message: ping });
  });

  app.get("/demo", handleDemo);

  // Schedule management routes
  app.post("/schedules", createSchedule);
  app.get("/schedules", getSchedules);
  app.get("/schedules/:id", getSchedule);
  app.put("/schedules/:id", updateSchedule);
  app.delete("/schedules/:id", deleteSchedule);
  app.patch("/schedules/:id/toggle", toggleSchedule);
  app.post("/schedules/:id/execute", executeSchedule);

  // WebSocket data routes for external applications (with authentication)
  app.post(
    "/progress",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleProgressUpdate,
  );
  app.post(
    "/events",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleEventUpdate,
  );
  app.post(
    "/bulk",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleBulkUpdate,
  );
  app.get("/ws-health", handleWebSocketHealth); // Public health check

  return app;
}
