import { WebSocketServer, WebSocket } from "ws";
import { Server } from "http";

interface ProgressUpdate {
  type: "progress";
  data: {
    id: string;
    title: string;
    type: "sync" | "upload" | "download";
    progress: number;
    status: "running" | "completed" | "failed" | "paused";
    startTime: Date;
    estimatedCompletion?: Date;
    currentFile?: string;
    totalFiles?: number;
    processedFiles?: number;
  };
}

interface EventUpdate {
  type: "event";
  data: {
    id: string;
    title: string;
    message: string;
    type: "success" | "error" | "warning" | "info";
    category: "sync" | "upload" | "download" | "system" | "auth";
    timestamp: Date;
    clientPath?: string;
    details?: string;
  };
}

type WSMessage = ProgressUpdate | EventUpdate;

export class WebSocketManager {
  private wss: WebSocketServer;
  private clients: Set<WebSocket> = new Set();
  private recentProgress: Map<string, any> = new Map();
  private recentEvents: any[] = [];

  constructor(server: Server) {
    console.log("🔧 Creating WebSocket manager with server:", !!server);

    try {
      this.wss = new WebSocketServer({
        noServer: true, // Don't create a separate HTTP server
      });
      console.log("✅ WebSocketServer created successfully");

      // Handle WebSocket upgrade on the existing server
      server.on("upgrade", (request, socket, head) => {
        console.log("🔄 WebSocket upgrade request for:", request.url);
        if (request.url === "/ws") {
          this.wss.handleUpgrade(request, socket, head, (ws) => {
            this.wss.emit("connection", ws, request);
          });
        }
      });
      console.log("✅ WebSocket upgrade handler registered");

      this.setupWebSocket();
      this.startMinimalDemoData(); // Reduced demo data
      console.log("✅ WebSocket manager fully initialized");
    } catch (error) {
      console.error("💀 Error in WebSocket manager constructor:", error);
      throw error;
    }
  }

  private setupWebSocket() {
    this.wss.on("connection", (ws: WebSocket) => {
      console.log("WebSocket client connected");
      this.clients.add(ws);

      // Send initial data when client connects
      this.sendInitialData(ws);

      ws.on("close", () => {
        console.log("WebSocket client disconnected");
        this.clients.delete(ws);
      });

      ws.on("error", (error) => {
        console.error("WebSocket error:", error);
        this.clients.delete(ws);
      });
    });
  }

  private sendInitialData(ws: WebSocket) {
    // Send stored progress items
    this.recentProgress.forEach((progress) => {
      this.sendMessage(ws, { type: "progress", data: progress });
    });

    // Send recent events (last 20)
    this.recentEvents.slice(-20).forEach((event) => {
      this.sendMessage(ws, { type: "event", data: event });
    });

    // Send a welcome message if no data exists
    if (this.recentProgress.size === 0 && this.recentEvents.length === 0) {
      this.sendMessage(ws, {
        type: "event",
        data: {
          id: "welcome",
          title: "WebSocket Connected",
          message: "Ready to receive updates from external applications",
          type: "info" as const,
          category: "system" as const,
          timestamp: new Date(),
        },
      });
    }
  }

  private sendMessage(ws: WebSocket, message: WSMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  public broadcast(message: WSMessage) {
    // Store the data for new connections
    if (message.type === "progress") {
      this.recentProgress.set(message.data.id, message.data);

      // Clean up completed tasks after 5 minutes
      if (message.data.status === "completed") {
        setTimeout(
          () => {
            this.recentProgress.delete(message.data.id);
          },
          5 * 60 * 1000,
        );
      }
    } else if (message.type === "event") {
      this.recentEvents.push(message.data);

      // Keep only last 50 events
      if (this.recentEvents.length > 50) {
        this.recentEvents = this.recentEvents.slice(-50);
      }
    }

    // Broadcast to all connected clients
    this.clients.forEach((ws) => {
      this.sendMessage(ws, message);
    });
  }

  public getConnectedClientsCount(): number {
    return this.clients.size;
  }

  public getRecentProgress(): any[] {
    return Array.from(this.recentProgress.values());
  }

  public getRecentEvents(): any[] {
    return this.recentEvents.slice(-20);
  }

  public clearProgress(progressId: string): boolean {
    return this.recentProgress.delete(progressId);
  }

  public clearAllProgress(): void {
    this.recentProgress.clear();
  }

  public clearEvents(): void {
    this.recentEvents = [];
  }

  private startMinimalDemoData() {
    // Only generate minimal demo data if no external data is received
    let lastExternalDataTime = Date.now();

    // Check for external data activity
    const originalBroadcast = this.broadcast.bind(this);
    this.broadcast = (message: WSMessage) => {
      lastExternalDataTime = Date.now();
      originalBroadcast(message);
    };

    // Generate minimal demo data only if no external data for 30 seconds
    const demoInterval = setInterval(() => {
      const timeSinceLastExternal = Date.now() - lastExternalDataTime;

      // Only send demo data if no external data received recently and no active progress
      if (timeSinceLastExternal > 30000 && this.recentProgress.size === 0) {
        const demoEvent: EventUpdate = {
          type: "event",
          data: {
            id: `demo-${Date.now()}`,
            title: "Waiting for external data",
            message:
              "No external applications are currently sending data. Send POST requests to /api/progress or /api/events to see real-time updates.",
            type: "info" as const,
            category: "system" as const,
            timestamp: new Date(),
          },
        };

        // Use original broadcast to avoid updating lastExternalDataTime
        originalBroadcast(demoEvent);
      }
    }, 60000); // Check every minute

    // Clean up interval when needed
    process.on("SIGTERM", () => {
      clearInterval(demoInterval);
    });
  }
}
