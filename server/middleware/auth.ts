import { Request<PERSON><PERSON><PERSON> } from "express";

// Simple API key authentication
// In production, use a more secure system with database storage
const VALID_API_KEYS = new Set([
  process.env.API_KEY || 'demo-api-key-12345',
  'external-app-key-67890',
  // Add more keys as needed
]);

export const authenticateApiKey: RequestHandler = (req, res, next) => {
  const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
  
  if (!apiKey) {
    return res.status(401).json({ 
      error: 'Missing API key. Include X-API-Key header or Authorization: Bearer token' 
    });
  }

  if (!VALID_API_KEYS.has(apiKey as string)) {
    return res.status(403).json({ 
      error: 'Invalid API key' 
    });
  }

  // Store the API key in the request for logging
  (req as any).apiKey = apiKey;
  next();
};

// Rate limiting middleware
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 100; // requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute

export const rateLimitByApiKey: RequestHandler = (req, res, next) => {
  const apiKey = (req as any).apiKey || 'unknown';
  const now = Date.now();
  
  const keyData = requestCounts.get(apiKey);
  
  if (!keyData || now > keyData.resetTime) {
    // Reset or initialize counter
    requestCounts.set(apiKey, { count: 1, resetTime: now + RATE_WINDOW });
    next();
  } else if (keyData.count < RATE_LIMIT) {
    // Increment counter
    keyData.count++;
    next();
  } else {
    // Rate limit exceeded
    res.status(429).json({ 
      error: 'Rate limit exceeded',
      limit: RATE_LIMIT,
      windowMs: RATE_WINDOW,
      resetTime: new Date(keyData.resetTime).toISOString()
    });
  }
};

// Optional: Middleware to log API usage
export const logApiUsage: RequestHandler = (req, res, next) => {
  const apiKey = (req as any).apiKey || 'unknown';
  const timestamp = new Date().toISOString();
  
  console.log(`📡 API Request: ${req.method} ${req.path} from ${apiKey} at ${timestamp}`);
  
  next();
};
