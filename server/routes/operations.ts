import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWebSocketManager } from "./websocket-data";

// In-memory storage for operations (in production, use a database)
const activeOperations = new Map();

// Start operation handler
export const handleOperationStart: RequestHandler = (req, res) => {
  try {
    const { operationType, source, destination, options } = req.body;

    if (!operationType || !source || !destination) {
      return res.status(400).json({
        error: "Missing required fields: operationType, source, destination",
      });
    }

    const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store operation details
    const operation = {
      id: operationId,
      type: operationType,
      source,
      destination,
      options: options || {},
      status: "starting",
      progress: 0,
      startTime: new Date().toISOString(),
      files: {
        total: options?.totalFiles || 0,
        processed: 0,
        failed: 0,
      },
    };

    activeOperations.set(operationId, operation);

    // Broadcast operation start via WebSocket
    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "operation-started",
        data: {
          id: operationId,
          operationType,
          source,
          destination,
          status: "starting",
          timestamp: new Date().toISOString(),
        },
      });

      // Simulate progress updates (in real implementation, this would be actual file operations)
      simulateOperation(operationId, wsManager);
    }

    console.log(`🚀 Operation started: ${operationType} (${operationId})`);

    res.json({
      success: true,
      message: "Operation started successfully",
      operationId,
      status: "starting",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Operation start error:", error);
    res.status(500).json({ error: "Failed to start operation" });
  }
};

// Control operation handler (pause, resume, cancel)
export const handleOperationControl: RequestHandler = (req, res) => {
  try {
    const { operationId, action } = req.body;

    if (!operationId || !action) {
      return res.status(400).json({
        error: "Missing required fields: operationId and action",
      });
    }

    const operation = activeOperations.get(operationId);
    if (!operation) {
      return res.status(404).json({ error: "Operation not found" });
    }

    // Update operation status
    operation.status = action === "cancel" ? "cancelled" : action;
    operation.lastUpdated = new Date().toISOString();

    // Broadcast control action via WebSocket
    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "operation-control",
        data: {
          id: operationId,
          action,
          status: operation.status,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`🎛️ Operation ${action}: ${operationId}`);

    res.json({
      success: true,
      message: `Operation ${action} successfully`,
      operationId,
      action,
      status: operation.status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Operation control error:", error);
    res.status(500).json({ error: "Failed to control operation" });
  }
};

// Get operation status handler
export const handleOperationStatus: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;

    const operation = activeOperations.get(id);
    if (!operation) {
      return res.status(404).json({ error: "Operation not found" });
    }

    res.json({
      success: true,
      operation: {
        id: operation.id,
        type: operation.type,
        status: operation.status,
        progress: operation.progress,
        startTime: operation.startTime,
        files: operation.files,
        currentFile: operation.currentFile,
        estimatedCompletion: operation.estimatedCompletion,
      },
    });
  } catch (error) {
    console.error("Operation status error:", error);
    res.status(500).json({ error: "Failed to get operation status" });
  }
};

// List operations handler
export const handleOperationsList: RequestHandler = (req, res) => {
  try {
    const operations = Array.from(activeOperations.values()).map((op) => ({
      id: op.id,
      type: op.type,
      status: op.status,
      progress: op.progress,
      startTime: op.startTime,
      source: op.source,
      destination: op.destination,
    }));

    res.json({
      success: true,
      operations,
      total: operations.length,
    });
  } catch (error) {
    console.error("Operations list error:", error);
    res.status(500).json({ error: "Failed to list operations" });
  }
};

// Batch operations handler
export const handleBatchOperations: RequestHandler = (req, res) => {
  try {
    const { operations } = req.body;

    if (!operations || !Array.isArray(operations)) {
      return res.status(400).json({
        error: "Missing or invalid operations array",
      });
    }

    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const operationIds: string[] = [];

    // Start each operation in the batch
    operations.forEach((op, index) => {
      const operationId = `${batchId}_op_${index}`;
      const operation = {
        id: operationId,
        batchId,
        type: op.operationType,
        source: op.source,
        destination: op.destination,
        options: op.options || {},
        status: "queued",
        progress: 0,
        startTime: new Date().toISOString(),
        batchOrder: index,
      };

      activeOperations.set(operationId, operation);
      operationIds.push(operationId);
    });

    // Broadcast batch start via WebSocket
    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "batch-operation-started",
        data: {
          batchId,
          operationIds,
          totalOperations: operations.length,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(
      `📦 Batch operation started: ${batchId} (${operations.length} operations)`,
    );

    res.json({
      success: true,
      message: "Batch operations started successfully",
      batchId,
      operationIds,
      totalOperations: operations.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Batch operations error:", error);
    res.status(500).json({ error: "Failed to start batch operations" });
  }
};

// Priority management handler
export const handleOperationPriority: RequestHandler = (req, res) => {
  try {
    const { operationId, priority } = req.body;

    if (!operationId || !priority) {
      return res.status(400).json({
        error: "Missing required fields: operationId and priority",
      });
    }

    const operation = activeOperations.get(operationId);
    if (!operation) {
      return res.status(404).json({ error: "Operation not found" });
    }

    operation.priority = priority;
    operation.lastUpdated = new Date().toISOString();

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "operation-priority-changed",
        data: {
          id: operationId,
          priority,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`⚖️ Operation priority changed: ${operationId} -> ${priority}`);

    res.json({
      success: true,
      message: "Operation priority updated successfully",
      operationId,
      priority,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Operation priority error:", error);
    res.status(500).json({ error: "Failed to update operation priority" });
  }
};

// Simulate operation progress (replace with actual file operations in production)
function simulateOperation(operationId: string, wsManager: any) {
  const operation = activeOperations.get(operationId);
  if (!operation) return;

  operation.status = "running";
  let progress = 0;
  const totalFiles =
    operation.options.totalFiles || Math.floor(Math.random() * 50) + 10;
  operation.files.total = totalFiles;

  const progressInterval = setInterval(
    () => {
      if (operation.status !== "running") {
        clearInterval(progressInterval);
        return;
      }

      progress += Math.floor(Math.random() * 15) + 5;
      if (progress > 100) progress = 100;

      const processedFiles = Math.floor((progress / 100) * totalFiles);
      operation.progress = progress;
      operation.files.processed = processedFiles;
      operation.currentFile = `file_${processedFiles}.txt`;

      // Broadcast progress update
      wsManager.broadcast({
        type: "progress",
        data: {
          id: operationId,
          title: `${operation.type} Operation`,
          type: operation.type,
          progress,
          status: "running",
          startTime: operation.startTime,
          currentFile: operation.currentFile,
          totalFiles,
          processedFiles,
        },
      });

      if (progress >= 100) {
        operation.status = "completed";
        operation.completedAt = new Date().toISOString();

        wsManager.broadcast({
          type: "operation-completed",
          data: {
            id: operationId,
            status: "completed",
            completedAt: operation.completedAt,
            files: operation.files,
          },
        });

        clearInterval(progressInterval);
        console.log(`✅ Operation completed: ${operationId}`);
      }
    },
    2000 + Math.random() * 3000,
  ); // Random interval between 2-5 seconds
}
