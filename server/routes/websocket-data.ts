import { Request<PERSON>and<PERSON> } from "express";
import { z } from "zod";

// Validation schemas
const ProgressUpdateSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.enum(["sync", "upload", "download"]),
  progress: z.number().min(0).max(100),
  status: z.enum(["running", "completed", "failed", "paused"]),
  startTime: z.string().datetime().optional(),
  estimatedCompletion: z.string().datetime().optional(),
  currentFile: z.string().optional(),
  totalFiles: z.number().optional(),
  processedFiles: z.number().optional(),
});

const EventUpdateSchema = z.object({
  id: z.string(),
  title: z.string(),
  message: z.string(),
  type: z.enum(["success", "error", "warning", "info"]),
  category: z.enum(["sync", "upload", "download", "system", "auth"]),
  timestamp: z.string().datetime().optional(),
  clientPath: z.string().optional(),
  details: z.string().optional(),
});

// Store WebSocket manager reference
let wsManager: any = null;

export function setWebSocketManager(manager: any) {
  console.log("🔗 WebSocket manager initialized:", !!manager);
  wsManager = manager;
}

export function getWebSocketManager() {
  return wsManager;
}

export const handleProgressUpdate: RequestHandler = (req, res) => {
  try {
    const progressData = ProgressUpdateSchema.parse(req.body);

    // Convert date strings to Date objects
    const processedData = {
      ...progressData,
      startTime: progressData.startTime
        ? new Date(progressData.startTime)
        : new Date(),
      estimatedCompletion: progressData.estimatedCompletion
        ? new Date(progressData.estimatedCompletion)
        : undefined,
    };

    // Broadcast to all connected WebSocket clients
    if (wsManager) {
      wsManager.broadcast({
        type: "progress",
        data: processedData,
      });

      console.log(
        `📊 Progress update broadcasted: ${progressData.title} - ${progressData.progress}%`,
      );
      res.json({ success: true, message: "Progress update broadcasted" });
    } else {
      console.warn("⚠️ WebSocket manager not initialized");
      res.status(503).json({ error: "WebSocket service not available" });
    }
  } catch (error) {
    console.error("❌ Invalid progress data:", error);
    res.status(400).json({
      error: "Invalid progress data",
      details: error instanceof z.ZodError ? error.errors : error,
    });
  }
};

export const handleEventUpdate: RequestHandler = (req, res) => {
  try {
    const eventData = EventUpdateSchema.parse(req.body);

    // Convert date string to Date object
    const processedData = {
      ...eventData,
      timestamp: eventData.timestamp
        ? new Date(eventData.timestamp)
        : new Date(),
    };

    // Broadcast to all connected WebSocket clients
    if (wsManager) {
      wsManager.broadcast({
        type: "event",
        data: processedData,
      });

      console.log(
        `📋 Event broadcasted: ${eventData.title} - ${eventData.type}`,
      );
      res.json({ success: true, message: "Event broadcasted" });
    } else {
      console.warn("⚠️ WebSocket manager not initialized");
      res.status(503).json({ error: "WebSocket service not available" });
    }
  } catch (error) {
    console.error("❌ Invalid event data:", error);
    res.status(400).json({
      error: "Invalid event data",
      details: error instanceof z.ZodError ? error.errors : error,
    });
  }
};

export const handleBulkUpdate: RequestHandler = (req, res) => {
  try {
    const { progress = [], events = [] } = req.body;

    let successCount = 0;
    let errorCount = 0;
    const errors: any[] = [];

    // Process progress updates
    progress.forEach((item: any, index: number) => {
      try {
        const progressData = ProgressUpdateSchema.parse(item);
        const processedData = {
          ...progressData,
          startTime: progressData.startTime
            ? new Date(progressData.startTime)
            : new Date(),
          estimatedCompletion: progressData.estimatedCompletion
            ? new Date(progressData.estimatedCompletion)
            : undefined,
        };

        if (wsManager) {
          wsManager.broadcast({
            type: "progress",
            data: processedData,
          });
          successCount++;
        }
      } catch (error) {
        errorCount++;
        errors.push({
          type: "progress",
          index,
          error: error instanceof z.ZodError ? error.errors : error,
        });
      }
    });

    // Process event updates
    events.forEach((item: any, index: number) => {
      try {
        const eventData = EventUpdateSchema.parse(item);
        const processedData = {
          ...eventData,
          timestamp: eventData.timestamp
            ? new Date(eventData.timestamp)
            : new Date(),
        };

        if (wsManager) {
          wsManager.broadcast({
            type: "event",
            data: processedData,
          });
          successCount++;
        }
      } catch (error) {
        errorCount++;
        errors.push({
          type: "event",
          index,
          error: error instanceof z.ZodError ? error.errors : error,
        });
      }
    });

    console.log(
      `📦 Bulk update: ${successCount} successful, ${errorCount} errors`,
    );

    res.json({
      success: errorCount === 0,
      processed: successCount,
      errors: errorCount,
      errorDetails: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("❌ Bulk update error:", error);
    res.status(400).json({ error: "Invalid bulk update data" });
  }
};

// Health check endpoint for external apps
export const handleWebSocketHealth: RequestHandler = (_req, res) => {
  try {
    const isDevelopment = process.env.NODE_ENV === "development";
    const wsManagerExists = !!wsManager;

    let connectedClients = 0;
    let isHealthy = false;

    if (wsManagerExists) {
      try {
        connectedClients = wsManager.getConnectedClientsCount();
        isHealthy = true;
        console.log(
          "✅ WebSocket manager is healthy, connected clients:",
          connectedClients,
        );
      } catch (error) {
        console.error("❌ Error getting connected clients count:", error);
        isHealthy = false;
      }
    } else {
      console.warn("⚠️ WebSocket manager not available for health check");
    }

    const response = {
      status: isHealthy ? "healthy" : "unhealthy",
      environment: isDevelopment ? "development" : "production",
      connectedClients,
      wsManagerAvailable: wsManagerExists,
      timestamp: new Date().toISOString(),
      details: wsManagerExists
        ? "WebSocket manager available"
        : "WebSocket manager not initialized",
    };

    res.json(response);
  } catch (error) {
    console.error("💀 Fatal error in WebSocket health check:", error);
    res.status(500).json({
      status: "error",
      environment:
        process.env.NODE_ENV === "development" ? "development" : "production",
      connectedClients: 0,
      wsManagerAvailable: false,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
};
