import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { 
  ProcessInfo, 
  ProcessCreateRequest, 
  ProcessUpdateRequest, 
  ProcessListResponse, 
  ProcessStatsResponse 
} from "@shared/api";

// In-memory storage for processes (in production, use a database)
const processes: Map<string, ProcessInfo> = new Map();

// Generate unique ID
function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

// GET /api/processes - List all processes
export const listProcesses: RequestHandler = (req, res) => {
  const status = req.query.status as string;
  const limit = parseInt(req.query.limit as string) || 100;
  const offset = parseInt(req.query.offset as string) || 0;

  let processArray = Array.from(processes.values());
  
  if (status) {
    processArray = processArray.filter(p => p.status === status);
  }

  const total = processArray.length;
  const paginatedProcesses = processArray.slice(offset, offset + limit);

  const response: ProcessListResponse = {
    processes: paginatedProcesses,
    total
  };

  res.json(response);
};

// POST /api/processes - Create new process
export const createProcess: RequestHandler = (req, res) => {
  const request = req.body as ProcessCreateRequest;
  
  if (!request.name) {
    return res.status(400).json({ error: "Process name is required" });
  }

  const id = generateId();
  const process: ProcessInfo = {
    id,
    name: request.name,
    pid: request.pid,
    status: 'starting',
    startTime: new Date().toISOString(),
    metadata: request.metadata || {},
    logs: []
  };

  processes.set(id, process);
  
  res.status(201).json(process);
};

// GET /api/processes/:id - Get specific process
export const getProcess: RequestHandler = (req, res) => {
  const { id } = req.params;
  const process = processes.get(id);

  if (!process) {
    return res.status(404).json({ error: "Process not found" });
  }

  res.json(process);
};

// PUT /api/processes/:id - Update process
export const updateProcess: RequestHandler = (req, res) => {
  const { id } = req.params;
  const updates = req.body as ProcessUpdateRequest;
  const process = processes.get(id);

  if (!process) {
    return res.status(404).json({ error: "Process not found" });
  }

  // Update process fields
  if (updates.status !== undefined) {
    process.status = updates.status;
    
    if (updates.status === 'stopped' && !process.endTime) {
      process.endTime = new Date().toISOString();
      if (process.startTime) {
        process.duration = new Date().getTime() - new Date(process.startTime).getTime();
      }
    }
  }
  
  if (updates.cpuUsage !== undefined) process.cpuUsage = updates.cpuUsage;
  if (updates.memoryUsage !== undefined) process.memoryUsage = updates.memoryUsage;
  if (updates.metadata) {
    process.metadata = { ...process.metadata, ...updates.metadata };
  }
  if (updates.logs) {
    process.logs = [...(process.logs || []), ...updates.logs];
    // Keep only last 100 log entries
    if (process.logs.length > 100) {
      process.logs = process.logs.slice(-100);
    }
  }

  processes.set(id, process);
  res.json(process);
};

// DELETE /api/processes/:id - Delete process
export const deleteProcess: RequestHandler = (req, res) => {
  const { id } = req.params;
  
  if (!processes.has(id)) {
    return res.status(404).json({ error: "Process not found" });
  }

  processes.delete(id);
  res.status(204).send();
};

// GET /api/processes/stats - Get process statistics
export const getProcessStats: RequestHandler = (req, res) => {
  const processArray = Array.from(processes.values());
  
  const stats: ProcessStatsResponse = {
    totalProcesses: processArray.length,
    runningProcesses: processArray.filter(p => p.status === 'running').length,
    stoppedProcesses: processArray.filter(p => p.status === 'stopped').length,
    errorProcesses: processArray.filter(p => p.status === 'error').length,
    averageCpuUsage: processArray.reduce((sum, p) => sum + (p.cpuUsage || 0), 0) / processArray.length || 0,
    averageMemoryUsage: processArray.reduce((sum, p) => sum + (p.memoryUsage || 0), 0) / processArray.length || 0
  };

  res.json(stats);
};

// POST /api/processes/:id/logs - Add log entries to process
export const addProcessLogs: RequestHandler = (req, res) => {
  const { id } = req.params;
  const { logs } = req.body as { logs: string[] };
  const process = processes.get(id);

  if (!process) {
    return res.status(404).json({ error: "Process not found" });
  }

  if (!Array.isArray(logs)) {
    return res.status(400).json({ error: "Logs must be an array of strings" });
  }

  process.logs = [...(process.logs || []), ...logs];
  
  // Keep only last 100 log entries
  if (process.logs.length > 100) {
    process.logs = process.logs.slice(-100);
  }

  processes.set(id, process);
  res.json({ message: "Logs added successfully", logCount: logs.length });
};
