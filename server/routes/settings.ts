import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWebSocketManager } from "./websocket-data";

// Settings update handler
export const handleSettingsUpdate: RequestHandler = (req, res) => {
  try {
    const { category, settings } = req.body;

    if (!category || !settings) {
      return res.status(400).json({
        error: "Missing required fields: category and settings",
      });
    }

    // Broadcast settings update via WebSocket
    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "settings-changed",
        data: {
          id: `settings-${Date.now()}`,
          category,
          settings,
          timestamp: new Date().toISOString(),
          action: "update",
        },
      });
    }

    console.log(`⚙️ Settings updated - Category: ${category}`);

    res.json({
      success: true,
      message: "Settings updated successfully",
      category,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Settings update error:", error);
    res.status(500).json({ error: "Failed to update settings" });
  }
};

// Theme settings handler
export const handleThemeSettings: RequestHandler = (req, res) => {
  try {
    const { theme, accentColor, customCSS } = req.body;

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "theme-changed",
        data: {
          id: `theme-${Date.now()}`,
          theme,
          accentColor,
          customCSS,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`🎨 Theme updated: ${theme}`);

    res.json({
      success: true,
      message: "Theme settings updated successfully",
      theme,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Theme settings error:", error);
    res.status(500).json({ error: "Failed to update theme settings" });
  }
};

// Notification settings handler
export const handleNotificationSettings: RequestHandler = (req, res) => {
  try {
    const { category = "notifications", settings } = req.body;

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "notification-settings-changed",
        data: {
          id: `notifications-${Date.now()}`,
          category,
          settings,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`🔔 Notification settings updated`);

    res.json({
      success: true,
      message: "Notification settings updated successfully",
      settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Notification settings error:", error);
    res.status(500).json({ error: "Failed to update notification settings" });
  }
};

// Performance settings handler
export const handlePerformanceSettings: RequestHandler = (req, res) => {
  try {
    const { category = "performance", settings } = req.body;

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "performance-settings-changed",
        data: {
          id: `performance-${Date.now()}`,
          category,
          settings,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`⚡ Performance settings updated`);

    res.json({
      success: true,
      message: "Performance settings updated successfully",
      settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Performance settings error:", error);
    res.status(500).json({ error: "Failed to update performance settings" });
  }
};

// Security settings handler
export const handleSecuritySettings: RequestHandler = (req, res) => {
  try {
    const { category = "security", settings } = req.body;

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "security-settings-changed",
        data: {
          id: `security-${Date.now()}`,
          category,
          settings,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`🔒 Security settings updated`);

    res.json({
      success: true,
      message: "Security settings updated successfully",
      settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Security settings error:", error);
    res.status(500).json({ error: "Failed to update security settings" });
  }
};

// API configuration handler
export const handleApiSettings: RequestHandler = (req, res) => {
  try {
    const { rateLimit, enableCors, allowedOrigins } = req.body;

    const wsManager = getWebSocketManager();
    if (wsManager) {
      wsManager.broadcast({
        type: "api-settings-changed",
        data: {
          id: `api-${Date.now()}`,
          rateLimit,
          enableCors,
          allowedOrigins,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`🔧 API settings updated`);

    res.json({
      success: true,
      message: "API settings updated successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("API settings error:", error);
    res.status(500).json({ error: "Failed to update API settings" });
  }
};
