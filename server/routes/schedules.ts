import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { z } from "zod";

// Validation schema for schedule creation
const CreateScheduleSchema = z.object({
  name: z.string().min(1, "Schedule name is required"),
  sourceFolder: z.string().min(1, "Source folder is required"),
  destinationFolder: z.string().min(1, "Destination folder is required"),
  clientId: z.string().min(1, "Client ID is required"),
  interval: z.enum(["daily", "weekly", "monthly"]),
  time: z
    .string()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  dayOfWeek: z.number().min(0).max(6).optional(),
  dayOfMonth: z.number().min(1).max(28).optional(),
  isActive: z.boolean(),
  exclusions: z.array(z.string()),
  description: z.string().optional(),
});

export type CreateScheduleData = z.infer<typeof CreateScheduleSchema>;

interface Schedule extends CreateScheduleData {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  lastSync?: Date;
  nextSync: Date;
  status: "pending" | "syncing" | "completed" | "failed" | "paused";
  progress?: number;
  error?: string;
  syncHistory: Array<{
    startTime: Date;
    endTime?: Date;
    status: "success" | "error";
    filesSynced: number;
    filesProcessed: number;
    error?: string;
  }>;
}

// In-memory storage for schedules (in production, this would be a database)
let schedules: Schedule[] = [];

// Helper function to calculate next sync time
function calculateNextSync(schedule: CreateScheduleData): Date {
  const now = new Date();
  const [hours, minutes] = schedule.time.split(":").map(Number);

  let nextSync = new Date();
  nextSync.setHours(hours, minutes, 0, 0);

  // If the time has already passed today, move to next occurrence
  if (nextSync <= now) {
    switch (schedule.interval) {
      case "daily":
        nextSync.setDate(nextSync.getDate() + 1);
        break;
      case "weekly":
        nextSync.setDate(nextSync.getDate() + 7);
        break;
      case "monthly":
        nextSync.setMonth(nextSync.getMonth() + 1);
        break;
    }
  }

  // Adjust for weekly schedules
  if (schedule.interval === "weekly" && schedule.dayOfWeek !== undefined) {
    const daysDiff = (schedule.dayOfWeek - nextSync.getDay() + 7) % 7;
    if (daysDiff === 0 && nextSync <= now) {
      nextSync.setDate(nextSync.getDate() + 7);
    } else {
      nextSync.setDate(nextSync.getDate() + daysDiff);
    }
  }

  // Adjust for monthly schedules
  if (schedule.interval === "monthly" && schedule.dayOfMonth !== undefined) {
    nextSync.setDate(schedule.dayOfMonth);
    if (nextSync <= now) {
      nextSync.setMonth(nextSync.getMonth() + 1);
      nextSync.setDate(schedule.dayOfMonth);
    }
  }

  return nextSync;
}

// Create a new schedule
export const createSchedule: RequestHandler = (req, res) => {
  try {
    const validatedData = CreateScheduleSchema.parse(req.body);

    // Validate interval-specific requirements
    if (
      validatedData.interval === "weekly" &&
      validatedData.dayOfWeek === undefined
    ) {
      return res.status(400).json({
        error: "dayOfWeek is required for weekly schedules",
      });
    }

    if (
      validatedData.interval === "monthly" &&
      validatedData.dayOfMonth === undefined
    ) {
      return res.status(400).json({
        error: "dayOfMonth is required for monthly schedules",
      });
    }

    const schedule: Schedule = {
      ...validatedData,
      id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      nextSync: calculateNextSync(validatedData),
      status: validatedData.isActive ? "pending" : "paused",
      syncHistory: [],
    };

    schedules.push(schedule);

    console.log(`Created new schedule: ${schedule.name} (${schedule.id})`);

    res.status(201).json({
      message: "Schedule created successfully",
      schedule,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Validation error",
        details: error.errors,
      });
    }

    console.error("Error creating schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Get all schedules
export const getSchedules: RequestHandler = (req, res) => {
  try {
    res.json({
      schedules: schedules.sort(
        (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
      ),
      total: schedules.length,
    });
  } catch (error) {
    console.error("Error fetching schedules:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Get a specific schedule
export const getSchedule: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;
    const schedule = schedules.find((s) => s.id === id);

    if (!schedule) {
      return res.status(404).json({ error: "Schedule not found" });
    }

    res.json({ schedule });
  } catch (error) {
    console.error("Error fetching schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Update a schedule
export const updateSchedule: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);

    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }

    const validatedData = CreateScheduleSchema.partial().parse(req.body);
    const currentSchedule = schedules[scheduleIndex];

    const updatedSchedule: Schedule = {
      ...currentSchedule,
      ...validatedData,
      updatedAt: new Date(),
    };

    // Recalculate next sync if timing data changed
    if (
      validatedData.interval ||
      validatedData.time ||
      validatedData.dayOfWeek ||
      validatedData.dayOfMonth
    ) {
      updatedSchedule.nextSync = calculateNextSync(updatedSchedule);
    }

    schedules[scheduleIndex] = updatedSchedule;

    console.log(
      `Updated schedule: ${updatedSchedule.name} (${updatedSchedule.id})`,
    );

    res.json({
      message: "Schedule updated successfully",
      schedule: updatedSchedule,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Validation error",
        details: error.errors,
      });
    }

    console.error("Error updating schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Delete a schedule
export const deleteSchedule: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);

    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }

    const deletedSchedule = schedules.splice(scheduleIndex, 1)[0];

    console.log(
      `Deleted schedule: ${deletedSchedule.name} (${deletedSchedule.id})`,
    );

    res.json({
      message: "Schedule deleted successfully",
      schedule: deletedSchedule,
    });
  } catch (error) {
    console.error("Error deleting schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Toggle schedule status (pause/resume)
export const toggleSchedule: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);

    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }

    const schedule = schedules[scheduleIndex];
    schedule.isActive = !schedule.isActive;
    schedule.status = schedule.isActive ? "pending" : "paused";
    schedule.updatedAt = new Date();

    if (schedule.isActive) {
      schedule.nextSync = calculateNextSync(schedule);
    }

    console.log(
      `Toggled schedule: ${schedule.name} (${schedule.id}) - ${schedule.isActive ? "Active" : "Paused"}`,
    );

    res.json({
      message: `Schedule ${schedule.isActive ? "resumed" : "paused"} successfully`,
      schedule,
    });
  } catch (error) {
    console.error("Error toggling schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Execute a schedule immediately
export const executeSchedule: RequestHandler = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);

    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }

    const schedule = schedules[scheduleIndex];

    if (schedule.status === "syncing") {
      return res.status(400).json({ error: "Schedule is already running" });
    }

    // Update schedule status
    schedule.status = "syncing";
    schedule.progress = 0;
    schedule.updatedAt = new Date();
    schedule.lastSync = new Date();

    console.log(
      `Started immediate execution of schedule: ${schedule.name} (${schedule.id})`,
    );

    // Simulate sync process (in production, this would trigger actual sync)
    setTimeout(() => {
      if (schedules[scheduleIndex]) {
        schedules[scheduleIndex].status = "completed";
        schedules[scheduleIndex].progress = 100;
        schedules[scheduleIndex].nextSync = calculateNextSync(schedule);
        schedules[scheduleIndex].syncHistory.unshift({
          startTime: schedule.lastSync!,
          endTime: new Date(),
          status: "success",
          filesSynced: Math.floor(Math.random() * 100) + 50,
          filesProcessed: Math.floor(Math.random() * 120) + 80,
        });
      }
    }, 5000);

    res.json({
      message: "Schedule execution started",
      schedule,
    });
  } catch (error) {
    console.error("Error executing schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
