import path from "path";
import express from "express";
import { createServer } from "./index";
import { WebSocketManager } from "./websocket";

const httpServer = createServer();
const port = process.env.PORT || 3000;

// Initialize WebSocket
new WebSocketManager(httpServer);

// In production, serve the built SPA files
const __dirname = import.meta.dirname;
const distPath = path.join(__dirname, "../spa");

// Create Express app for static file serving
const app = express();

// Serve static files
app.use(express.static(distPath));

// Handle React Router - serve index.html for all non-API routes
app.get("*", (req, res) => {
  // Don't serve index.html for API routes or WebSocket
  if (req.path.startsWith("/api/") || req.path.startsWith("/health") || req.path.startsWith("/ws")) {
    return res.status(404).json({ error: "Endpoint not found" });
  }

  res.sendFile(path.join(distPath, "index.html"));
});

// Add the static file serving to the HTTP server
httpServer.on('request', (req, res) => {
  // First check if it's handled by the main server (API routes)
  // If not, pass to the static file server
  const originalListeners = httpServer.listeners('request');
  let handled = false;

  // Try the main server first
  for (const listener of originalListeners) {
    if (listener !== arguments.callee) {
      try {
        (listener as any)(req, res);
        if (res.headersSent) {
          handled = true;
          break;
        }
      } catch (e) {
        // Continue to next listener
      }
    }
  }

  // If not handled by main server, try static files
  if (!handled && !res.headersSent) {
    app(req, res);
  }
});

httpServer.listen(port, () => {
  console.log(`🚀 Fusion Starter server running on port ${port}`);
  console.log(`📱 Frontend: http://localhost:${port}`);
  console.log(`🔧 API: http://localhost:${port}/api`);
  console.log(`🔌 WebSocket: ws://localhost:${port}/ws`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("🛑 Received SIGTERM, shutting down gracefully");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("🛑 Received SIGINT, shutting down gracefully");
  process.exit(0);
});
