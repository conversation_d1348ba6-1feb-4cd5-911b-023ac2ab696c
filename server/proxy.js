const http = require('http');
const httpProxy = require('http-proxy');
const fs = require('fs');
const path = require('path');

class W8FileProxy {
    constructor() {
        this.targetPort = process.env.VITE_PORT || 5173;
        this.proxyPort = process.env.PROXY_PORT || 80;
        this.proxy = httpProxy.createProxyServer({
            target: `http://localhost:${this.targetPort}`,
            changeOrigin: true,
            ws: true, // Enable WebSocket proxying
        });
        
        this.server = null;
        this.isRunning = false;
    }

    start() {
        if (this.isRunning) {
            console.log('🔄 Proxy server is already running');
            return;
        }

        this.server = http.createServer((req, res) => {
            // Log requests for debugging
            console.log(`🌐 Proxying: ${req.method} ${req.url} -> localhost:${this.targetPort}`);
            
            // Set CORS headers for development
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            // Proxy the request
            this.proxy.web(req, res, {}, (err) => {
                if (err) {
                    console.error('❌ Proxy error:', err.message);
                    res.writeHead(502, {'Content-Type': 'text/plain'});
                    res.end('Bad Gateway: Unable to connect to the application server');
                }
            });
        });

        // Handle WebSocket upgrades
        this.server.on('upgrade', (req, socket, head) => {
            console.log('🔄 WebSocket upgrade request proxied');
            this.proxy.ws(req, socket, head, {}, (err) => {
                if (err) {
                    console.error('❌ WebSocket proxy error:', err.message);
                    socket.destroy();
                }
            });
        });

        // Error handling
        this.proxy.on('error', (err, req, res) => {
            console.error('❌ Proxy server error:', err.message);
            if (res && !res.headersSent) {
                res.writeHead(500, {'Content-Type': 'text/plain'});
                res.end('Internal Server Error');
            }
        });

        // Start listening
        this.server.listen(this.proxyPort, '0.0.0.0', (err) => {
            if (err) {
                console.error('❌ Failed to start proxy server:', err.message);
                return;
            }
            
            this.isRunning = true;
            console.log('🚀 W8File Reverse Proxy Server started');
            console.log(`📡 Listening on: http://W8File.Importer.Local (port ${this.proxyPort})`);
            console.log(`🎯 Proxying to: http://localhost:${this.targetPort}`);
            console.log('🔌 WebSocket support: Enabled');
        });

        // Graceful shutdown
        process.on('SIGINT', () => this.stop());
        process.on('SIGTERM', () => this.stop());
    }

    stop() {
        if (!this.isRunning) {
            return;
        }

        console.log('🛑 Shutting down proxy server...');
        
        if (this.server) {
            this.server.close(() => {
                console.log('✅ Proxy server stopped');
                this.isRunning = false;
            });
        }

        if (this.proxy) {
            this.proxy.close();
        }
    }

    getStatus() {
        return {
            running: this.isRunning,
            proxyPort: this.proxyPort,
            targetPort: this.targetPort,
            url: `http://W8File.Importer.Local`
        };
    }
}

// Export for use in other modules
module.exports = W8FileProxy;

// If run directly, start the proxy
if (require.main === module) {
    const proxy = new W8FileProxy();
    proxy.start();
}
