#!/usr/bin/env pwsh
# W8File Importer Solution Development Menu
# Comprehensive development, build, test, and deployment management

# ============================================================================
# SYSTEM REQUIREMENTS AND COMPATIBILITY CHECKS
# ============================================================================

function Test-AdminRights {
    try {
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } catch {
        return $false
    }
}

function Initialize-Environment {
    # Set UTF-8 encoding for proper emoji support
    try {
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
        $OutputEncoding = [System.Text.Encoding]::UTF8
        $Host.UI.RawUI.WindowTitle = "W8File Development Menu"
    } catch {
        Write-Warning "Could not set UTF-8 encoding or window title."
    }
}

function Show-Banner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    🚀 W8FILE IMPORTER SOLUTION                              ║" -ForegroundColor Cyan
    Write-Host "║                     Development & Deployment Menu                           ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

function Show-SystemInfo {
    $nodeVersion = try { (node --version) } catch { "Not installed" }
    $npmVersion = try { (npm --version) } catch { "Not installed" }
    $dotnetVersion = try { (dotnet --version) } catch { "Not installed" }
    $tenantId = $env:W8FILE_TENANT_ID ?? "Not set"
    $providerId = $env:W8FILE_PROVIDER_ID ?? "Not set"
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Gray
    Write-Host "│  📊 SYSTEM STATUS                     │" -ForegroundColor Gray
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Gray
    Write-Host "  🟢 Node.js: " -NoNewline -ForegroundColor Green
    Write-Host "$nodeVersion" -ForegroundColor White
    Write-Host "  📦 NPM: " -NoNewline -ForegroundColor Blue
    Write-Host "$npmVersion" -ForegroundColor White
    Write-Host "  🔷 .NET: " -NoNewline -ForegroundColor Magenta
    Write-Host "$dotnetVersion" -ForegroundColor White
    Write-Host "  🏢 Tenant ID: " -NoNewline -ForegroundColor Yellow
    Write-Host "$tenantId" -ForegroundColor White
    Write-Host "  🔑 Provider ID: " -NoNewline -ForegroundColor Yellow
    Write-Host "$providerId" -ForegroundColor White
    Write-Host ""
}

function Show-Menu {
    Clear-Host
    Show-Banner
    Show-SystemInfo
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Cyan
    Write-Host "│  🎯 DEVELOPMENT COMMANDS              │" -ForegroundColor Cyan
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "1" -NoNewline -ForegroundColor Green
    Write-Host "] 🚀 " -NoNewline -ForegroundColor Gray
    Write-Host "Start Dashboard Development" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "2" -NoNewline -ForegroundColor Green
    Write-Host "] ⚡ " -NoNewline -ForegroundColor Gray
    Write-Host "Start Electron Development" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "3" -NoNewline -ForegroundColor Green
    Write-Host "] 🔌 " -NoNewline -ForegroundColor Gray
    Write-Host "Start WebSocket Service" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "4" -NoNewline -ForegroundColor Green
    Write-Host "] 🌐 " -NoNewline -ForegroundColor Gray
    Write-Host "Start Full Stack Development" -ForegroundColor White
    Write-Host ""
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  🏗️ BUILD & TEST COMMANDS             │" -ForegroundColor Yellow
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "5" -NoNewline -ForegroundColor Green
    Write-Host "] 🧹 " -NoNewline -ForegroundColor Gray
    Write-Host "Clean All Projects" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "6" -NoNewline -ForegroundColor Green
    Write-Host "] 🏗️ " -NoNewline -ForegroundColor Gray
    Write-Host "Build All Projects" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "7" -NoNewline -ForegroundColor Green
    Write-Host "] 🧪 " -NoNewline -ForegroundColor Gray
    Write-Host "Run All Tests" -ForegroundColor White
    Write-Host ""
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Magenta
    Write-Host "│  📦 DEPLOYMENT COMMANDS               │" -ForegroundColor Magenta
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "8" -NoNewline -ForegroundColor Green
    Write-Host "] 📦 " -NoNewline -ForegroundColor Gray
    Write-Host "Publish Dashboard" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "9" -NoNewline -ForegroundColor Green
    Write-Host "] 🔧 " -NoNewline -ForegroundColor Gray
    Write-Host "Publish WebSocket Service" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "A" -NoNewline -ForegroundColor Green
    Write-Host "] 🏪 " -NoNewline -ForegroundColor Gray
    Write-Host "Create Windows Service" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "B" -NoNewline -ForegroundColor Green
    Write-Host "] 🔄 " -NoNewline -ForegroundColor Gray
    Write-Host "Full CI/CD Pipeline" -ForegroundColor White
    Write-Host ""
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Green
    Write-Host "│  🛠️ CONFIGURATION COMMANDS            │" -ForegroundColor Green
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Green
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "C" -NoNewline -ForegroundColor Green
    Write-Host "] 🔧 " -NoNewline -ForegroundColor Gray
    Write-Host "Setup Environment Variables" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "D" -NoNewline -ForegroundColor Green
    Write-Host "] 📋 " -NoNewline -ForegroundColor Gray
    Write-Host "Show Project Status" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "E" -NoNewline -ForegroundColor Green
    Write-Host "] 🔄 " -NoNewline -ForegroundColor Gray
    Write-Host "Update Configuration Files" -ForegroundColor White
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "H" -NoNewline -ForegroundColor Green
    Write-Host "] ❓ " -NoNewline -ForegroundColor Gray
    Write-Host "Show Help" -ForegroundColor White
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "0" -NoNewline -ForegroundColor Red
    Write-Host "] 🚪 " -NoNewline -ForegroundColor Gray
    Write-Host "Exit" -ForegroundColor White
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
}

# ============================================================================
# ENVIRONMENT VARIABLE MANAGEMENT
# ============================================================================

function Set-EnvironmentVariables {
    Write-Host "🔧 " -NoNewline -ForegroundColor Blue
    Write-Host "Setting up environment variables..." -ForegroundColor Yellow
    
    $tenantId = Read-Host "Enter Tenant ID (current: $($env:W8FILE_TENANT_ID))"
    if (-not $tenantId -and $env:W8FILE_TENANT_ID) {
        $tenantId = $env:W8FILE_TENANT_ID
    }
    
    $providerId = Read-Host "Enter Provider ID (current: $($env:W8FILE_PROVIDER_ID))"
    if (-not $providerId -and $env:W8FILE_PROVIDER_ID) {
        $providerId = $env:W8FILE_PROVIDER_ID
    }
    
    $environment = Read-Host "Enter Environment [Production/Development] (current: $($env:W8FILE_ENVIRONMENT ?? 'Production'))"
    if (-not $environment) {
        $environment = $env:W8FILE_ENVIRONMENT ?? "Production"
    }
    
    try {
        # Set system environment variables (requires admin)
        if (Test-AdminRights) {
            [Environment]::SetEnvironmentVariable("W8FILE_TENANT_ID", $tenantId, "Machine")
            [Environment]::SetEnvironmentVariable("W8FILE_PROVIDER_ID", $providerId, "Machine")
            [Environment]::SetEnvironmentVariable("W8FILE_ENVIRONMENT", $environment, "Machine")
            [Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", $environment, "Machine")
            Write-Host "✅ System environment variables set" -ForegroundColor Green
        } else {
            # Set user environment variables
            [Environment]::SetEnvironmentVariable("W8FILE_TENANT_ID", $tenantId, "User")
            [Environment]::SetEnvironmentVariable("W8FILE_PROVIDER_ID", $providerId, "User")
            [Environment]::SetEnvironmentVariable("W8FILE_ENVIRONMENT", $environment, "User")
            [Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", $environment, "User")
            Write-Host "✅ User environment variables set (run as Admin for system-wide)" -ForegroundColor Green
        }
        
        # Update current session
        $env:W8FILE_TENANT_ID = $tenantId
        $env:W8FILE_PROVIDER_ID = $providerId
        $env:W8FILE_ENVIRONMENT = $environment
        $env:ASPNETCORE_ENVIRONMENT = $environment
        
        Write-Host "📝 Environment variables configured:" -ForegroundColor Cyan
        Write-Host "   W8FILE_TENANT_ID: $tenantId" -ForegroundColor Gray
        Write-Host "   W8FILE_PROVIDER_ID: $providerId" -ForegroundColor Gray
        Write-Host "   W8FILE_ENVIRONMENT: $environment" -ForegroundColor Gray
        
    } catch {
        Write-Host "❌ Failed to set environment variables: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Update-ConfigurationFiles {
    Write-Host "📝 " -NoNewline -ForegroundColor Blue
    Write-Host "Updating configuration files..." -ForegroundColor Yellow
    
    # Update FileImporterService appsettings.json
    $configPath = "FileImporterService/appsettings.json"
    if (Test-Path $configPath) {
        try {
            $config = Get-Content $configPath | ConvertFrom-Json
            
            # Ensure W8FileSettings section exists
            if (-not $config.W8FileSettings) {
                $config | Add-Member -Type NoteProperty -Name "W8FileSettings" -Value @{}
            }
            
            # Configure to use environment variables
            $config.W8FileSettings.TenantId = "`${W8FILE_TENANT_ID}"
            $config.W8FileSettings.ProviderId = "`${W8FILE_PROVIDER_ID}"
            $config.W8FileSettings.Environment = "`${W8FILE_ENVIRONMENT}"
            
            # Save updated configuration
            $config | ConvertTo-Json -Depth 10 | Set-Content $configPath
            Write-Host "✅ Updated FileImporterService configuration" -ForegroundColor Green
            
        } catch {
            Write-Host "❌ Failed to update FileImporterService config: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Update Dashboard environment configuration if needed
    $dashboardEnvPath = "W8File.Importer/.env.production"
    if (Test-Path "W8File.Importer") {
        try {
            $envContent = @"
VITE_W8FILE_TENANT_ID=$($env:W8FILE_TENANT_ID)
VITE_W8FILE_PROVIDER_ID=$($env:W8FILE_PROVIDER_ID)
VITE_W8FILE_ENVIRONMENT=$($env:W8FILE_ENVIRONMENT)
"@
            $envContent | Set-Content $dashboardEnvPath
            Write-Host "✅ Updated Dashboard environment configuration" -ForegroundColor Green
            
        } catch {
            Write-Host "❌ Failed to update Dashboard config: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Initialize environment
Initialize-Environment

# Check for admin rights
$isAdmin = Test-AdminRights
if (-not $isAdmin) {
    Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
    Write-Host "Running without Administrator privileges. Some features may be limited." -ForegroundColor Yellow
    Write-Host ""
}
