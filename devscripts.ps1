#!/usr/bin/env pwsh
# W8File Electron Development Menu
# Interactive TUI for managing development tasks

# ============================================================================
# SYSTEM REQUIREMENTS AND COMPATIBILITY CHECKS
# ============================================================================

function Test-AdminRights {
    try {
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } catch {
        return $false
    }
}

function Test-PowerShellVersion {
    return $PSVersionTable.PSVersion.Major -ge 7
}

function Test-TerminalCompatibility {
    # Check if running in Windows Terminal, PowerShell ISE, or compatible terminal
    $compatibleTerminals = @(
        "WindowsTerminal",
        "Microsoft.WindowsTerminal",
        "pwsh",
        "powershell"
    )
    
    $currentProcess = Get-Process -Id $PID
    $parentProcess = try { Get-Process -Id $currentProcess.Parent.Id -ErrorAction Stop } catch { $null }
    
    if ($env:WT_SESSION -or $env:TERM_PROGRAM -eq "vscode") {
        return $true
    }
    
    if ($parentProcess) {
        return $compatibleTerminals -contains $parentProcess.ProcessName
    }
    
    return $false
}

function Show-CompatibilityError {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Red
    Write-Host "║  ⚠️  COMPATIBILITY ISSUE DETECTED                                          ║" -ForegroundColor Red
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Red
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "This script requires PowerShell 7+ and a compatible terminal." -ForegroundColor White
    Write-Host ""
    Write-Host "📋 " -NoNewline -ForegroundColor Blue
    Write-Host "REQUIREMENTS:" -ForegroundColor White
    Write-Host "   • PowerShell 7+ (you have: $($PSVersionTable.PSVersion))" -ForegroundColor Gray
    Write-Host "   • Windows Terminal or compatible terminal with UTF-8 support" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💾 " -NoNewline -ForegroundColor Green
    Write-Host "DOWNLOAD LINKS:" -ForegroundColor White
    Write-Host "   • PowerShell 7+: " -NoNewline -ForegroundColor Gray
    Write-Host "https://github.com/PowerShell/PowerShell/releases" -ForegroundColor Cyan
    Write-Host "   • Windows Terminal: " -NoNewline -ForegroundColor Gray
    Write-Host "https://aka.ms/terminal" -ForegroundColor Cyan
    Write-Host "   • VS Code (alternative): " -NoNewline -ForegroundColor Gray
    Write-Host "https://code.visualstudio.com/" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "TIP: Install Windows Terminal for the best experience!" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

function Request-AdminElevation {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Yellow
    Write-Host "║  🔐 ADMINISTRATOR PRIVILEGES REQUIRED                                      ║" -ForegroundColor Yellow
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
    Write-Host "Some features require administrator privileges:" -ForegroundColor White
    Write-Host "   • Modifying hosts file for W8File.Importer.Local" -ForegroundColor Gray
    Write-Host "   • Running reverse proxy on port 80" -ForegroundColor Gray
    Write-Host "   • Installing system-level dependencies" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🚀 " -NoNewline -ForegroundColor Green
    Write-Host "Restarting as Administrator..." -ForegroundColor White
    Write-Host ""
    
    try {
        Start-Process pwsh -ArgumentList "-File `"$PSCommandPath`"" -Verb RunAs
        exit 0
    } catch {
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Failed to restart as Administrator. Please run manually." -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 " -NoNewline -ForegroundColor Yellow
        Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
        Read-Host "Press Enter to continue without admin privileges"
    }
}

function Initialize-Environment {
    # Set UTF-8 encoding for proper emoji support
    try {
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
        $OutputEncoding = [System.Text.Encoding]::UTF8
        
        # Verify UTF-8 encoding is working
        $testEmoji = "⚡🚀🔧"
        $encodingWorks = $true
        
        # Test if emojis render correctly (basic check)
        if ([Console]::OutputEncoding.EncodingName -notlike "*UTF*") {
            $encodingWorks = $false
        }
        
        if (-not $encodingWorks) {
            Write-Warning "UTF-8 encoding may not be fully supported in this terminal."
            Write-Host "Consider using Windows Terminal for the best experience."
        }
    } catch {
        Write-Warning "Could not set UTF-8 encoding. Some characters may not display correctly."
    }
    
    # Set window title
    try {
        $Host.UI.RawUI.WindowTitle = "W8File Development Menu"
    } catch {
        # Ignore if not supported
    }
}

# ============================================================================
# SYSTEM CHECKS AND INITIALIZATION
# ============================================================================

# Check PowerShell version and terminal compatibility
if (-not (Test-PowerShellVersion) -or -not (Test-TerminalCompatibility)) {
    Show-CompatibilityError
}

# Initialize environment (encoding, etc.)
Initialize-Environment

# Check for admin rights and offer to elevate
if (-not (Test-AdminRights)) {
    Request-AdminElevation
}

# If we reach here, we have admin rights or user chose to continue

function Show-Banner {
    Write-Host ""
    Write-Host "██╗    ██╗ █████╗ ███████╗██╗██╗     ███████╗" -ForegroundColor Cyan
    Write-Host "██║    ██║██╔══██╗██╔════╝██║██║     ██╔════╝" -ForegroundColor Cyan  
    Write-Host "██║ █╗ ██║╚█████╔╝█████╗  ██║██║     █████╗  " -ForegroundColor Yellow
    Write-Host "██║███╗██║██╔══██╗██╔══╝  ██║██║     ██╔══╝  " -ForegroundColor Yellow
    Write-Host "╚███╔███╔╝╚█████╔╝██║     ██║███████╗███████╗" -ForegroundColor Green
    Write-Host " ╚══╝╚══╝  ╚════╝ ╚═╝     ╚═╝╚══════╝╚══════╝" -ForegroundColor Green
    Write-Host ""
    Write-Host "██╗███╗   ███╗██████╗  ██████╗ ██████╗ ████████╗███████╗██████╗ " -ForegroundColor Magenta
    Write-Host "██║████╗ ████║██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔════╝██╔══██╗" -ForegroundColor Magenta
    Write-Host "██║██╔████╔██║██████╔╝██║   ██║██████╔╝   ██║   █████╗  ██████╔╝" -ForegroundColor Blue
    Write-Host "██║██║╚██╔╝██║██╔═══╝ ██║   ██║██╔══██╗   ██║   ██╔══╝  ██╔══██╗" -ForegroundColor Blue
    Write-Host "██║██║ ╚═╝ ██║██║     ╚██████╔╝██║  ██║   ██║   ███████╗██║  ██║" -ForegroundColor Red
    Write-Host "╚═╝╚═╝     ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═╝  ╚═╝" -ForegroundColor Red
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Yellow
    Write-Host "║  ⚡ " -NoNewline -ForegroundColor Yellow
    Write-Host "W8FILE DEVELOPMENT MENU - Custom Domain Edition" -NoNewline -ForegroundColor Cyan
    Write-Host "           ⚡ ║" -ForegroundColor Yellow
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Yellow
    Write-Host ""
}

function Show-SystemInfo {
    $nodeVersion = try { (node --version) } catch { "Not installed" }
    $npmVersion = try { (npm --version) } catch { "Not installed" }
    $electronInstalled = Test-Path "node_modules/electron"
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Gray
    Write-Host "│  📊 SYSTEM STATUS                     │" -ForegroundColor Gray
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Gray
    Write-Host "  🟢 Node.js: " -NoNewline -ForegroundColor Green
    Write-Host "$nodeVersion" -ForegroundColor White
    Write-Host "  📦 NPM: " -NoNewline -ForegroundColor Blue
    Write-Host "$npmVersion" -ForegroundColor White
    Write-Host "  ⚡ Electron: " -NoNewline -ForegroundColor Yellow
    if ($electronInstalled) {
        Write-Host "Installed" -ForegroundColor Green
    } else {
        Write-Host "Not installed" -ForegroundColor Red
    }
    Write-Host ""
}

function Show-Menu {
    Clear-Host
    Show-Banner
    Show-SystemInfo
    
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Cyan
    Write-Host "│  🎯 DEVELOPMENT COMMANDS              │" -ForegroundColor Cyan
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "1" -NoNewline -ForegroundColor Green
    Write-Host "] 🚀 " -NoNewline -ForegroundColor Gray
    Write-Host "Start Development with Proxy" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Full stack: Vite + Proxy + Electron)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "2" -NoNewline -ForegroundColor Green
    Write-Host "] 🔧 " -NoNewline -ForegroundColor Gray
    Write-Host "Start Development (Standard)" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Vite dev server + Electron)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "3" -NoNewline -ForegroundColor Green
    Write-Host "] 🌐 " -NoNewline -ForegroundColor Gray
    Write-Host "Start Vite Dev Server Only" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Web development server)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "4" -NoNewline -ForegroundColor Green
    Write-Host "] ⚡ " -NoNewline -ForegroundColor Gray
    Write-Host "Start Electron App Only" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Launch built Electron app)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  🛠️  BUILD & UTILITY COMMANDS         │" -ForegroundColor Yellow
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "5" -NoNewline -ForegroundColor Green
    Write-Host "] 🏗️  " -NoNewline -ForegroundColor Gray
    Write-Host "Build Application" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Production build)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "6" -NoNewline -ForegroundColor Green
    Write-Host "] 🧹 " -NoNewline -ForegroundColor Gray
    Write-Host "Clean Build Artifacts" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Remove dist folders)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "7" -NoNewline -ForegroundColor Green
    Write-Host "] 🔧 " -NoNewline -ForegroundColor Gray
    Write-Host "Setup Hosts File" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Configure W8File.Importer.Local)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "8" -NoNewline -ForegroundColor Green
    Write-Host "] 🔗 " -NoNewline -ForegroundColor Gray
    Write-Host "Create Reverse Proxy Mapping" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Configure custom proxy routes)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "9" -NoNewline -ForegroundColor Green
    Write-Host "] ❓ " -NoNewline -ForegroundColor Gray
    Write-Host "Show Help" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host "(Display detailed command help)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  [" -NoNewline -ForegroundColor Gray
    Write-Host "0" -NoNewline -ForegroundColor Red
    Write-Host "] 🚪 " -NoNewline -ForegroundColor Gray
    Write-Host "Exit" -ForegroundColor White
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
}

function Run-DevelopmentProxy {
    Write-Host "🚀 " -NoNewline -ForegroundColor Blue
    Write-Host "Starting Development Proxy Environment..." -ForegroundColor Yellow
    Write-Progress -Activity "Setting up development environment" -Status "Initializing components..." -PercentComplete 10
    Start-Sleep -Seconds 0.5
    Write-Progress -Activity "Setting up development environment" -Status "Starting reverse proxy..." -PercentComplete 30
    Start-Sleep -Seconds 0.5
    Write-Progress -Activity "Setting up development environment" -Status "Launching application..." -PercentComplete 60
    Start-Sleep -Seconds 0.5
.\scripts\electron.ps1 dev-proxy
    Write-Progress -Activity "Setting up development environment" -Status "Environment ready!" -PercentComplete 100 -Completed
}

function Run-StandardDevelopment {
    Write-Host "🔧 " -NoNewline -ForegroundColor Blue
    Write-Host "Starting Standard Development Environment..." -ForegroundColor Yellow
    Write-Progress -Activity "Setting up development environment" -Status "Starting Vite dev server..." -PercentComplete 30
    Start-Sleep -Seconds 0.5
    Write-Progress -Activity "Setting up development environment" -Status "Launching Electron..." -PercentComplete 60
    Start-Sleep -Seconds 0.5
.\scripts\electron.ps1 dev
    Write-Progress -Activity "Setting up development environment" -Status "Environment ready!" -PercentComplete 100 -Completed
}

function Run-ViteDevServer {
    Write-Host "🌐 " -NoNewline -ForegroundColor Blue
    Write-Host "Starting Vite Dev Server Only..." -ForegroundColor Yellow
    Write-Progress -Activity "Starting Vite" -Status "Initializing dev server..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
    npm run dev
    Write-Progress -Activity "Starting Vite" -Status "Server ready!" -PercentComplete 100 -Completed
}

function Run-ElectronApp {
    Write-Host "⚡ " -NoNewline -ForegroundColor Blue
    Write-Host "Starting Electron App Only..." -ForegroundColor Yellow
    Write-Progress -Activity "Starting Electron" -Status "Launching application..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
    npm run electron
    Write-Progress -Activity "Starting Electron" -Status "Application launched!" -PercentComplete 100 -Completed
}

function Run-BuildApplication {
    Write-Host "🏗️ " -NoNewline -ForegroundColor Blue
    Write-Host "Building Application..." -ForegroundColor Yellow
    Write-Progress -Activity "Building" -Status "Compiling application..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
    npm run build
    Write-Progress -Activity "Building" -Status "Build complete!" -PercentComplete 100 -Completed
}

function Run-CleanBuild {
    Write-Host "🧹 " -NoNewline -ForegroundColor Blue
    Write-Host "Cleaning Build Artifacts..." -ForegroundColor Yellow
    Write-Progress -Activity "Cleaning" -Status "Removing build artifacts..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
    if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
    if (Test-Path "out") { Remove-Item -Recurse -Force "out" }
    if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
    Write-Host "✅ Cleaned build artifacts successfully!" -ForegroundColor Green
    Write-Progress -Activity "Cleaning" -Status "Cleanup complete!" -PercentComplete 100 -Completed
}

function Run-HostsFileSetup {
    Write-Host "🔧 " -NoNewline -ForegroundColor Blue
    Write-Host "Setting up Hosts File..." -ForegroundColor Yellow
    Write-Progress -Activity "Hosts Setup" -Status "Configuring W8File.Importer.Local..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
.\scripts\setup-hosts.ps1
    Write-Progress -Activity "Hosts Setup" -Status "Hosts file configured!" -PercentComplete 100 -Completed
}

function Run-ReverseProxyMapping {
    Write-Host "🔗 " -NoNewline -ForegroundColor Blue
    Write-Host "Creating Reverse Proxy Mapping..." -ForegroundColor Yellow
    Write-Progress -Activity "Proxy Setup" -Status "Configuring reverse proxy routes..." -PercentComplete 0
    Start-Sleep -Seconds 0.5
    
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Cyan
    Write-Host "│  🔗 REVERSE PROXY CONFIGURATION       │" -ForegroundColor Cyan
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "This will set up the reverse proxy for W8File development:" -ForegroundColor White
    Write-Host "• Configure hosts file (W8File.Importer.Local -> 127.0.0.1)" -ForegroundColor Gray
    Write-Host "• Start reverse proxy server (port 80 → localhost:5173)" -ForegroundColor Gray
    Write-Host "• Enable custom domain access without port numbers" -ForegroundColor Gray
    Write-Host ""
    
    $confirm = Read-Host "Continue with proxy setup? (y/n)"
    if ($confirm -eq 'y' -or $confirm -eq 'Y' -or $confirm -eq 'yes') {
        Write-Progress -Activity "Proxy Setup" -Status "Running proxy configuration script..." -PercentComplete 50
.\scripts\dev-with-proxy.ps1
        Write-Progress -Activity "Proxy Setup" -Status "Proxy configuration complete!" -PercentComplete 100 -Completed
    } else {
        Write-Host "❌ Proxy setup cancelled." -ForegroundColor Yellow
        Write-Progress -Activity "Proxy Setup" -Status "Setup cancelled" -PercentComplete 100 -Completed
    }
}

function Show-Help {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║  ❓ W8FILE DEVELOPMENT HELP                                                 ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🚀 " -NoNewline -ForegroundColor Yellow
    Write-Host "Development with Proxy:" -ForegroundColor White
    Write-Host "   • Runs Vite dev server, reverse proxy, and Electron" -ForegroundColor Gray
    Write-Host "   • Access via http://W8File.Importer.Local (no port needed)" -ForegroundColor Gray
    Write-Host "   • Requires Administrator privileges for port 80" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔧 " -NoNewline -ForegroundColor Yellow
    Write-Host "Standard Development:" -ForegroundColor White
    Write-Host "   • Runs Vite dev server and Electron (traditional setup)" -ForegroundColor Gray
    Write-Host "   • Access via http://localhost:5173" -ForegroundColor Gray
    Write-Host "   • No special privileges required" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🌐 " -NoNewline -ForegroundColor Yellow
    Write-Host "Vite Dev Server Only:" -ForegroundColor White
    Write-Host "   • Runs only the web development server" -ForegroundColor Gray
    Write-Host "   • For frontend development and testing" -ForegroundColor Gray
    Write-Host ""
    Write-Host "⚡ " -NoNewline -ForegroundColor Yellow
    Write-Host "Electron App Only:" -ForegroundColor White
    Write-Host "   • Launches the Electron application" -ForegroundColor Gray
    Write-Host "   • Uses pre-built assets or dev server" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🏗️ " -NoNewline -ForegroundColor Yellow
    Write-Host "Build Application:" -ForegroundColor White
    Write-Host "   • Creates production build of the application" -ForegroundColor Gray
    Write-Host "   • Optimized and minified for distribution" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🧹 " -NoNewline -ForegroundColor Yellow
    Write-Host "Clean Build Artifacts:" -ForegroundColor White
    Write-Host "   • Removes dist, out, and build folders" -ForegroundColor Gray
    Write-Host "   • Cleans up previous build artifacts" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔧 " -NoNewline -ForegroundColor Yellow
    Write-Host "Setup Hosts File:" -ForegroundColor White
    Write-Host "   • Configures W8File.Importer.Local domain" -ForegroundColor Gray
    Write-Host "   • Maps custom domain to 127.0.0.1" -ForegroundColor Gray
    Write-Host "   • Required for proxy development mode" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💡 " -NoNewline -ForegroundColor Green
    Write-Host "Tips:" -ForegroundColor White
    Write-Host "   • Run option 7 first if using proxy mode for the first time" -ForegroundColor Yellow
    Write-Host "   • Use Windows Terminal for the best experience" -ForegroundColor Yellow
    Write-Host "   • Administrator privileges needed for proxy mode" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to return to main menu"
}

Do {
    Show-Menu
    $choice = Read-Host "Enter your choice (0-9)"
    switch ($choice) {
        "1" { Run-DevelopmentProxy }
        "2" { Run-StandardDevelopment }
        "3" { Run-ViteDevServer }
        "4" { Run-ElectronApp }
        "5" { Run-BuildApplication }
        "6" { Run-CleanBuild }
        "7" { Run-HostsFileSetup }
        "8" { Run-ReverseProxyMapping }
        "9" { Show-Help }
        "0" { Write-Host "🚪 Exiting W8File Development Menu..." -ForegroundColor Yellow; Start-Sleep -Seconds 1; break }
        Default { 
            Write-Host "❌ " -NoNewline -ForegroundColor Red
            Write-Host "Invalid choice. Please select a valid option (0-9)." -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
    if ($choice -ne "0" -and $choice -ne "9") {
        Write-Host ""
        Write-Host "Press Enter to return to menu..." -ForegroundColor Gray
        Read-Host
    }
} While ($choice -ne "0")
