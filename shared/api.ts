/**
 * Shared code between client and server
 * Useful to share types between client and server
 * and/or small pure JS functions that can be used on both client and server
 */

/**
 * Example response type for /api/demo
 */
export interface DemoResponse {
  message: string;
}

// Process Tracking API Types
export interface ProcessInfo {
  id: string;
  name: string;
  pid?: number;
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  startTime?: string;
  endTime?: string;
  duration?: number;
  cpuUsage?: number;
  memoryUsage?: number;
  metadata?: Record<string, any>;
  logs?: string[];
}

export interface ProcessCreateRequest {
  name: string;
  pid?: number;
  metadata?: Record<string, any>;
}

export interface ProcessUpdateRequest {
  status?: ProcessInfo['status'];
  cpuUsage?: number;
  memoryUsage?: number;
  metadata?: Record<string, any>;
  logs?: string[];
}

export interface ProcessListResponse {
  processes: ProcessInfo[];
  total: number;
}

export interface ProcessStatsResponse {
  totalProcesses: number;
  runningProcesses: number;
  stoppedProcesses: number;
  errorProcesses: number;
  averageCpuUsage: number;
  averageMemoryUsage: number;
}
