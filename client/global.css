@import url("https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Space+Grotesk:wght@300..700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Enhanced Desktop Application Theme System
   * Professional typography hierarchy with sophisticated themes and modern design tokens
   */

  /* Typography Scale & Font System */
  :root {
    /* Font Families */
    --font-sans:
      "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
      sans-serif;
    --font-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;
    --font-display: "Space Grotesk", "Inter", sans-serif;
    --font-serif: "Playfair Display", Georgia, serif;

    /* Typography Scale - Perfect Fourth (1.333) */
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.333rem; /* 21.33px */
    --text-2xl: 1.777rem; /* 28.43px */
    --text-3xl: 2.369rem; /* 37.90px */
    --text-4xl: 3.157rem; /* 50.51px */
    --text-5xl: 4.209rem; /* 67.34px */
    --text-6xl: 5.61rem; /* 89.76px */

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Letter Spacing */
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;

    /* Font Weights */
    --font-thin: 100;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
  }

  /* Base Desktop Theme - Modern Light */
  :root {
    /* Core Colors with Enhanced Depth */
    --background: 250 15% 97%;
    --background-secondary: 250 12% 94%;
    --background-tertiary: 250 10% 91%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(250, 15%, 98%) 0%,
      hsl(250, 10%, 96%) 100%
    );
    --foreground: 250 15% 8%;
    --foreground-secondary: 250 8% 35%;
    --foreground-muted: 250 5% 55%;

    /* Primary Color System */
    --primary: 217 91% 60%;
    --primary-hover: 217 91% 55%;
    --primary-light: 217 91% 70%;
    --primary-foreground: 0 0% 100%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(217, 91%, 65%) 0%,
      hsl(217, 91%, 55%) 100%
    );
    --primary-glow: 0 0 20px hsla(217, 91%, 60%, 0.3);

    /* Secondary Colors */
    --secondary: 250 8% 88%;
    --secondary-hover: 250 8% 82%;
    --secondary-foreground: 250 12% 15%;
    --secondary-gradient: linear-gradient(
      135deg,
      hsl(250, 10%, 90%) 0%,
      hsl(250, 8%, 85%) 100%
    );

    /* Surface Elements with Enhanced Depth */
    --card: 0 0% 100%;
    --card-hover: 250 20% 99%;
    --card-border: 250 8% 90%;
    --card-foreground: 250 15% 8%;
    --card-shadow: 250 5% 15%;
    --card-gradient: linear-gradient(
      135deg,
      hsla(0, 0%, 100%, 0.95) 0%,
      hsla(250, 20%, 99%, 0.9) 100%
    );

    --panel: 0 0% 100%;
    --panel-border: 250 6% 88%;
    --panel-foreground: 250 15% 8%;
    --panel-gradient: linear-gradient(
      135deg,
      hsl(0, 0%, 100%) 0%,
      hsl(250, 15%, 98%) 100%
    );

    /* Navigation & Sidebar with Gradients */
    --sidebar-background: linear-gradient(
      180deg,
      hsl(250, 12%, 97%) 0%,
      hsl(250, 8%, 95%) 100%
    );
    --sidebar-foreground: 250 10% 20%;
    --sidebar-accent: 217 91% 60%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(217, 91%, 65%) 0%,
      hsl(217, 91%, 55%) 100%
    );
    --sidebar-border: 250 8% 88%;
    --sidebar-item-hover: 250 12% 92%;
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(217, 91%, 60%) 0%,
      hsl(217, 91%, 50%) 100%
    );

    /* Input Elements */
    --input: 250 10% 96%;
    --input-border: 250 8% 85%;
    --input-foreground: 250 15% 8%;
    --input-placeholder: 250 5% 55%;
    --input-focus: 217 91% 60%;
    --input-gradient: linear-gradient(
      135deg,
      hsl(250, 12%, 97%) 0%,
      hsl(250, 8%, 95%) 100%
    );

    /* Interactive States */
    --muted: 250 8% 92%;
    --muted-foreground: 250 5% 46%;
    --accent: 217 50% 95%;
    --accent-foreground: 217 91% 60%;
    --accent-gradient: linear-gradient(
      135deg,
      hsl(217, 50%, 97%) 0%,
      hsl(217, 40%, 93%) 100%
    );

    /* Status Colors with Gradients */
    --success: 142 76% 36%;
    --success-light: 142 76% 46%;
    --success-foreground: 0 0% 100%;
    --success-gradient: linear-gradient(
      135deg,
      hsl(142, 76%, 40%) 0%,
      hsl(142, 76%, 32%) 100%
    );
    --success-glow: 0 0 15px hsla(142, 76%, 36%, 0.3);

    --warning: 38 92% 50%;
    --warning-light: 38 92% 60%;
    --warning-foreground: 0 0% 100%;
    --warning-gradient: linear-gradient(
      135deg,
      hsl(38, 92%, 55%) 0%,
      hsl(38, 92%, 45%) 100%
    );
    --warning-glow: 0 0 15px hsla(38, 92%, 50%, 0.3);

    --destructive: 0 84% 60%;
    --destructive-light: 0 84% 70%;
    --destructive-foreground: 0 0% 100%;
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 65%) 0%,
      hsl(0, 84%, 55%) 100%
    );
    --destructive-glow: 0 0 15px hsla(0, 84%, 60%, 0.3);

    /* Borders and Separators */
    --border: 250 8% 88%;
    --border-secondary: 250 6% 82%;
    --border-gradient: linear-gradient(
      90deg,
      transparent 0%,
      hsl(250, 8%, 88%) 50%,
      transparent 100%
    );
    --ring: 217 91% 60%;

    /* Enhanced Design Tokens */
    --radius: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;

    /* Advanced Shadow System */
    --shadow-sm:
      0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    --shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md:
      0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg:
      0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 30px -5px hsla(217, 91%, 60%, 0.3);
    --shadow-glow: 0 0 20px hsla(217, 91%, 60%, 0.4);

    /* Animation & Transitions */
    --transition-fast: 150ms ease-out;
    --transition-base: 200ms ease-out;
    --transition-slow: 300ms ease-out;
    --spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  /* Arctic Blue Theme - Professional Cool with Enhanced Gradients */
  .theme-arctic {
    --background: 210 25% 98%;
    --background-secondary: 210 20% 95%;
    --background-tertiary: 210 15% 92%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(210, 30%, 99%) 0%,
      hsl(210, 20%, 96%) 100%
    );
    --foreground: 210 20% 8%;

    --primary: 200 95% 45%;
    --primary-hover: 200 95% 40%;
    --primary-light: 200 95% 55%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(200, 95%, 50%) 0%,
      hsl(200, 95%, 40%) 100%
    );
    --primary-glow: 0 0 25px hsla(200, 95%, 45%, 0.4);

    --card: 210 40% 99%;
    --card-hover: 210 30% 98%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(210, 40%, 99%) 0%,
      hsl(210, 25%, 97%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(210, 30%, 98%) 0%,
      hsl(210, 20%, 96%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(200, 95%, 50%) 0%,
      hsl(200, 95%, 40%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(200, 95%, 45%) 0%,
      hsl(200, 95%, 35%) 100%
    );

    --success-gradient: linear-gradient(
      135deg,
      hsl(160, 84%, 45%) 0%,
      hsl(160, 84%, 35%) 100%
    );
    --warning-gradient: linear-gradient(
      135deg,
      hsl(45, 93%, 55%) 0%,
      hsl(45, 93%, 45%) 100%
    );
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 65%) 0%,
      hsl(0, 84%, 55%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(200, 95%, 45%, 0.3);
    --shadow-glow: 0 0 25px hsla(200, 95%, 45%, 0.4);
  }

  /* Forest Theme - Natural Green with Organic Gradients */
  .theme-forest {
    --background: 140 15% 97%;
    --background-secondary: 140 12% 94%;
    --background-tertiary: 140 10% 90%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(140, 20%, 98%) 0%,
      hsl(140, 12%, 95%) 100%
    );
    --foreground: 140 12% 10%;

    --primary: 142 71% 45%;
    --primary-hover: 142 71% 40%;
    --primary-light: 142 71% 55%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(142, 71%, 50%) 0%,
      hsl(142, 71%, 40%) 100%
    );
    --primary-glow: 0 0 25px hsla(142, 71%, 45%, 0.4);

    --card: 140 25% 99%;
    --card-hover: 140 20% 98%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(140, 25%, 99%) 0%,
      hsl(140, 15%, 97%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(140, 18%, 97%) 0%,
      hsl(140, 12%, 95%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(142, 71%, 50%) 0%,
      hsl(142, 71%, 40%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(142, 71%, 45%) 0%,
      hsl(142, 71%, 35%) 100%
    );

    --success-gradient: linear-gradient(
      135deg,
      hsl(142, 71%, 50%) 0%,
      hsl(142, 71%, 40%) 100%
    );
    --warning-gradient: linear-gradient(
      135deg,
      hsl(45, 93%, 55%) 0%,
      hsl(45, 93%, 45%) 100%
    );
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 65%) 0%,
      hsl(0, 84%, 55%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(142, 71%, 45%, 0.3);
    --shadow-glow: 0 0 25px hsla(142, 71%, 45%, 0.4);
  }

  /* Sunset Theme - Warm Orange with Vibrant Gradients */
  .theme-sunset {
    --background: 25 20% 97%;
    --background-secondary: 25 15% 94%;
    --background-tertiary: 25 12% 90%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(25, 25%, 98%) 0%,
      hsl(25, 15%, 95%) 100%
    );
    --foreground: 25 15% 10%;

    --primary: 25 95% 53%;
    --primary-hover: 25 95% 48%;
    --primary-light: 25 95% 63%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(25, 95%, 58%) 0%,
      hsl(25, 95%, 48%) 100%
    );
    --primary-glow: 0 0 25px hsla(25, 95%, 53%, 0.4);

    --card: 25 30% 99%;
    --card-hover: 25 25% 98%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(25, 30%, 99%) 0%,
      hsl(25, 20%, 97%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(25, 25%, 97%) 0%,
      hsl(25, 15%, 95%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(25, 95%, 58%) 0%,
      hsl(25, 95%, 48%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(25, 95%, 53%) 0%,
      hsl(25, 95%, 43%) 100%
    );

    --success-gradient: linear-gradient(
      135deg,
      hsl(142, 71%, 50%) 0%,
      hsl(142, 71%, 40%) 100%
    );
    --warning-gradient: linear-gradient(
      135deg,
      hsl(38, 95%, 58%) 0%,
      hsl(38, 95%, 48%) 100%
    );
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 65%) 0%,
      hsl(0, 84%, 55%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(25, 95%, 53%, 0.3);
    --shadow-glow: 0 0 25px hsla(25, 95%, 53%, 0.4);
  }

  /* Lavender Theme - Elegant Purple with Sophisticated Gradients */
  .theme-lavender {
    --background: 270 15% 97%;
    --background-secondary: 270 12% 94%;
    --background-tertiary: 270 10% 90%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(270, 20%, 98%) 0%,
      hsl(270, 12%, 95%) 100%
    );
    --foreground: 270 12% 10%;

    --primary: 271 81% 56%;
    --primary-hover: 271 81% 51%;
    --primary-light: 271 81% 66%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(271, 81%, 61%) 0%,
      hsl(271, 81%, 51%) 100%
    );
    --primary-glow: 0 0 25px hsla(271, 81%, 56%, 0.4);

    --card: 270 25% 99%;
    --card-hover: 270 20% 98%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(270, 25%, 99%) 0%,
      hsl(270, 15%, 97%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(270, 18%, 97%) 0%,
      hsl(270, 12%, 95%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(271, 81%, 61%) 0%,
      hsl(271, 81%, 51%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(271, 81%, 56%) 0%,
      hsl(271, 81%, 46%) 100%
    );

    --success-gradient: linear-gradient(
      135deg,
      hsl(142, 71%, 50%) 0%,
      hsl(142, 71%, 40%) 100%
    );
    --warning-gradient: linear-gradient(
      135deg,
      hsl(45, 93%, 55%) 0%,
      hsl(45, 93%, 45%) 100%
    );
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 65%) 0%,
      hsl(0, 84%, 55%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(271, 81%, 56%, 0.3);
    --shadow-glow: 0 0 25px hsla(271, 81%, 56%, 0.4);
  }

  /* Midnight Theme - Premium Dark with Rich Gradients */
  .theme-midnight {
    --background: 222 47% 11%;
    --background-secondary: 222 47% 8%;
    --background-tertiary: 222 47% 6%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(222, 47%, 12%) 0%,
      hsl(222, 47%, 9%) 100%
    );
    --foreground: 210 40% 98%;
    --foreground-secondary: 210 25% 75%;
    --foreground-muted: 210 15% 55%;

    --primary: 217 91% 60%;
    --primary-hover: 217 91% 65%;
    --primary-light: 217 91% 70%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(217, 91%, 65%) 0%,
      hsl(217, 91%, 55%) 100%
    );
    --primary-glow: 0 0 30px hsla(217, 91%, 60%, 0.5);

    --secondary: 217 32% 17%;
    --secondary-hover: 217 32% 22%;
    --secondary-gradient: linear-gradient(
      135deg,
      hsl(217, 32%, 19%) 0%,
      hsl(217, 32%, 15%) 100%
    );

    --card: 222 47% 14%;
    --card-hover: 222 47% 16%;
    --card-border: 217 32% 20%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(222, 47%, 15%) 0%,
      hsl(222, 47%, 12%) 100%
    );

    --panel: 222 47% 14%;
    --panel-gradient: linear-gradient(
      135deg,
      hsl(222, 47%, 15%) 0%,
      hsl(222, 47%, 12%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(222, 47%, 10%) 0%,
      hsl(222, 47%, 8%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(217, 91%, 65%) 0%,
      hsl(217, 91%, 55%) 100%
    );
    --sidebar-item-hover: 217 32% 16%;
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(217, 91%, 60%) 0%,
      hsl(217, 91%, 50%) 100%
    );

    --input: 217 32% 17%;
    --input-border: 217 32% 25%;
    --input-gradient: linear-gradient(
      135deg,
      hsl(217, 32%, 18%) 0%,
      hsl(217, 32%, 16%) 100%
    );

    --border: 217 32% 20%;
    --border-secondary: 217 32% 25%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 32% 16%;
    --accent-gradient: linear-gradient(
      135deg,
      hsl(217, 32%, 18%) 0%,
      hsl(217, 32%, 14%) 100%
    );

    --success-gradient: linear-gradient(
      135deg,
      hsl(142, 76%, 45%) 0%,
      hsl(142, 76%, 35%) 100%
    );
    --warning-gradient: linear-gradient(
      135deg,
      hsl(38, 92%, 60%) 0%,
      hsl(38, 92%, 50%) 100%
    );
    --destructive-gradient: linear-gradient(
      135deg,
      hsl(0, 84%, 70%) 0%,
      hsl(0, 84%, 60%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(217, 91%, 60%, 0.4);
    --shadow-glow: 0 0 30px hsla(217, 91%, 60%, 0.5);
  }

  /* Neon Cyberpunk Theme - Futuristic Dark with Neon Accents */
  .theme-neon {
    --background: 240 10% 3%;
    --background-secondary: 240 10% 6%;
    --background-tertiary: 240 10% 9%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(240, 10%, 4%) 0%,
      hsl(240, 10%, 7%) 100%
    );
    --foreground: 120 100% 85%;
    --foreground-secondary: 120 50% 70%;
    --foreground-muted: 120 30% 55%;

    --primary: 120 100% 50%;
    --primary-hover: 120 100% 55%;
    --primary-light: 120 100% 60%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(120, 100%, 55%) 0%,
      hsl(180, 100%, 45%) 100%
    );
    --primary-glow: 0 0 20px hsla(120, 100%, 50%, 0.8);

    --card: 240 10% 8%;
    --card-hover: 240 10% 12%;
    --card-border: 120 50% 25%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(240, 10%, 9%) 0%,
      hsl(240, 10%, 6%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(240, 10%, 5%) 0%,
      hsl(240, 10%, 3%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(120, 100%, 55%) 0%,
      hsl(180, 100%, 45%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(120, 100%, 50%) 0%,
      hsl(180, 100%, 40%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(120, 100%, 50%, 0.5);
    --shadow-glow: 0 0 40px hsla(120, 100%, 50%, 0.6);
  }

  /* Rose Gold Theme - Elegant Light with Rose Accents */
  .theme-rose {
    --background: 350 100% 98%;
    --background-secondary: 350 50% 95%;
    --background-tertiary: 350 30% 92%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(350, 100%, 99%) 0%,
      hsl(350, 50%, 96%) 100%
    );
    --foreground: 350 30% 10%;

    --primary: 330 81% 60%;
    --primary-hover: 330 81% 55%;
    --primary-light: 330 81% 70%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(330, 81%, 65%) 0%,
      hsl(340, 81%, 55%) 100%
    );
    --primary-glow: 0 0 25px hsla(330, 81%, 60%, 0.4);

    --card: 350 80% 99%;
    --card-hover: 350 60% 98%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(350, 80%, 99%) 0%,
      hsl(350, 40%, 97%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(350, 60%, 98%) 0%,
      hsl(350, 40%, 96%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(330, 81%, 65%) 0%,
      hsl(340, 81%, 55%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(330, 81%, 60%) 0%,
      hsl(340, 81%, 50%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(330, 81%, 60%, 0.3);
    --shadow-glow: 0 0 25px hsla(330, 81%, 60%, 0.4);
  }

  /* Ocean Theme - Deep Blue Dark Theme */
  .theme-ocean {
    --background: 220 30% 8%;
    --background-secondary: 220 30% 5%;
    --background-tertiary: 220 30% 3%;
    --background-gradient: linear-gradient(
      135deg,
      hsl(220, 30%, 9%) 0%,
      hsl(220, 30%, 6%) 100%
    );
    --foreground: 200 100% 90%;
    --foreground-secondary: 200 50% 75%;
    --foreground-muted: 200 30% 60%;

    --primary: 200 100% 50%;
    --primary-hover: 200 100% 55%;
    --primary-light: 200 100% 60%;
    --primary-gradient: linear-gradient(
      135deg,
      hsl(200, 100%, 55%) 0%,
      hsl(240, 100%, 45%) 100%
    );
    --primary-glow: 0 0 30px hsla(200, 100%, 50%, 0.5);

    --card: 220 30% 12%;
    --card-hover: 220 30% 15%;
    --card-border: 200 50% 20%;
    --card-gradient: linear-gradient(
      135deg,
      hsl(220, 30%, 13%) 0%,
      hsl(220, 30%, 10%) 100%
    );

    --sidebar-background: linear-gradient(
      180deg,
      hsl(220, 30%, 7%) 0%,
      hsl(220, 30%, 5%) 100%
    );
    --sidebar-accent-gradient: linear-gradient(
      135deg,
      hsl(200, 100%, 55%) 0%,
      hsl(240, 100%, 45%) 100%
    );
    --sidebar-item-active: linear-gradient(
      135deg,
      hsl(200, 100%, 50%) 0%,
      hsl(240, 100%, 40%) 100%
    );

    --shadow-colored: 0 10px 30px -5px hsla(200, 100%, 50%, 0.4);
    --shadow-glow: 0 0 30px hsla(200, 100%, 50%, 0.5);
  }

  /* Enhanced Theme Transitions */
  :root[style*="--theme-transition"] * {
    transition: var(--theme-transition) !important;
  }

  /* Theme Preview Mode */
  .theme-preview-mode {
    position: relative;
  }

  .theme-preview-mode::before {
    content: "PREVIEW MODE";
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-gradient);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 9999;
    animation: pulse 2s infinite;
  }

  /* Enhanced Theme Selector Animations */
  .theme-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .theme-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-lg);
  }

  .theme-preview-indicator {
    animation: theme-pulse 2s ease-in-out infinite;
  }

  @keyframes theme-pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(0.95);
    }
  }

  /* System Theme Detection */
  @media (prefers-color-scheme: dark) {
    :root.theme-auto {
      /* Auto theme will apply midnight theme colors in dark mode */
    }
  }

  @media (prefers-color-scheme: light) {
    :root.theme-auto {
      /* Auto theme will apply default theme colors in light mode */
    }
  }

  /* Enhanced Component Theming */
  .themed-gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .themed-shadow {
    box-shadow: var(--shadow-colored);
  }

  .themed-glow {
    box-shadow: var(--shadow-glow);
  }

  .themed-border {
    border-image: var(--primary-gradient) 1;
  }

  /* Theme-aware animations */
  .theme-transition * {
    transition:
      color 0.3s ease,
      background-color 0.3s ease,
      border-color 0.3s ease,
      box-shadow 0.3s ease,
      transform 0.3s ease !important;
  }
}

@layer base {
  * {
    @apply border-border;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: var(--background-gradient);
  }

  /* Enhanced Typography System with Semantic Classes */
  .text-display-large {
    font-family: var(--font-display);
    font-size: var(--text-6xl);
    line-height: var(--leading-none);
    letter-spacing: var(--tracking-tighter);
    font-weight: var(--font-extrabold);
  }

  .text-display {
    font-family: var(--font-display);
    font-size: var(--text-5xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    font-weight: var(--font-bold);
  }

  .text-headline-large {
    font-family: var(--font-display);
    font-size: var(--text-4xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    font-weight: var(--font-bold);
  }

  .text-headline {
    font-family: var(--font-display);
    font-size: var(--text-3xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    font-weight: var(--font-semibold);
  }

  .text-title-large {
    font-family: var(--font-sans);
    font-size: var(--text-2xl);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-semibold);
  }

  .text-title {
    font-family: var(--font-sans);
    font-size: var(--text-xl);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-medium);
  }

  .text-label-large {
    font-family: var(--font-sans);
    font-size: var(--text-lg);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-medium);
  }

  .text-label {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-normal);
  }

  .text-body-large {
    font-family: var(--font-sans);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-normal);
  }

  .text-body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-normal);
  }

  .text-caption {
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wide);
    font-weight: var(--font-normal);
  }

  .text-overline {
    font-family: var(--font-sans);
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-widest);
    font-weight: var(--font-medium);
    text-transform: uppercase;
  }

  .text-code {
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-normal);
  }

  /* Standard HTML headings enhanced with typography system */
  h1 {
    @apply text-headline;
  }

  h2 {
    @apply text-title-large;
  }

  h3 {
    @apply text-title;
  }

  h4 {
    @apply text-label-large;
  }

  h5 {
    @apply text-label;
  }

  h6 {
    @apply text-caption;
  }

  p {
    @apply text-body;
  }

  /* Code Typography */
  code,
  pre {
    font-family: var(--font-mono);
    font-feature-settings:
      "liga" 1,
      "calt" 1;
  }

  code {
    @apply text-code;
    background: hsl(var(--muted));
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    border: 1px solid hsl(var(--border));
  }

  pre {
    @apply text-code;
    background: hsl(var(--card));
    padding: 1rem;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    overflow-x: auto;
  }

  pre code {
    background: transparent;
    padding: 0;
    border: none;
    border-radius: 0;
  }

  /* Desktop Application Styling */
  #root {
    @apply min-h-screen;
    background: var(--background-gradient);
  }

  /* Enhanced Card Styling with Gradients */
  .card-modern {
    background: var(--card-gradient);
    border: 1px solid hsl(var(--card-border));
    box-shadow: var(--shadow);
    border-radius: var(--radius);
    backdrop-filter: blur(8px);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
  }

  .card-modern::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--primary) / 0.3),
      transparent
    );
    opacity: 0;
    transition: opacity var(--transition-base);
  }

  .card-modern:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: hsl(var(--primary) / 0.2);
  }

  .card-modern:hover::before {
    opacity: 1;
  }

  /* Enhanced Panel Styling */
  .panel {
    background: var(--panel-gradient);
    border: 1px solid hsl(var(--panel-border));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(12px);
    position: relative;
    overflow: hidden;
  }

  .panel::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-gradient);
  }

  /* Enhanced Sidebar Styling */
  .sidebar {
    background: var(--sidebar-background);
    border-right: 1px solid hsl(var(--sidebar-border));
    color: hsl(var(--sidebar-foreground));
    backdrop-filter: blur(12px);
  }

  .sidebar-item {
    @apply transition-all duration-200 ease-out;
    position: relative;
    overflow: hidden;
  }

  .sidebar-item::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--sidebar-accent-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-base);
  }

  .sidebar-item:hover {
    background: hsl(var(--sidebar-item-hover));
    transform: translateX(2px);
  }

  .sidebar-item.active {
    background: var(--sidebar-item-active);
    color: hsl(var(--sidebar-accent-foreground));
    box-shadow: var(--shadow-colored);
  }

  .sidebar-item.active::before {
    transform: scaleY(1);
  }

  /* Enhanced Input Styling */
  .input-modern {
    background: var(--input-gradient);
    border: 1px solid hsl(var(--input-border));
    color: hsl(var(--input-foreground));
    transition: all var(--transition-base);
    backdrop-filter: blur(8px);
  }

  .input-modern::placeholder {
    color: hsl(var(--input-placeholder));
  }

  .input-modern:focus {
    outline: none;
    border-color: hsl(var(--primary));
    box-shadow:
      0 0 0 3px hsl(var(--primary) / 0.1),
      var(--shadow-glow);
  }

  /* Enhanced Button Styling */
  .btn-modern {
    @apply transition-all duration-200 ease-out;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
  }

  .btn-modern::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .btn-modern:hover::before {
    left: 100%;
  }

  .btn-modern:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }

  /* Enhanced Primary Button */
  .btn-primary {
    background: var(--primary-gradient);
    color: hsl(var(--primary-foreground));
    border: none;
    box-shadow: var(--shadow-colored);
  }

  .btn-primary:hover {
    box-shadow: var(--shadow-glow);
  }

  /* Text Gradients */
  .text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-bold);
  }

  .text-gradient-success {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-warning {
    background: var(--warning-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-destructive {
    background: var(--destructive-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass Effect */
  .glass {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: hsla(var(--card), 0.8);
    border: 1px solid hsla(var(--border), 0.2);
    box-shadow: var(--shadow-lg);
  }

  /* Enhanced Progress Indicators */
  .progress-modern {
    background: hsl(var(--muted));
    border-radius: var(--radius);
    overflow: hidden;
    position: relative;
  }

  .progress-modern::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }

  .progress-fill {
    background: var(--primary-gradient);
    height: 100%;
    border-radius: inherit;
    transition: width 0.5s var(--spring);
    position: relative;
    overflow: hidden;
  }

  .progress-fill::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
    animation: progress-shine 2s infinite;
  }

  /* Status Indicators */
  .status-success {
    background: var(--success-gradient);
    color: hsl(var(--success-foreground));
    box-shadow: var(--success-glow);
  }

  .status-warning {
    background: var(--warning-gradient);
    color: hsl(var(--warning-foreground));
    box-shadow: var(--warning-glow);
  }

  .status-destructive {
    background: var(--destructive-gradient);
    color: hsl(var(--destructive-foreground));
    box-shadow: var(--destructive-glow);
  }

  /* Modern Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--background-secondary));
    border-radius: var(--radius);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--radius);
    transition: all var(--transition-base);
  }

  ::-webkit-scrollbar-thumb:hover {
    box-shadow: var(--shadow-glow);
  }

  /* Dialog specific scrollbars */
  .dialog-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) hsl(var(--background-secondary));
  }

  .dialog-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .dialog-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .dialog-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  .dialog-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }

  /* Animations */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }

    100% {
      transform: translateX(100%);
    }
  }

  @keyframes progress-shine {
    0% {
      transform: translateX(-100%);
    }

    100% {
      transform: translateX(200%);
    }
  }

  @keyframes glow-pulse {
    0%,
    100% {
      box-shadow: var(--shadow-glow);
    }

    50% {
      box-shadow: 0 0 40px hsla(var(--primary), 0.6);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-3px);
    }
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes pulse-ring {
    0% {
      transform: scale(0.33);
    }

    40%,
    50% {
      opacity: 1;
    }

    100% {
      opacity: 0;
      transform: scale(1.33);
    }
  }

  @keyframes slide-in-bottom {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  /* Utility Classes */
  .animate-glow {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .animate-slide-in {
    animation: slide-in-bottom 0.5s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .gradient-border {
    position: relative;
    background: var(--panel-gradient);
    border-radius: var(--radius-lg);
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 0;
    padding: 1px;
    background: var(--primary-gradient);
    border-radius: inherit;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: xor;
  }

  /* Enhanced Status Indicators */
  .status-primary {
    background: var(--primary-gradient);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--primary-glow);
  }

  /* Loading States */
  .loading-skeleton {
    background: linear-gradient(
      90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted) / 0.8) 50%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Improved Hover Effects */
  .hover-lift {
    transition: all var(--transition-base);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  /* Enhanced Focus Styles */
  .focus-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
  }
}

/* Responsive Typography */
@media (min-width: 640px) {
  :root {
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 3.75rem; /* 60px */
  }
}

@media (min-width: 768px) {
  :root {
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 4rem; /* 64px */
  }
}

@media (min-width: 1024px) {
  :root {
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 4rem; /* 64px */
  }

  body {
    font-size: var(--text-base);
  }

  .container {
    max-width: none;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1440px) {
  :root {
    --text-base: 1.125rem; /* 18px */
    --text-lg: 1.25rem; /* 20px */
    --text-xl: 1.375rem; /* 22px */
    --text-2xl: 1.625rem; /* 26px */
    --text-3xl: 2rem; /* 32px */
    --text-4xl: 2.5rem; /* 40px */
    --text-5xl: 3.25rem; /* 52px */
    --text-6xl: 4.5rem; /* 72px */
  }

  body {
    font-size: var(--text-base);
  }
}

@media (min-width: 1920px) {
  :root {
    --text-base: 1.125rem; /* 18px */
    --text-lg: 1.25rem; /* 20px */
    --text-xl: 1.5rem; /* 24px */
    --text-2xl: 1.75rem; /* 28px */
    --text-3xl: 2.25rem; /* 36px */
    --text-4xl: 2.75rem; /* 44px */
    --text-5xl: 3.5rem; /* 56px */
    --text-6xl: 5rem; /* 80px */
  }

  body {
    font-size: var(--text-base);
  }
}

/* Print Styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  body {
    font-family: Georgia, serif;
    font-size: 12pt;
    line-height: 1.5;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
    font-family: Arial, sans-serif;
  }

  p,
  blockquote {
    page-break-inside: avoid;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 30%;
    --input-border: 0 0% 30%;
  }
}

/* Focus visible for keyboard navigation */
@media (focus-visible) {
  *:focus:not(:focus-visible) {
    outline: none;
  }

  *:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}
