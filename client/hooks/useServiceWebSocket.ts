import { useState, useEffect, useRef, useCallback } from "react";

interface ProgressItem {
  id: string;
  title: string;
  type: "sync" | "upload" | "download";
  progress: number;
  status: "running" | "completed" | "failed" | "paused";
  startTime: Date;
  estimatedCompletion?: Date;
  currentFile?: string;
  totalFiles?: number;
  processedFiles?: number;
}

interface EventLogItem {
  id: string;
  title: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  category: "sync" | "upload" | "download" | "system" | "auth";
  timestamp: Date;
  clientPath?: string;
  details?: string;
}

interface ServiceCommand {
  type: "service-command";
  command: "start" | "stop" | "pause" | "resume" | "restart" | "shutdown";
}

interface ServiceStatus {
  type: "service-status";
  status: "running" | "stopped" | "paused" | "starting" | "stopping" | "error";
}

interface SystemReady {
  type: "system-ready";
}

interface ProgressUpdate {
  type: "progress";
  data: ProgressItem;
}

interface EventUpdate {
  type: "event";
  data: EventLogItem;
}

type WSMessage = ProgressUpdate | EventUpdate | ServiceStatus | SystemReady;

export function useServiceWebSocket() {
  const [progressItems, setProgressItems] = useState<ProgressItem[]>([]);
  const [events, setEvents] = useState<EventLogItem[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [serviceStatus, setServiceStatus] = useState<
    "running" | "stopped" | "paused" | "starting" | "stopping" | "error"
  >("stopped");
  const [isSystemReady, setIsSystemReady] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = async () => {
    // Check if we're in development mode first
    try {
      const response = await fetch("/api/ws-health");
      const data = await response.json();

      if (data.status === "development") {
        console.log(
          "🔧 Development mode: WebSocket not available, using simulated data",
        );
        setIsConnected(false);
        setIsSystemReady(true);
        return;
      }
    } catch (error) {
      console.log("🔧 Health check failed, assuming development mode");
      setIsConnected(false);
      setIsSystemReady(true);
      return;
    }

    // Production mode - establish WebSocket connection
    try {
      // Check if running in Electron
      const isElectron = typeof window !== "undefined" && window.electronAPI;
      let wsUrl: string;

      if (isElectron) {
        // In Electron, get the server port from the main process
        const port = await window.electronAPI.getServerPort();
        wsUrl = `ws://localhost:${port}/ws`;
      } else {
        // In browser, use current host and port
        const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
        wsUrl = `${protocol}//${window.location.host}/ws`;
      }

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log("WebSocket connected");
        setIsConnected(true);
        setReconnectAttempts(0);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WSMessage = JSON.parse(event.data);

          if (message.type === "progress") {
            const progressData = {
              ...message.data,
              startTime: new Date(message.data.startTime),
              estimatedCompletion: message.data.estimatedCompletion
                ? new Date(message.data.estimatedCompletion)
                : undefined,
            };

            setProgressItems((prev) => {
              const existing = prev.find((item) => item.id === progressData.id);
              if (existing) {
                return prev.map((item) =>
                  item.id === progressData.id ? progressData : item,
                );
              } else {
                return [...prev, progressData];
              }
            });
          } else if (message.type === "event") {
            const eventData = {
              ...message.data,
              timestamp: new Date(message.data.timestamp),
            };

            setEvents((prev) => {
              // Add new event to the beginning and keep only last 50 events
              const newEvents = [
                eventData,
                ...prev.filter((e) => e.id !== eventData.id),
              ];
              return newEvents.slice(0, 50);
            });
          } else if (message.type === "service-status") {
            setServiceStatus(message.status);
          } else if (message.type === "system-ready") {
            setIsSystemReady(true);
            setServiceStatus("running");
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      wsRef.current.onclose = () => {
        console.log("WebSocket disconnected");
        setIsConnected(false);
        setIsSystemReady(false);

        // Attempt to reconnect with exponential backoff
        if (reconnectAttempts < 5) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts((prev) => prev + 1);
            connect();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error(
          "WebSocket connection error:",
          error.type || "Connection failed",
        );
        setIsConnected(false);
        setIsSystemReady(false);
      };
    } catch (error) {
      console.error("Failed to create WebSocket connection:", error);
      setIsConnected(false);
      setIsSystemReady(false);
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
    setIsSystemReady(false);
  };

  const sendServiceCommand = useCallback(
    (
      command: "start" | "stop" | "pause" | "resume" | "restart" | "shutdown",
    ) => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        const message: ServiceCommand = {
          type: "service-command",
          command,
        };
        wsRef.current.send(JSON.stringify(message));

        // Add event log for the command
        const eventData: EventLogItem = {
          id: `cmd-${Date.now()}`,
          title: `Service ${command}`,
          message: `Service ${command} command sent`,
          type: "info",
          category: "system",
          timestamp: new Date(),
        };

        setEvents((prev) => [eventData, ...prev.slice(0, 49)]);
      } else {
        console.error("WebSocket is not connected");
      }
    },
    [],
  );

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, []);

  // Clean up completed tasks periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      setProgressItems((prev) =>
        prev.filter(
          (item) =>
            item.status !== "completed" ||
            new Date().getTime() - new Date(item.startTime).getTime() <
              5 * 60 * 1000, // Keep completed for 5 minutes
        ),
      );
    }, 60000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  return {
    progressItems,
    events,
    isConnected,
    reconnectAttempts,
    serviceStatus,
    isSystemReady,
    connect,
    disconnect,
    sendServiceCommand,
  };
}
