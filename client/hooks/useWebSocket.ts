import { useState, useEffect, useRef, useCallback } from "react";

interface WebSocketMessage {
  type:
    | "progress"
    | "event"
    | "notification"
    | "schedule"
    | "service-status"
    | "system-ready"
    | "system-metrics";
  data?: any;
  timestamp?: string;
}

interface ProgressItem {
  id: string;
  title: string;
  type: "sync" | "upload" | "download";
  progress: number;
  status: "running" | "completed" | "failed" | "paused";
  startTime: Date;
  estimatedCompletion?: Date;
  currentFile?: string;
  totalFiles?: number;
  processedFiles?: number;
}

interface EventLogItem {
  id: string;
  title: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  category: "sync" | "upload" | "download" | "system" | "auth";
  timestamp: Date;
  clientPath?: string;
  details?: string;
}

interface NotificationItem {
  id: string;
  title: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  timestamp: Date;
  read: boolean;
}

interface Schedule {
  id: string;
  name: string;
  folder: string;
  destination: string;
  frequency: string;
  nextRun: Date;
  lastRun: Date;
  status: "active" | "paused" | "error";
  filesCount: number;
  isRunning: boolean;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  storage: number;
  uptime: string;
}

export function useWebSocket() {
  const [progressItems, setProgressItems] = useState<ProgressItem[]>([]);
  const [events, setEvents] = useState<EventLogItem[]>([]);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [serviceStatus, setServiceStatus] = useState<
    "running" | "stopped" | "paused" | "starting" | "stopping" | "error"
  >("stopped");
  const [isSystemReady, setIsSystemReady] = useState(false);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    storage: 0,
    uptime: "0s",
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const disconnectKillTimerRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    // Check if the WebSocket health endpoint is available with longer timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for health check

    fetch("/api/ws-health", {
      signal: controller.signal,
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        clearTimeout(timeoutId);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log("🏥 WebSocket health check response:", data);

        // Require healthy WebSocket service - no fallback mode
        if (data.status === "unhealthy" || !data.wsManagerAvailable) {
          const error = `WebSocket service ${data.status === "unhealthy" ? "unhealthy" : "manager not available"}`;
          console.error("💀 FATAL ERROR:", error);
          throw new Error(`Connection required: ${error}`);
        }

        // WebSocket service is healthy - establish WebSocket connection
        try {
          const protocol =
            window.location.protocol === "https:" ? "wss:" : "ws:";
          const wsUrl = `${protocol}//${window.location.host}/ws`;

          console.log("🔗 Attempting WebSocket connection to:", wsUrl);
          console.log("🔗 Connection details:", {
            protocol: window.location.protocol,
            host: window.location.host,
            fullUrl: wsUrl,
          });

          wsRef.current = new WebSocket(wsUrl);

          // Add connection timeout with longer duration
          const connectionTimeout = setTimeout(() => {
            if (
              wsRef.current &&
              wsRef.current.readyState === WebSocket.CONNECTING
            ) {
              console.log("🔴 WebSocket connection timeout, closing...");
              wsRef.current.close();
            }
          }, 30000); // 30 second timeout for connection

          wsRef.current.onopen = () => {
            clearTimeout(connectionTimeout);
            // Clear the kill timer if connection is restored
            if (disconnectKillTimerRef.current) {
              clearTimeout(disconnectKillTimerRef.current);
              disconnectKillTimerRef.current = null;
              console.log("✅ Kill timer cancelled - connection restored");
            }
            console.log("✅ WebSocket connected successfully");
            setIsConnected(true);
            setIsSystemReady(true);
            setReconnectAttempts(0); // Reset retry counter on successful connection

            // Request initial data
            sendMessage({ type: "request-initial-data" });
          };

          wsRef.current.onmessage = (event) => {
            try {
              const message: WebSocketMessage = JSON.parse(event.data);

              switch (message.type) {
                case "progress":
                  if (message.data) {
                    const progressData = {
                      ...message.data,
                      startTime: new Date(message.data.startTime),
                      estimatedCompletion: message.data.estimatedCompletion
                        ? new Date(message.data.estimatedCompletion)
                        : undefined,
                    };

                    setProgressItems((prev) => {
                      const existing = prev.find(
                        (item) => item.id === progressData.id,
                      );
                      if (existing) {
                        return prev.map((item) =>
                          item.id === progressData.id ? progressData : item,
                        );
                      } else {
                        return [...prev, progressData];
                      }
                    });
                  }
                  break;

                case "event":
                  if (message.data) {
                    const eventData = {
                      ...message.data,
                      timestamp: new Date(message.data.timestamp),
                    };

                    setEvents((prev) => {
                      const newEvents = [
                        eventData,
                        ...prev.filter((e) => e.id !== eventData.id),
                      ];
                      return newEvents.slice(0, 50); // Keep only last 50 events
                    });
                  }
                  break;

                case "notification":
                  if (message.data) {
                    const notificationData = {
                      ...message.data,
                      timestamp: new Date(message.data.timestamp),
                    };

                    setNotifications((prev) => {
                      const newNotifications = [
                        notificationData,
                        ...prev.filter((n) => n.id !== notificationData.id),
                      ];
                      return newNotifications.slice(0, 100); // Keep only last 100 notifications
                    });
                  }
                  break;

                case "schedule":
                  if (message.data) {
                    const scheduleData = {
                      ...message.data,
                      nextRun: new Date(message.data.nextRun),
                      lastRun: new Date(message.data.lastRun),
                    };

                    setSchedules((prev) => {
                      const existing = prev.find(
                        (s) => s.id === scheduleData.id,
                      );
                      if (existing) {
                        return prev.map((s) =>
                          s.id === scheduleData.id ? scheduleData : s,
                        );
                      } else {
                        return [...prev, scheduleData];
                      }
                    });
                  }
                  break;

                case "service-status":
                  if (message.data?.status) {
                    setServiceStatus(message.data.status);
                  }
                  break;

                case "system-ready":
                  setIsSystemReady(true);
                  setServiceStatus("running");
                  break;

                case "system-metrics":
                  if (message.data) {
                    setSystemMetrics(message.data);
                  }
                  break;
              }
            } catch (error) {
              console.error("Error parsing WebSocket message:", error);
            }
          };

          wsRef.current.onclose = (event) => {
            clearTimeout(connectionTimeout);
            console.log("🔗 WebSocket disconnected:", {
              code: event.code,
              reason: event.reason,
              wasClean: event.wasClean,
            });
            setIsConnected(false);
            setIsSystemReady(false);

            // Start 5-minute kill timer
            console.warn("⚠️ Connection lost - starting 5-minute kill timer");
            disconnectKillTimerRef.current = setTimeout(
              () => {
                console.error(
                  "💀 FATAL ERROR: 5 minutes without connection - killing application",
                );
                document.body.innerHTML = `
                <div style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100vh;
                  background: #000;
                  color: #ff4444;
                  font-family: monospace;
                  text-align: center;
                ">
                  <h1 style="font-size: 3rem; margin-bottom: 1rem;">💀 CONNECTION TIMEOUT</h1>
                  <p style="font-size: 1.2rem; margin-bottom: 2rem;">Lost connection for 5 minutes</p>
                  <p style="font-size: 1rem; opacity: 0.7;">Application terminated</p>
                  <button onclick="location.reload()" style="
                    margin-top: 2rem;
                    padding: 1rem 2rem;
                    background: #ff4444;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 1rem;
                    cursor: pointer;
                  ">RESTART</button>
                </div>
              `;
              },
              5 * 60 * 1000,
            ); // 5 minutes

            // Continuously attempt to reconnect during the 5-minute window with longer delays
            const delay = Math.min(
              5000 + reconnectAttempts * 2000, // Start at 5s, increase by 2s each time
              60000, // Max 60 seconds between attempts
            );
            console.log(
              `🔄 Attempting reconnect ${reconnectAttempts + 1} in ${delay / 1000}s`,
            );

            reconnectTimeoutRef.current = setTimeout(() => {
              setReconnectAttempts((prev) => prev + 1);
              connect();
            }, delay);
          };

          wsRef.current.onerror = (error) => {
            clearTimeout(connectionTimeout);
            console.warn("⚠️ WebSocket connection error - will retry:", {
              error: error,
              type: error.type,
              readyState: wsRef.current?.readyState,
              url: wsRef.current?.url,
            });

            // Don't kill the app - let it retry through the normal reconnection logic
            setIsConnected(false);
            setIsSystemReady(false);
          };
        } catch (error) {
          console.error(
            "💀 FATAL ERROR: Failed to create WebSocket connection:",
            error,
          );

          // Kill the application immediately
          document.body.innerHTML = `
            <div style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100vh;
              background: #000;
              color: #ff4444;
              font-family: monospace;
              text-align: center;
            ">
              <h1 style="font-size: 3rem; margin-bottom: 1rem;">💀 CONNECTION FAILED</h1>
              <p style="font-size: 1.2rem; margin-bottom: 2rem;">Unable to create WebSocket</p>
              <p style="font-size: 1rem; opacity: 0.7;">${error.message}</p>
              <button onclick="location.reload()" style="
                margin-top: 2rem;
                padding: 1rem 2rem;
                background: #ff4444;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 1rem;
                cursor: pointer;
              ">RETRY</button>
            </div>
          `;
          throw error;
        }
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        if (error.name === "AbortError") {
          console.warn("⚠️ WebSocket health check timed out - will retry");
        } else {
          console.warn(
            "⚠️ WebSocket health check failed - will retry:",
            error.message,
          );
        }

        // Don't kill the app - let splash screen handle the waiting
        setIsConnected(false);
        setIsSystemReady(false);

        // Retry the connection after a longer delay
        const retryDelay = Math.min(
          10000 + reconnectAttempts * 5000, // Start at 10s, increase by 5s each time
          60000, // Max 60 seconds
        );
        setTimeout(() => {
          console.log(
            `🔄 Retrying WebSocket health check in ${retryDelay / 1000}s...`,
          );
          setReconnectAttempts((prev) => prev + 1);
          connect();
        }, retryDelay);
      });
  }, [reconnectAttempts]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (disconnectKillTimerRef.current) {
      clearTimeout(disconnectKillTimerRef.current);
    }
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
    setIsSystemReady(false);
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn("WebSocket is not connected, message not sent:", message);
    }
  }, []);

  // WebSocket actions
  const sendServiceCommand = useCallback(
    (
      command: "start" | "stop" | "pause" | "resume" | "restart" | "shutdown",
    ) => {
      sendMessage({
        type: "service-command",
        command,
      });
    },
    [sendMessage],
  );

  const createSchedule = useCallback(
    (schedule: Omit<Schedule, "id">) => {
      sendMessage({
        type: "create-schedule",
        data: schedule,
      });
    },
    [sendMessage],
  );

  const updateSchedule = useCallback(
    (id: string, updates: Partial<Schedule>) => {
      sendMessage({
        type: "update-schedule",
        data: { id, updates },
      });
    },
    [sendMessage],
  );

  const deleteSchedule = useCallback(
    (id: string) => {
      sendMessage({
        type: "delete-schedule",
        data: { id },
      });
    },
    [sendMessage],
  );

  const markNotificationRead = useCallback(
    (id: string) => {
      sendMessage({
        type: "mark-notification-read",
        data: { id },
      });

      // Optimistically update local state
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n)),
      );
    },
    [sendMessage],
  );

  const uploadFiles = useCallback(
    (files: FileList, destination?: string) => {
      sendMessage({
        type: "upload-files",
        data: {
          files: Array.from(files).map((f) => ({
            name: f.name,
            size: f.size,
            type: f.type,
          })),
          destination,
        },
      });
    },
    [sendMessage],
  );

  const syncFolder = useCallback(
    (folderPath: string, destination: string) => {
      sendMessage({
        type: "sync-folder",
        data: { folderPath, destination },
      });
    },
    [sendMessage],
  );

  useEffect(() => {
    connect();
    return () => disconnect();
  }, [connect, disconnect]);

  // Cleanup completed tasks periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      setProgressItems((prev) =>
        prev.filter(
          (item) =>
            item.status !== "completed" ||
            new Date().getTime() - new Date(item.startTime).getTime() <
              5 * 60 * 1000, // Keep completed for 5 minutes
        ),
      );
    }, 60000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  return {
    // State
    progressItems,
    events,
    notifications,
    schedules,
    isConnected,
    reconnectAttempts,
    serviceStatus,
    isSystemReady,
    systemMetrics,

    // Actions
    connect,
    disconnect,
    sendMessage,
    sendServiceCommand,
    createSchedule,
    updateSchedule,
    deleteSchedule,
    markNotificationRead,
    uploadFiles,
    syncFolder,
  };
}
