interface UploadResponse {
  success: boolean
  files?: Array<{
    id: string
    originalName: string
    size: number
    mimeType: string
    uploadedAt: string
    url?: string
  }>
  error?: string
}

interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export class FileUploadService {
  private baseUrl = 'https://api.medvestcapital.w8file.com'
  private apiKey: string | null = null
  private tenantId: string | null = null
  private providerId: string | null = null

  constructor(apiKey?: string) {
    this.apiKey = apiKey || null
    this.loadCredentials()
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey
  }

  private loadCredentials() {
    this.apiKey = localStorage.getItem("w8file_api_key")
    this.tenantId = localStorage.getItem("w8file_tenant_id")
    this.providerId = localStorage.getItem("w8file_provider_id")
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    if (this.tenantId) {
      headers['X-Tenant-ID'] = this.tenantId
    }

    if (this.providerId) {
      headers['X-Provider-ID'] = this.providerId
    }

    return headers
  }

  async uploadFiles(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<UploadResponse[]> {
    const results: UploadResponse[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      try {
        const result = await this.uploadSingleFile(file, (progress) => {
          onProgress?.(i, progress)
        })
        results.push(result)
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Upload failed'
        })
      }
    }

    return results
  }

  private async uploadSingleFile(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResponse> {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('file', file)

      const xhr = new XMLHttpRequest()

      // Set up progress tracking
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress: UploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          }
          onProgress(progress)
        }
      })

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            resolve({
              success: true,
              files: response.files || [{
                id: response.id || Date.now().toString(),
                originalName: file.name,
                size: file.size,
                mimeType: file.type,
                uploadedAt: new Date().toISOString(),
                url: response.url
              }]
            })
          } catch (error) {
            resolve({
              success: true,
              files: [{
                id: Date.now().toString(),
                originalName: file.name,
                size: file.size,
                mimeType: file.type,
                uploadedAt: new Date().toISOString()
              }]
            })
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`))
        }
      })

      xhr.addEventListener('error', () => {
        reject(new Error('Network error during upload'))
      })

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'))
      })

      // Configure the request
      xhr.open('POST', `${this.baseUrl}/api/upload`) // Adjust endpoint as needed

      // Add authentication headers if credentials are available
      // Reload credentials in case they were updated
      this.loadCredentials()

      if (this.apiKey) {
        xhr.setRequestHeader('Authorization', `Bearer ${this.apiKey}`)
      }

      if (this.tenantId) {
        xhr.setRequestHeader('X-Tenant-ID', this.tenantId)
      }

      if (this.providerId) {
        xhr.setRequestHeader('X-Provider-ID', this.providerId)
      }

      // Send the request
      xhr.send(formData)
    })
  }

  async listFiles(folderId?: string): Promise<any[]> {
    try {
      const url = new URL(`${this.baseUrl}/api/files`)
      if (folderId) {
        url.searchParams.append('folderId', folderId)
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getHeaders()
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch files: ${response.status}`)
      }

      const data = await response.json()
      return data.files || data || []
    } catch (error) {
      console.error('Error listing files:', error)
      return []
    }
  }

  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/files/${fileId}`, {
        method: 'DELETE',
        headers: this.getHeaders()
      })

      return response.ok
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }

  async createFolder(name: string, parentId?: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/folders`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          name,
          parentId
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to create folder: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating folder:', error)
      throw error
    }
  }
}

// Create a singleton instance
export const fileUploadService = new FileUploadService()

// TODO: Configure with actual API key
// fileUploadService.setApiKey('your-api-key-here')
