interface ScheduledUpload {
  id: string
  files: File[]
  scheduledTime: Date
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled'
  folderId?: string
  description?: string
  createdAt: Date
  progress?: number
  error?: string
}

export class UploadSchedulerService {
  private scheduledUploads: Map<string, ScheduledUpload> = new Map()
  private timers: Map<string, NodeJS.Timeout> = new Map()

  constructor() {
    // Load scheduled uploads from localStorage on initialization
    this.loadFromStorage()
    // Resume any pending uploads
    this.resumePendingUploads()
  }

  scheduleUpload(
    files: File[],
    scheduledTime: Date,
    options?: {
      folderId?: string
      description?: string
    }
  ): string {
    const id = this.generateId()
    const scheduledUpload: ScheduledUpload = {
      id,
      files,
      scheduledTime,
      status: 'pending',
      folderId: options?.folderId,
      description: options?.description,
      createdAt: new Date(),
      progress: 0
    }

    this.scheduledUploads.set(id, scheduledUpload)
    this.saveToStorage()

    // Schedule the upload
    this.scheduleExecution(scheduledUpload)

    return id
  }

  getScheduledUploads(): ScheduledUpload[] {
    return Array.from(this.scheduledUploads.values()).sort(
      (a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime()
    )
  }

  getUploadById(id: string): ScheduledUpload | undefined {
    return this.scheduledUploads.get(id)
  }

  cancelScheduledUpload(id: string): boolean {
    const upload = this.scheduledUploads.get(id)
    if (!upload || upload.status !== 'pending') {
      return false
    }

    // Clear the timer
    const timer = this.timers.get(id)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(id)
    }

    // Update status
    upload.status = 'cancelled'
    this.scheduledUploads.set(id, upload)
    this.saveToStorage()

    return true
  }

  rescheduleUpload(id: string, newTime: Date): boolean {
    const upload = this.scheduledUploads.get(id)
    if (!upload || upload.status !== 'pending') {
      return false
    }

    // Clear existing timer
    const timer = this.timers.get(id)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(id)
    }

    // Update scheduled time
    upload.scheduledTime = newTime
    this.scheduledUploads.set(id, upload)
    this.saveToStorage()

    // Reschedule
    this.scheduleExecution(upload)

    return true
  }

  deleteScheduledUpload(id: string): boolean {
    const upload = this.scheduledUploads.get(id)
    if (!upload) {
      return false
    }

    // Cancel if pending
    if (upload.status === 'pending') {
      this.cancelScheduledUpload(id)
    }

    // Remove from storage
    this.scheduledUploads.delete(id)
    this.saveToStorage()

    return true
  }

  private scheduleExecution(upload: ScheduledUpload) {
    const now = new Date()
    const delay = upload.scheduledTime.getTime() - now.getTime()

    if (delay <= 0) {
      // Execute immediately if time has passed
      this.executeUpload(upload)
    } else {
      // Schedule for later
      const timer = setTimeout(() => {
        this.executeUpload(upload)
      }, delay)
      
      this.timers.set(upload.id, timer)
    }
  }

  private async executeUpload(upload: ScheduledUpload) {
    try {
      // Update status to uploading
      upload.status = 'uploading'
      upload.progress = 0
      this.scheduledUploads.set(upload.id, upload)
      this.saveToStorage()

      // Simulate upload progress (replace with real API call)
      await this.simulateUpload(upload)

      // Mark as completed
      upload.status = 'completed'
      upload.progress = 100
      this.scheduledUploads.set(upload.id, upload)
      this.saveToStorage()

      // Clean up timer
      this.timers.delete(upload.id)

      // Notify success (you can add custom callbacks here)
      this.notifyUploadComplete(upload)

    } catch (error) {
      upload.status = 'failed'
      upload.error = error instanceof Error ? error.message : 'Upload failed'
      this.scheduledUploads.set(upload.id, upload)
      this.saveToStorage()
      this.timers.delete(upload.id)
    }
  }

  private async simulateUpload(upload: ScheduledUpload): Promise<void> {
    // Simulate upload progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 200))
      upload.progress = progress
      this.scheduledUploads.set(upload.id, upload)
      this.saveToStorage()
    }
  }

  private notifyUploadComplete(upload: ScheduledUpload) {
    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification('Upload Complete', {
        body: `Successfully uploaded ${upload.files.length} file(s)`,
        icon: '/favicon.ico'
      })
    }
  }

  private resumePendingUploads() {
    const now = new Date()
    
    this.scheduledUploads.forEach(upload => {
      if (upload.status === 'pending') {
        if (upload.scheduledTime <= now) {
          // Execute immediately
          this.executeUpload(upload)
        } else {
          // Reschedule
          this.scheduleExecution(upload)
        }
      }
    })
  }

  private saveToStorage() {
    try {
      const data = Array.from(this.scheduledUploads.values()).map(upload => ({
        ...upload,
        // Don't save File objects, just metadata
        files: upload.files.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified
        })),
        scheduledTime: upload.scheduledTime.toISOString(),
        createdAt: upload.createdAt.toISOString()
      }))
      
      localStorage.setItem('scheduled_uploads', JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save scheduled uploads:', error)
    }
  }

  private loadFromStorage() {
    try {
      const data = localStorage.getItem('scheduled_uploads')
      if (!data) return

      const uploads = JSON.parse(data)
      uploads.forEach((uploadData: any) => {
        // Skip completed uploads older than 24 hours
        const createdAt = new Date(uploadData.createdAt)
        const now = new Date()
        if (uploadData.status === 'completed' && 
            now.getTime() - createdAt.getTime() > 24 * 60 * 60 * 1000) {
          return
        }

        const upload: ScheduledUpload = {
          ...uploadData,
          scheduledTime: new Date(uploadData.scheduledTime),
          createdAt: new Date(uploadData.createdAt),
          files: [] // Files can't be restored from storage
        }

        this.scheduledUploads.set(upload.id, upload)
      })
    } catch (error) {
      console.error('Failed to load scheduled uploads:', error)
    }
  }

  private generateId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Request notification permission
  async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }
}

// Create singleton instance
export const uploadSchedulerService = new UploadSchedulerService()
