interface SyncSchedule {
  id: string;
  name: string;
  sourceFolder: string;
  destinationFolder?: string; // Auto-generated path
  clientId: string; // Added for client/year/month structure
  interval: "daily" | "weekly" | "monthly";
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly (0 = Sunday)
  dayOfMonth?: number; // 1-31 for monthly
  isActive: boolean;
  lastSync?: Date;
  nextSync: Date;
  exclusions: SyncExclusion[];
  createdAt: Date;
  updatedAt: Date;
  status: "pending" | "syncing" | "completed" | "failed" | "paused";
  progress?: number;
  error?: string;
  syncHistory: SyncHistory[];
}

interface SyncExclusion {
  id: string;
  type: "file" | "folder" | "pattern";
  value: string; // filename, folder path, or regex pattern
  isActive: boolean;
}

interface SyncHistory {
  id: string;
  startTime: Date;
  endTime?: Date;
  status: "success" | "failed" | "partial";
  filesProcessed: number;
  filesSynced: number;
  filesSkipped: number;
  error?: string;
}

export class FolderSyncService {
  private syncSchedules: Map<string, SyncSchedule> = new Map();
  private syncTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.loadFromStorage();
    this.scheduleAllSyncs();
  }

  createSyncSchedule(config: {
    name: string;
    sourceFolder: string;
    clientId: string;
    interval: "daily" | "weekly" | "monthly";
    time: string;
    dayOfWeek?: number;
    dayOfMonth?: number;
    exclusions?: Omit<SyncExclusion, "id">[];
  }): string {
    const id = this.generateId();
    const now = new Date();

    const schedule: SyncSchedule = {
      id,
      name: config.name,
      sourceFolder: config.sourceFolder,
      destinationFolder: this.generateDestinationPath(config.clientId),
      clientId: config.clientId,
      interval: config.interval,
      time: config.time,
      dayOfWeek: config.dayOfWeek,
      dayOfMonth: config.dayOfMonth,
      isActive: true,
      nextSync: this.calculateNextSync(
        config.interval,
        config.time,
        config.dayOfWeek,
        config.dayOfMonth,
      ),
      exclusions: (config.exclusions || []).map((exc) => ({
        ...exc,
        id: this.generateId(),
        isActive: true,
      })),
      createdAt: now,
      updatedAt: now,
      status: "pending",
      progress: 0,
      syncHistory: [],
    };

    this.syncSchedules.set(id, schedule);
    this.saveToStorage();
    this.scheduleSync(schedule);

    return id;
  }

  updateSyncSchedule(id: string, updates: Partial<SyncSchedule>): boolean {
    const schedule = this.syncSchedules.get(id);
    if (!schedule) return false;

    // Clear existing timer
    this.clearSyncTimer(id);

    // Update schedule
    const updatedSchedule = {
      ...schedule,
      ...updates,
      updatedAt: new Date(),
    };

    // Recalculate next sync if timing changed
    if (
      updates.interval ||
      updates.time ||
      updates.dayOfWeek ||
      updates.dayOfMonth
    ) {
      updatedSchedule.nextSync = this.calculateNextSync(
        updatedSchedule.interval,
        updatedSchedule.time,
        updatedSchedule.dayOfWeek,
        updatedSchedule.dayOfMonth,
      );
    }

    this.syncSchedules.set(id, updatedSchedule);
    this.saveToStorage();

    // Reschedule if active
    if (updatedSchedule.isActive) {
      this.scheduleSync(updatedSchedule);
    }

    return true;
  }

  getSyncSchedules(): SyncSchedule[] {
    return Array.from(this.syncSchedules.values()).sort(
      (a, b) => a.nextSync.getTime() - b.nextSync.getTime(),
    );
  }

  getSyncSchedule(id: string): SyncSchedule | undefined {
    return this.syncSchedules.get(id);
  }

  deleteSyncSchedule(id: string): boolean {
    const schedule = this.syncSchedules.get(id);
    if (!schedule) return false;

    this.clearSyncTimer(id);
    this.syncSchedules.delete(id);
    this.saveToStorage();

    return true;
  }

  pauseSyncSchedule(id: string): boolean {
    const schedule = this.syncSchedules.get(id);
    if (!schedule) return false;

    this.clearSyncTimer(id);
    schedule.isActive = false;
    schedule.status = "paused";
    schedule.updatedAt = new Date();

    this.syncSchedules.set(id, schedule);
    this.saveToStorage();

    return true;
  }

  resumeSyncSchedule(id: string): boolean {
    const schedule = this.syncSchedules.get(id);
    if (!schedule) return false;

    schedule.isActive = true;
    schedule.status = "pending";
    schedule.updatedAt = new Date();

    // Recalculate next sync
    schedule.nextSync = this.calculateNextSync(
      schedule.interval,
      schedule.time,
      schedule.dayOfWeek,
      schedule.dayOfMonth,
    );

    this.syncSchedules.set(id, schedule);
    this.saveToStorage();
    this.scheduleSync(schedule);

    return true;
  }

  addExclusion(
    scheduleId: string,
    exclusion: Omit<SyncExclusion, "id">,
  ): boolean {
    const schedule = this.syncSchedules.get(scheduleId);
    if (!schedule) return false;

    const newExclusion: SyncExclusion = {
      ...exclusion,
      id: this.generateId(),
      isActive: true,
    };

    schedule.exclusions.push(newExclusion);
    schedule.updatedAt = new Date();

    this.syncSchedules.set(scheduleId, schedule);
    this.saveToStorage();

    return true;
  }

  removeExclusion(scheduleId: string, exclusionId: string): boolean {
    const schedule = this.syncSchedules.get(scheduleId);
    if (!schedule) return false;

    schedule.exclusions = schedule.exclusions.filter(
      (exc) => exc.id !== exclusionId,
    );
    schedule.updatedAt = new Date();

    this.syncSchedules.set(scheduleId, schedule);
    this.saveToStorage();

    return true;
  }

  async runSyncNow(id: string): Promise<boolean> {
    const schedule = this.syncSchedules.get(id);
    if (!schedule) return false;

    return this.executeSync(schedule);
  }

  private calculateNextSync(
    interval: string,
    time: string,
    dayOfWeek?: number,
    dayOfMonth?: number,
  ): Date {
    const now = new Date();
    const [hours, minutes] = time.split(":").map(Number);

    let nextSync = new Date();
    nextSync.setHours(hours, minutes, 0, 0);

    switch (interval) {
      case "daily":
        if (nextSync <= now) {
          nextSync.setDate(nextSync.getDate() + 1);
        }
        break;

      case "weekly":
        if (dayOfWeek !== undefined) {
          const daysUntilTarget = (dayOfWeek - nextSync.getDay() + 7) % 7;
          if (daysUntilTarget === 0 && nextSync <= now) {
            nextSync.setDate(nextSync.getDate() + 7);
          } else {
            nextSync.setDate(nextSync.getDate() + daysUntilTarget);
          }
        }
        break;

      case "monthly":
        if (dayOfMonth !== undefined) {
          nextSync.setDate(dayOfMonth);
          if (nextSync <= now) {
            nextSync.setMonth(nextSync.getMonth() + 1);
            nextSync.setDate(dayOfMonth);
          }
        }
        break;
    }

    return nextSync;
  }

  private scheduleSync(schedule: SyncSchedule) {
    if (!schedule.isActive) return;

    const now = new Date();
    const delay = schedule.nextSync.getTime() - now.getTime();

    if (delay <= 0) {
      // Execute immediately if time has passed
      this.executeSync(schedule);
    } else {
      // Schedule for later
      const timer = setTimeout(() => {
        this.executeSync(schedule);
      }, delay);

      this.syncTimers.set(schedule.id, timer);
    }
  }

  private scheduleAllSyncs() {
    this.syncSchedules.forEach((schedule) => {
      if (schedule.isActive) {
        this.scheduleSync(schedule);
      }
    });
  }

  private clearSyncTimer(id: string) {
    const timer = this.syncTimers.get(id);
    if (timer) {
      clearTimeout(timer);
      this.syncTimers.delete(id);
    }
  }

  private async executeSync(schedule: SyncSchedule): Promise<boolean> {
    try {
      const historyEntry: SyncHistory = {
        id: this.generateId(),
        startTime: new Date(),
        status: "success",
        filesProcessed: 0,
        filesSynced: 0,
        filesSkipped: 0,
      };

      // Update status to syncing
      schedule.status = "syncing";
      schedule.progress = 0;
      schedule.lastSync = new Date();
      this.syncSchedules.set(schedule.id, schedule);
      this.saveToStorage();

      // Simulate sync process (replace with real API calls)
      await this.performFolderSync(schedule, historyEntry);

      // Mark as completed
      schedule.status = "completed";
      schedule.progress = 100;
      historyEntry.endTime = new Date();
      schedule.syncHistory.unshift(historyEntry);

      // Keep only last 10 history entries
      if (schedule.syncHistory.length > 10) {
        schedule.syncHistory = schedule.syncHistory.slice(0, 10);
      }

      // Calculate next sync
      schedule.nextSync = this.calculateNextSync(
        schedule.interval,
        schedule.time,
        schedule.dayOfWeek,
        schedule.dayOfMonth,
      );

      this.syncSchedules.set(schedule.id, schedule);
      this.saveToStorage();

      // Schedule next sync
      this.clearSyncTimer(schedule.id);
      this.scheduleSync(schedule);

      // Notify success
      this.notifySyncComplete(schedule, true);

      return true;
    } catch (error) {
      schedule.status = "failed";
      schedule.error = error instanceof Error ? error.message : "Sync failed";

      const historyEntry: SyncHistory = {
        id: this.generateId(),
        startTime: schedule.lastSync!,
        endTime: new Date(),
        status: "failed",
        filesProcessed: 0,
        filesSynced: 0,
        filesSkipped: 0,
        error: schedule.error,
      };

      schedule.syncHistory.unshift(historyEntry);
      this.syncSchedules.set(schedule.id, schedule);
      this.saveToStorage();

      this.notifySyncComplete(schedule, false);

      return false;
    }
  }

  private async performFolderSync(
    schedule: SyncSchedule,
    history: SyncHistory,
  ): Promise<void> {
    // Simulate folder sync with exclusions
    const totalFiles = Math.floor(Math.random() * 100) + 20;
    history.filesProcessed = totalFiles;

    for (let i = 0; i <= totalFiles; i++) {
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Check exclusions (simplified simulation)
      const shouldSkip = Math.random() < 0.1; // 10% skip rate

      if (shouldSkip) {
        history.filesSkipped++;
      } else {
        history.filesSynced++;
      }

      schedule.progress = Math.round((i / totalFiles) * 100);
      this.syncSchedules.set(schedule.id, schedule);
      this.saveToStorage();
    }
  }

  private notifySyncComplete(schedule: SyncSchedule, success: boolean) {
    if (Notification.permission === "granted") {
      new Notification(
        success ? "Folder Sync Complete" : "Folder Sync Failed",
        {
          body: success
            ? `Successfully synced folder "${schedule.name}"`
            : `Sync failed for folder "${schedule.name}": ${schedule.error}`,
          icon: "/favicon.ico",
        },
      );
    }
  }

  private saveToStorage() {
    try {
      const data = Array.from(this.syncSchedules.values()).map((schedule) => ({
        ...schedule,
        nextSync: schedule.nextSync.toISOString(),
        lastSync: schedule.lastSync?.toISOString(),
        createdAt: schedule.createdAt.toISOString(),
        updatedAt: schedule.updatedAt.toISOString(),
        syncHistory: schedule.syncHistory.map((h) => ({
          ...h,
          startTime: h.startTime.toISOString(),
          endTime: h.endTime?.toISOString(),
        })),
      }));

      localStorage.setItem("folder_sync_schedules", JSON.stringify(data));
    } catch (error) {
      console.error("Failed to save sync schedules:", error);
    }
  }

  private loadFromStorage() {
    try {
      const data = localStorage.getItem("folder_sync_schedules");
      if (!data) return;

      const schedules = JSON.parse(data);
      schedules.forEach((scheduleData: any) => {
        const schedule: SyncSchedule = {
          ...scheduleData,
          nextSync: new Date(scheduleData.nextSync),
          lastSync: scheduleData.lastSync
            ? new Date(scheduleData.lastSync)
            : undefined,
          createdAt: new Date(scheduleData.createdAt),
          updatedAt: new Date(scheduleData.updatedAt),
          syncHistory: scheduleData.syncHistory.map((h: any) => ({
            ...h,
            startTime: new Date(h.startTime),
            endTime: h.endTime ? new Date(h.endTime) : undefined,
          })),
        };

        this.syncSchedules.set(schedule.id, schedule);
      });
    } catch (error) {
      console.error("Failed to load sync schedules:", error);
    }
  }

  private generateDestinationPath(clientId: string): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");

    return `${clientId}/${year}/${month}`;
  }

  private generateId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Request notification permission
  async requestNotificationPermission(): Promise<boolean> {
    if ("Notification" in window) {
      const permission = await Notification.requestPermission();
      return permission === "granted";
    }
    return false;
  }
}

// Create singleton instance
export const folderSyncService = new FolderSyncService();
