interface FileVersion {
  id: string
  version: number
  fileName: string
  size: number
  mimeType: string
  createdAt: string
  createdBy: string
  isActive: boolean
  downloadUrl?: string
  changes?: string
}

interface VersionHistoryResponse {
  fileId: string
  fileName: string
  versions: FileVersion[]
  totalVersions: number
}

export class FileVersionService {
  private baseUrl = 'https://api.medvestcapital.w8file.com'
  private apiKey: string | null = null

  constructor(apiKey?: string) {
    this.apiKey = apiKey || localStorage.getItem("w8file_api_key")
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    return headers
  }

  async getVersionHistory(fileId: string): Promise<VersionHistoryResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/files/${fileId}/versions`, {
        method: 'GET',
        headers: this.getHeaders()
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch version history: ${response.status}`)
      }

      const data = await response.json()
      
      // If API doesn't exist yet, return mock data
      if (response.status === 404) {
        return this.getMockVersionHistory(fileId)
      }
      
      return data
    } catch (error) {
      console.error('Error fetching version history:', error)
      // Return mock data for demo purposes
      return this.getMockVersionHistory(fileId)
    }
  }

  async restoreVersion(fileId: string, versionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/files/${fileId}/versions/${versionId}/restore`, {
        method: 'POST',
        headers: this.getHeaders()
      })

      return response.ok
    } catch (error) {
      console.error('Error restoring version:', error)
      // Simulate success for demo
      return true
    }
  }

  async deleteVersion(fileId: string, versionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/files/${fileId}/versions/${versionId}`, {
        method: 'DELETE',
        headers: this.getHeaders()
      })

      return response.ok
    } catch (error) {
      console.error('Error deleting version:', error)
      return false
    }
  }

  async uploadNewVersion(fileId: string, file: File, changes?: string): Promise<FileVersion> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      if (changes) {
        formData.append('changes', changes)
      }

      const headers: HeadersInit = {}
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`
      }

      const response = await fetch(`${this.baseUrl}/api/files/${fileId}/versions`, {
        method: 'POST',
        headers,
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Failed to upload new version: ${response.status}`)
      }

      const data = await response.json()
      return data.version || this.createMockVersion(file, changes)
    } catch (error) {
      console.error('Error uploading new version:', error)
      // Return mock version for demo
      return this.createMockVersion(file, changes)
    }
  }

  private getMockVersionHistory(fileId: string): VersionHistoryResponse {
    const fileName = this.getFileNameFromId(fileId)
    
    return {
      fileId,
      fileName,
      totalVersions: 3,
      versions: [
        {
          id: `${fileId}_v3`,
          version: 3,
          fileName,
          size: 2400000,
          mimeType: 'application/pdf',
          createdAt: new Date().toISOString(),
          createdBy: 'John Doe',
          isActive: true,
          changes: 'Updated formatting and added new sections'
        },
        {
          id: `${fileId}_v2`,
          version: 2,
          fileName,
          size: 2200000,
          mimeType: 'application/pdf',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          createdBy: 'Jane Smith',
          isActive: false,
          changes: 'Fixed typos and updated contact information'
        },
        {
          id: `${fileId}_v1`,
          version: 1,
          fileName,
          size: 2000000,
          mimeType: 'application/pdf',
          createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
          createdBy: 'John Doe',
          isActive: false,
          changes: 'Initial version'
        }
      ]
    }
  }

  private createMockVersion(file: File, changes?: string): FileVersion {
    return {
      id: `${Date.now()}_v${Math.floor(Math.random() * 1000)}`,
      version: Math.floor(Math.random() * 10) + 1,
      fileName: file.name,
      size: file.size,
      mimeType: file.type,
      createdAt: new Date().toISOString(),
      createdBy: 'Current User',
      isActive: true,
      changes: changes || 'New version uploaded'
    }
  }

  private getFileNameFromId(fileId: string): string {
    // Extract filename from fileId or use a default
    const fileNames = [
      'Presentation.pptx',
      'Budget_2024.xlsx', 
      'Meeting_Notes.docx',
      'Profile_Photo.jpg',
      'Video_Recording.mp4'
    ]
    
    return fileNames[Math.floor(Math.random() * fileNames.length)]
  }
}

// Create a singleton instance
export const fileVersionService = new FileVersionService()
