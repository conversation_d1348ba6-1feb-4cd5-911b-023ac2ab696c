import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { EnhancedThemeProvider } from "@/components/EnhancedThemeProvider";
import { SplashScreen } from "@/components/SplashScreen";
import { AnimatePresence } from "framer-motion";
import { useWebSocket } from "@/hooks/useWebSocket";
import Index from "./pages/Index";
import Folder from "./pages/Folder";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => {
  const [isAppReady, setIsAppReady] = useState(false);
  const [docsLoaded, setDocsLoaded] = useState(false);

  // Use the real WebSocket connection
  const { isConnected, isSystemReady } = useWebSocket();

  // Simulate document loading after connection is established
  useEffect(() => {
    if (isConnected && !docsLoaded) {
      console.log("✅ WebSocket connected, loading documents...");

      // Simulate document loading (1-2 seconds)
      const docsTimeout = setTimeout(
        () => {
          setDocsLoaded(true);
          console.log("✅ Documents loaded");
        },
        1000 + Math.random() * 1000,
      );

      return () => clearTimeout(docsTimeout);
    }
  }, [isConnected, docsLoaded]);

  const handleLoadComplete = () => {
    setIsAppReady(true);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <EnhancedThemeProvider
        defaultTheme="auto"
        storageKey="w8file-theme"
        enableAnimations={true}
        enableSystemDetection={true}
      >
        <TooltipProvider>
          <Toaster />
          <Sonner />

          <AnimatePresence mode="wait">
            {!isAppReady ? (
              <SplashScreen
                key="splash"
                isConnected={isConnected}
                docsLoaded={docsLoaded}
                onLoadComplete={handleLoadComplete}
              />
            ) : (
              <BrowserRouter key="app">
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/folder/:folderId" element={<Folder />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            )}
          </AnimatePresence>
        </TooltipProvider>
      </EnhancedThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
