import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import { Search, Upload, Download, MoreHorizontal, Folder as FolderIcon, File, Grid3X3, List, CloudDrizzle, Plus, Filter, SortAsc, ArrowLeft, Home, ChevronRight, FolderSync, Calendar } from "lucide-react"
import { UploadDialog } from "@/components/UploadDialog"
import { ApiConfig } from "@/components/ApiConfig"
import { FolderSyncDialog } from "@/components/FolderSyncDialog"
import { SyncSchedulesDialog } from "@/components/SyncSchedulesDialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface FileItem {
  id: string
  name: string
  type: "folder" | "file"
  size?: string
  modified: string
  extension?: string
}

const folderContents: Record<string, FileItem[]> = {
  documents: [
    { id: "doc1", name: "Contracts", type: "folder", modified: "1 day ago" },
    { id: "doc2", name: "Invoices", type: "folder", modified: "3 days ago" },
    { id: "doc3", name: "Project_Proposal.docx", type: "file", size: "2.1 MB", modified: "2 hours ago", extension: "docx" },
    { id: "doc4", name: "Terms_and_Conditions.pdf", type: "file", size: "856 KB", modified: "1 week ago", extension: "pdf" },
  ],
  photos: [
    { id: "photo1", name: "Vacation 2024", type: "folder", modified: "2 weeks ago" },
    { id: "photo2", name: "Work Events", type: "folder", modified: "1 month ago" },
    { id: "photo3", name: "IMG_0001.jpg", type: "file", size: "3.2 MB", modified: "Yesterday", extension: "jpg" },
    { id: "photo4", name: "Screenshot_2024.png", type: "file", size: "1.8 MB", modified: "3 days ago", extension: "png" },
  ],
  projects: [
    { id: "proj1", name: "Website Redesign", type: "folder", modified: "1 day ago" },
    { id: "proj2", name: "Mobile App", type: "folder", modified: "5 days ago" },
    { id: "proj3", name: "Architecture_Diagram.png", type: "file", size: "4.1 MB", modified: "Yesterday", extension: "png" },
    { id: "proj4", name: "Requirements.docx", type: "file", size: "567 KB", modified: "2 days ago", extension: "docx" },
  ]
}

const getFileIcon = (item: FileItem) => {
  if (item.type === "folder") return <FolderIcon className="h-4 w-4 text-blue-500" />
  
  const iconClass = "h-4 w-4"
  switch (item.extension) {
    case "pdf": return <File className={cn(iconClass, "text-red-500")} />
    case "docx": return <File className={cn(iconClass, "text-blue-600")} />
    case "jpg": case "png": return <File className={cn(iconClass, "text-purple-500")} />
    default: return <File className={cn(iconClass, "text-gray-500")} />
  }
}

export default function Folder() {
  const { folderId } = useParams<{ folderId: string }>()
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [folderSyncDialogOpen, setFolderSyncDialogOpen] = useState(false)
  const [syncSchedulesOpen, setSyncSchedulesOpen] = useState(false)

  const folderName = folderId ? folderId.charAt(0).toUpperCase() + folderId.slice(1) : "Unknown"
  const files = folderId ? folderContents[folderId] || [] : []

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <CloudDrizzle className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  CloudVault
                </h1>
              </div>
              <Badge variant="secondary" className="ml-2">Personal</Badge>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="relative w-80 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search files and folders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <ApiConfig />
              <Button size="sm" className="gap-2" onClick={() => setFolderSyncDialogOpen(true)}>
                <FolderSync className="h-4 w-4" />
                Create Schedule
              </Button>
              <Button variant="outline" size="sm" className="gap-2" onClick={() => setFolderSyncDialogOpen(true)}>
                <FolderSync className="h-4 w-4" />
                Sync Folders
              </Button>
              <Button variant="outline" size="sm" className="gap-2" onClick={() => setSyncSchedulesOpen(true)}>
                <Calendar className="h-4 w-4" />
                Sync Schedules
              </Button>

            </div>
          </div>
        </div>
      </header>

      {/* Navigation Breadcrumb */}
      <div className="container max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back
              </Link>
            </Button>
            <div className="flex items-center gap-2 text-sm text-muted-foreground ml-4">
              <Link to="/" className="hover:text-foreground transition-colors">
                <Home className="h-4 w-4" />
              </Link>
              <ChevronRight className="h-4 w-4" />
              <span className="font-medium text-foreground">{folderName}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="gap-2">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button variant="ghost" size="sm" className="gap-2">
              <SortAsc className="h-4 w-4" />
              Sort
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center gap-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="container max-w-7xl mx-auto px-4 pb-8">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">{folderName} ({filteredFiles.length} items)</h2>
          </div>

          {filteredFiles.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="h-20 w-20 bg-muted rounded-full flex items-center justify-center">
                  <FolderIcon className="h-10 w-10 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">This folder is empty</h3>
                  <p className="text-muted-foreground">Create sync schedules to start mirroring files.</p>
                </div>
                <div className="flex gap-2">
                  <Button className="gap-2" onClick={() => setFolderSyncDialogOpen(true)}>
                    <FolderSync className="h-4 w-4" />
                    Create Schedule
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            <>
              {viewMode === "grid" ? (
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
                  {filteredFiles.map((file) => (
                    <Card key={file.id} className="group cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
                      <div className="p-4">
                        <div className="flex flex-col items-center space-y-3">
                          <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                            {getFileIcon(file)}
                          </div>
                          <div className="text-center w-full">
                            <p className="font-medium text-sm truncate" title={file.name}>
                              {file.name}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">{file.modified}</p>
                            {file.size && (
                              <p className="text-xs text-muted-foreground">{file.size}</p>
                            )}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem>Share</DropdownMenuItem>
                            <DropdownMenuItem>Rename</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <div className="p-1">
                    {filteredFiles.map((file, index) => (
                      <div key={file.id}>
                        <div className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-md cursor-pointer group">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            {getFileIcon(file)}
                            <div className="flex-1 min-w-0">
                              <p className="font-medium truncate">{file.name}</p>
                              <p className="text-sm text-muted-foreground">{file.modified}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            {file.size && (
                              <span className="text-sm text-muted-foreground">{file.size}</span>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuItem>Share</DropdownMenuItem>
                                <DropdownMenuItem>Rename</DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                        {index < filteredFiles.length - 1 && <Separator />}
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </>
          )}
        </div>
      </main>

      <UploadDialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen} />

      <FolderSyncDialog
        open={folderSyncDialogOpen}
        onOpenChange={setFolderSyncDialogOpen}
      />

      <SyncSchedulesDialog
        open={syncSchedulesOpen}
        onOpenChange={setSyncSchedulesOpen}
      />
    </div>
  )
}
