import { useState } from "react";
import {
  Search,
  FileText,
  Home,
  Settings,
  Activity,
  Calendar,
  BarChart3,
  Bell,
  Plus,
  Filter,
  TrendingUp,
  FolderSync,
  Palette,
  Save,
  BookOpen,
} from "lucide-react";

import { ServiceControl } from "@/components/ServiceControl";
import { UploadDialog } from "@/components/UploadDialog";
import { ApiConfig } from "@/components/ApiConfig";
import { VersionHistoryDialog } from "@/components/VersionHistoryDialog";
import { FolderSyncDialog } from "@/components/FolderSyncDialog";
import { SyncSchedulesDialog } from "@/components/SyncSchedulesDialog";
import { AdvancedThemeSelector } from "@/components/AdvancedThemeSelector";
import { CompanyInfo } from "@/components/CompanyInfo";

import { NotificationCenter } from "@/components/NotificationCenter";
import { ApiDocumentation } from "@/components/ApiDocumentation";
import { useWebSocket } from "@/hooks/useWebSocket";

import { ProgressTracker } from "@/components/ProgressTracker";
import { EventLog } from "@/components/EventLog";
import { UpcomingSchedules } from "@/components/UpcomingSchedules";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";

export default function Index() {
  const [searchQuery, setSearchQuery] = useState("");
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [versionHistoryOpen, setVersionHistoryOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [folderSyncDialogOpen, setFolderSyncDialogOpen] = useState(false);
  const [syncSchedulesOpen, setSyncSchedulesOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("dashboard");
  const [settingsSubsection, setSettingsSubsection] = useState("general");

  // Get WebSocket connection and data
  const { isConnected, serviceStatus, systemMetrics, sendServiceCommand } =
    useWebSocket();

  const navigationItems = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: Home,
      description: "Overview and activity",
      color: "from-blue-500 to-cyan-500",
    },
    {
      id: "settings",
      label: "Settings",
      icon: Settings,
      description: "Configuration & themes",
      color: "from-gray-500 to-slate-500",
    },
  ];

  return (
    <div className="min-h-screen bg-background flex">
      {/* Enhanced Sidebar Navigation */}
      <aside className="sidebar w-72 min-h-screen flex-shrink-0 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-primary to-secondary" />
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]" />
        </div>

        {/* App Header in Sidebar */}
        <div className="p-6 border-b border-sidebar-border relative flex flex-col">
          <img
            loading="lazy"
            srcSet="https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=100 100w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=200 200w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=400 400w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=800 800w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=1200 1200w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=1600 1600w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128?width=2000 2000w, https://cdn.builder.io/api/v1/image/assets%2F66c45a672f0647d19eada36290429cd7%2F21d29abe78274ab8b58270bf2d27e128"
            className="w-[150px] h-auto max-w-[500px] mx-auto mt-5 aspect-square object-cover object-center"
            alt="App Logo"
          />
        </div>

        {/* Navigation Menu */}
        <nav className="p-4 space-y-2">
          {navigationItems.map((item, index) => {
            const IconComponent = item.icon;
            const isActive = activeSection === item.id;

            return (
              <motion.button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`sidebar-item w-full text-left p-4 rounded-xl transition-all duration-300 group relative overflow-hidden ${
                  isActive ? "active" : ""
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Background gradient for active state */}
                {isActive && (
                  <motion.div
                    className={`absolute inset-0 bg-gradient-to-r ${item.color} opacity-10 rounded-xl`}
                    layoutId="activeBackground"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}

                <div className="flex items-center gap-3 relative z-10">
                  <motion.div
                    className={`p-2 rounded-lg transition-colors ${
                      isActive
                        ? "bg-gradient-to-r " +
                          item.color +
                          " text-white shadow-lg"
                        : "bg-sidebar-foreground/10 text-sidebar-foreground/70 group-hover:bg-sidebar-accent/10"
                    }`}
                    whileHover={{ rotate: isActive ? 0 : 10 }}
                  >
                    <IconComponent className="h-5 w-5" />
                  </motion.div>
                  <div className="flex-1">
                    <div
                      className={`text-label-large font-semibold transition-colors ${
                        isActive
                          ? "text-sidebar-accent-foreground"
                          : "text-sidebar-foreground"
                      }`}
                    >
                      {item.label}
                    </div>
                    <div
                      className={`text-caption transition-colors ${
                        isActive
                          ? "text-sidebar-accent-foreground/80"
                          : "text-sidebar-foreground/50"
                      }`}
                    >
                      {item.description}
                    </div>
                  </div>
                  {isActive && (
                    <motion.div
                      className="w-2 h-2 bg-primary rounded-full"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 }}
                    />
                  )}
                </div>
              </motion.button>
            );
          })}
        </nav>
      </aside>

      {/* Main Application Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Enhanced Top Bar */}
        <header className="glass border-b h-20 flex items-center justify-between px-8 sticky top-0 z-40 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/3 via-transparent to-secondary/3" />

          <motion.div
            className="flex items-center gap-6 relative z-10"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div>
              <h2 className="text-headline text-gradient capitalize flex items-center gap-3">
                {activeSection}
              </h2>
            </div>
          </motion.div>

          <motion.div
            className="flex items-center gap-4 relative z-10"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Filter Button */}
            <div className="relative w-96 group">
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <NotificationCenter />
              <ApiConfig />
            </div>
          </motion.div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-8 bg-background-secondary/30 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/1 via-transparent to-secondary/1" />

          <div className="relative z-10">
            <AnimatePresence mode="wait">
              {activeSection === "dashboard" && (
                <motion.div
                  key="dashboard"
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.6 }}
                >
                  {/* Service Control Section */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <ServiceControl
                      isConnected={isConnected}
                      serviceStatus={serviceStatus}
                      systemMetrics={systemMetrics}
                      onSendCommand={sendServiceCommand}
                    />
                  </motion.div>

                  {/* Dashboard Grid */}
                  <motion.div
                    className="grid grid-cols-1 xl:grid-cols-2 gap-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <ProgressTracker />
                    <EventLog />
                  </motion.div>

                  {/* Upcoming Schedules */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <UpcomingSchedules
                      onCreateScheduleClick={() => setSyncSchedulesOpen(true)}
                    />
                  </motion.div>
                </motion.div>
              )}

              {activeSection === "settings" && (
                <motion.div
                  key="settings"
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.6 }}
                >
                  {/* Settings Navigation */}
                  <div className="flex gap-2 mb-8">
                    <Button
                      variant={
                        settingsSubsection === "general" ? "default" : "outline"
                      }
                      onClick={() => setSettingsSubsection("general")}
                      className="gap-2"
                    >
                      <Settings className="h-4 w-4" />
                      General
                    </Button>
                    <Button
                      variant={
                        settingsSubsection === "appearance"
                          ? "default"
                          : "outline"
                      }
                      onClick={() => setSettingsSubsection("appearance")}
                      className="gap-2"
                    >
                      <Palette className="h-4 w-4" />
                      Appearance
                    </Button>
                    <Button
                      variant={
                        settingsSubsection === "help" ? "default" : "outline"
                      }
                      onClick={() => setSettingsSubsection("help")}
                      className="gap-2"
                    >
                      <BookOpen className="h-4 w-4" />
                      API Documentation
                    </Button>
                  </div>

                  {/* Settings Content */}
                  <AnimatePresence mode="wait">
                    {settingsSubsection === "general" && (
                      <motion.div
                        key="general"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.4 }}
                        className="space-y-8"
                      >
                        {/* Company Information */}
                        <CompanyInfo />

                        {/* Application Settings */}
                        <div className="panel p-8">
                          <div className="flex items-center gap-4 mb-6">
                            <div className="p-3 bg-gray-500/10 rounded-xl">
                              <Settings className="h-6 w-6 text-gray-500" />
                            </div>
                            <div>
                              <h3 className="text-title-large text-gradient">
                                Application Settings
                              </h3>
                              <p className="text-body text-muted-foreground">
                                Configure your W8 File Importer settings
                              </p>
                            </div>
                          </div>
                          <div className="max-w-2xl">
                            <ApiConfig />
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {settingsSubsection === "appearance" && (
                      <motion.div
                        key="appearance"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.4 }}
                      >
                        {/* Theme & Appearance */}
                        <div className="panel p-8">
                          <div className="flex items-center gap-4 mb-6">
                            <div className="p-3 bg-purple-500/10 rounded-xl">
                              <Palette className="h-6 w-6 text-purple-500" />
                            </div>
                            <div>
                              <h3 className="text-title-large text-gradient">
                                Theme & Appearance
                              </h3>
                              <p className="text-body text-muted-foreground">
                                Customize the application appearance and
                                interface preferences
                              </p>
                            </div>
                          </div>
                          <div className="space-y-6">
                            <div>
                              <h4 className="text-title text-gradient mb-3">
                                Advanced Theme Studio
                              </h4>
                              <p className="text-caption text-muted-foreground mb-4">
                                Access the full theme customization experience
                                with live previews, custom theme builder, and
                                theme management tools.
                              </p>
                              <AdvancedThemeSelector />
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {settingsSubsection === "help" && (
                      <motion.div
                        key="help"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.4 }}
                      >
                        <div className="panel p-8">
                          <ApiDocumentation />
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </main>
      </div>

      {/* Dialogs */}
      <UploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
      />

      <FolderSyncDialog
        open={folderSyncDialogOpen}
        onOpenChange={setFolderSyncDialogOpen}
      />

      <SyncSchedulesDialog
        open={syncSchedulesOpen}
        onOpenChange={setSyncSchedulesOpen}
      />

      {selectedFile && (
        <VersionHistoryDialog
          open={versionHistoryOpen}
          onOpenChange={setVersionHistoryOpen}
          fileId={selectedFile.id}
          fileName={selectedFile.name}
        />
      )}
    </div>
  );
}
