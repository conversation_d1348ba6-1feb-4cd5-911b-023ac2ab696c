import { useState, useEffect } from "react";
import {
  FolderSync,
  Clock,
  Calendar,
  Plus,
  Minus,
  <PERSON>ting<PERSON>,
  AlertTriangle,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { folderSyncService } from "@/services/folderSyncService";

interface FolderSyncDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface SyncExclusion {
  id: string;
  type: "file" | "folder" | "pattern";
  value: string;
  isActive: boolean;
}

const mockFolders = [
  { id: "documents", name: "Documents", path: "/Documents" },
  { id: "photos", name: "Photos", path: "/Photos" },
  { id: "projects", name: "Projects", path: "/Projects" },
  { id: "downloads", name: "Downloads", path: "/Downloads" },
  { id: "desktop", name: "Desktop", path: "/Desktop" },
];

const intervalOptions = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
];

const dayOfWeekOptions = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

const mockClients = [
  { id: "ClientA", name: "Client A - Acme Corp" },
  { id: "ClientB", name: "Client B - TechStart" },
  { id: "ClientC", name: "Client C - Global Industries" },
  { id: "Internal", name: "Internal - Company Files" },
];

export function FolderSyncDialog({
  open,
  onOpenChange,
}: FolderSyncDialogProps) {
  const [scheduleName, setScheduleName] = useState("");
  const [clientId, setClientId] = useState("");
  const [sourceFolder, setSourceFolder] = useState("");

  const [interval, setInterval] = useState<"daily" | "weekly" | "monthly">(
    "daily",
  );
  const [time, setTime] = useState("02:00");
  const [dayOfWeek, setDayOfWeek] = useState<number>(0);
  const [dayOfMonth, setDayOfMonth] = useState<number>(1);
  const [exclusions, setExclusions] = useState<Omit<SyncExclusion, "id">[]>([]);
  const [newExclusion, setNewExclusion] = useState({
    type: "file" as const,
    value: "",
  });
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);

  const addExclusion = () => {
    if (newExclusion.value.trim()) {
      setExclusions((prev) => [
        ...prev,
        {
          ...newExclusion,
          value: newExclusion.value.trim(),
          isActive: true,
        },
      ]);
      setNewExclusion({ type: "file", value: "" });
    }
  };

  const removeExclusion = (index: number) => {
    setExclusions((prev) => prev.filter((_, i) => i !== index));
  };

  const handleCreate = () => {
    if (!scheduleName.trim() || !clientId || !sourceFolder) {
      alert(
        "Please provide a schedule name, select a client, and select a source folder",
      );
      return;
    }

    const config = {
      name: scheduleName.trim(),
      clientId,
      sourceFolder,

      interval,
      time,
      dayOfWeek: interval === "weekly" ? dayOfWeek : undefined,
      dayOfMonth: interval === "monthly" ? dayOfMonth : undefined,
      exclusions,
    };

    const scheduleId = folderSyncService.createSyncSchedule(config);
    alert(`Folder sync schedule created successfully! ID: ${scheduleId}`);
    handleClose();
  };

  const handleClose = () => {
    setScheduleName("");
    setClientId("");
    setSourceFolder("");

    setInterval("daily");
    setTime("02:00");
    setDayOfWeek(0);
    setDayOfMonth(1);
    setExclusions([]);
    setNewExclusion({ type: "file", value: "" });
    onOpenChange(false);
  };

  const enableNotifications = async () => {
    const granted = await folderSyncService.requestNotificationPermission();
    setNotificationsEnabled(granted);
    if (!granted) {
      alert(
        "Notifications permission denied. You won't receive sync completion notifications.",
      );
    }
  };

  const getSchedulePreview = () => {
    if (!clientId || !sourceFolder)
      return "Please select client and source folder";

    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const clientName =
      mockClients.find((c) => c.id === clientId)?.name || clientId;

    let preview = `Sync "${sourceFolder}" to ${clientId}/${year}/${month}/[auto-organized] - ${interval}`;

    if (interval === "weekly") {
      const dayName = dayOfWeekOptions.find(
        (d) => d.value === dayOfWeek,
      )?.label;
      preview += ` on ${dayName}`;
    } else if (interval === "monthly") {
      preview += ` on day ${dayOfMonth}`;
    }

    preview += ` at ${time}`;

    return preview;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FolderSync className="h-5 w-5" />
            Create Folder Sync Schedule
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 flex-1 overflow-y-auto pr-2 dialog-scrollbar">
          {/* Basic Configuration */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="scheduleName">
                Schedule Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="scheduleName"
                value={scheduleName}
                onChange={(e) => setScheduleName(e.target.value)}
                placeholder="e.g., Daily Documents Backup"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="clientId">
                Client <span className="text-red-500">*</span>
              </Label>
              <Select value={clientId} onValueChange={setClientId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select client" />
                </SelectTrigger>
                <SelectContent>
                  {mockClients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="sourceFolder">
                  Source Folder <span className="text-red-500">*</span>
                </Label>
                <Select value={sourceFolder} onValueChange={setSourceFolder}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source folder" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockFolders.map((folder) => (
                      <SelectItem key={folder.id} value={folder.path}>
                        {folder.name} ({folder.path})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Schedule Configuration */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <h3 className="font-medium">Schedule Settings</h3>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="interval">Interval</Label>
                <Select
                  value={interval}
                  onValueChange={(value: any) => setInterval(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {intervalOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={time}
                  onChange={(e) => setTime(e.target.value)}
                />
              </div>

              {interval === "weekly" && (
                <div className="space-y-2">
                  <Label htmlFor="dayOfWeek">Day of Week</Label>
                  <Select
                    value={dayOfWeek.toString()}
                    onValueChange={(value) => setDayOfWeek(Number(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {dayOfWeekOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {interval === "monthly" && (
                <div className="space-y-2">
                  <Label htmlFor="dayOfMonth">Day of Month</Label>
                  <Input
                    id="dayOfMonth"
                    type="number"
                    min="1"
                    max="31"
                    value={dayOfMonth}
                    onChange={(e) => setDayOfMonth(Number(e.target.value))}
                  />
                </div>
              )}
            </div>

            {/* Schedule Preview */}
            <Alert>
              <Calendar className="h-4 w-4" />
              <AlertDescription>
                <strong>Schedule Preview:</strong> {getSchedulePreview()}
              </AlertDescription>
            </Alert>
          </div>

          <Separator />

          {/* Exclusions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <h3 className="font-medium">Exclusions</h3>
            </div>

            <div className="space-y-3">
              <div className="flex gap-2">
                <Select
                  value={newExclusion.type}
                  onValueChange={(value: any) =>
                    setNewExclusion((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="file">File</SelectItem>
                    <SelectItem value="folder">Folder</SelectItem>
                    <SelectItem value="pattern">Pattern</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder={
                    newExclusion.type === "file"
                      ? "filename.ext"
                      : newExclusion.type === "folder"
                        ? "folder-name"
                        : "*.tmp, temp*, etc."
                  }
                  value={newExclusion.value}
                  onChange={(e) =>
                    setNewExclusion((prev) => ({
                      ...prev,
                      value: e.target.value,
                    }))
                  }
                  className="flex-1"
                />
                <Button onClick={addExclusion} size="sm" className="gap-1">
                  <Plus className="h-3 w-3" />
                  Add
                </Button>
              </div>

              {exclusions.length > 0 && (
                <ScrollArea className="h-32">
                  <div className="space-y-2">
                    {exclusions.map((exclusion, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 p-2 border rounded"
                      >
                        <Badge variant="outline" className="text-xs">
                          {exclusion.type.toUpperCase()}
                        </Badge>
                        <span className="flex-1 text-sm font-mono">
                          {exclusion.value}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeExclusion(index)}
                          className="gap-1"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>

            <div className="text-sm text-muted-foreground">
              <p>
                <strong>File:</strong> Exclude specific files (e.g., "temp.log")
              </p>
              <p>
                <strong>Folder:</strong> Exclude entire folders (e.g.,
                "node_modules")
              </p>
              <p>
                <strong>Pattern:</strong> Use wildcards (e.g., "*.tmp", "temp*",
                ".*")
              </p>
            </div>
          </div>

          <Separator />

          {/* Notifications */}
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Notifications</h4>
                <p className="text-sm text-muted-foreground">
                  Get notified when sync operations complete
                </p>
              </div>
              <Button
                variant={notificationsEnabled ? "default" : "outline"}
                size="sm"
                onClick={enableNotifications}
                className="gap-2"
              >
                <Settings className="h-4 w-4" />
                {notificationsEnabled ? "Enabled" : "Enable"}
              </Button>
            </div>
          </Card>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t border-border/50 flex-shrink-0 bg-background">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreate}
            disabled={!scheduleName.trim() || !clientId || !sourceFolder}
            className="gap-2"
          >
            <FolderSync className="h-4 w-4" />
            Create Schedule
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
