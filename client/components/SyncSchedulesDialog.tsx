import { useState, useEffect } from "react";
import {
  FolderSync,
  Play,
  Pause,
  Trash2,
  Edit,
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCw,
  Settings,
  Plus,
} from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { folderSyncService } from "@/services/folderSyncService";
import { ScheduleCreationForm } from "@/components/ScheduleCreationForm";

interface SyncSchedulesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface SyncSchedule {
  id: string;
  name: string;
  sourceFolder: string;
  destinationFolder?: string;
  clientId: string;
  interval: "daily" | "weekly" | "monthly";
  time: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  isActive: boolean;
  lastSync?: Date;
  nextSync: Date;
  exclusions: any[];
  createdAt: Date;
  updatedAt: Date;
  status: "pending" | "syncing" | "completed" | "failed" | "paused";
  progress?: number;
  error?: string;
  syncHistory: any[];
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "pending":
      return <Clock className="h-4 w-4 text-blue-500" />;
    case "syncing":
      return <RotateCw className="h-4 w-4 text-yellow-500 animate-spin" />;
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "failed":
      return <XCircle className="h-4 w-4 text-red-500" />;
    case "paused":
      return <Pause className="h-4 w-4 text-gray-500" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
    case "syncing":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
    case "completed":
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
    case "failed":
      return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
    case "paused":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  }
};

const dayOfWeekOptions = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

export function SyncSchedulesDialog({
  open,
  onOpenChange,
}: SyncSchedulesDialogProps) {
  const [schedules, setSchedules] = useState<SyncSchedule[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (open) {
      loadSchedules();

      // Set up polling to refresh schedules
      const interval = setInterval(loadSchedules, 5000);
      return () => clearInterval(interval);
    }
  }, [open]);

  const loadSchedules = async () => {
    setLoading(true);
    try {
      // Try to load from API first
      const response = await fetch("/api/schedules");
      if (
        response.ok &&
        response.headers.get("content-type")?.includes("application/json")
      ) {
        const data = await response.json();
        setSchedules(data.schedules || []);
        console.log(`Loaded ${data.schedules?.length || 0} schedules from API`);
      } else {
        console.log("API not available, using local service");
        // Fallback to local service
        const syncSchedules = folderSyncService.getSyncSchedules();
        setSchedules(syncSchedules);
      }
    } catch (error) {
      console.log("API request failed, using local service fallback:", error);
      // Fallback to local service
      try {
        const syncSchedules = folderSyncService.getSyncSchedules();
        setSchedules(syncSchedules);
      } catch (fallbackError) {
        console.error("Both API and fallback failed:", fallbackError);
        setSchedules([]); // Empty array as last resort
      }
    } finally {
      setLoading(false);
    }
  };

  const createSchedule = async (scheduleData: any) => {
    setSubmitting(true);
    try {
      const response = await fetch("/api/schedules", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(scheduleData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Schedule created:", data.schedule);
        await loadSchedules(); // Reload schedules
        setShowCreateForm(false);
        return true;
      } else {
        const error = await response.json();
        console.error("Failed to create schedule:", error);
        alert(`Failed to create schedule: ${error.error}`);
        return false;
      }
    } catch (error) {
      console.error("Error creating schedule:", error);
      alert("Failed to create schedule. Please try again.");
      return false;
    } finally {
      setSubmitting(false);
    }
  };

  const handlePause = (id: string) => {
    const success = folderSyncService.pauseSyncSchedule(id);
    if (success) {
      loadSchedules();
    } else {
      alert("Failed to pause sync schedule");
    }
  };

  const handleResume = (id: string) => {
    const success = folderSyncService.resumeSyncSchedule(id);
    if (success) {
      loadSchedules();
    } else {
      alert("Failed to resume sync schedule");
    }
  };

  const handleDelete = (id: string) => {
    if (
      confirm(
        "Are you sure you want to delete this sync schedule? This action cannot be undone.",
      )
    ) {
      const success = folderSyncService.deleteSyncSchedule(id);
      if (success) {
        loadSchedules();
      } else {
        alert("Failed to delete sync schedule");
      }
    }
  };

  const handleRunNow = async (id: string) => {
    try {
      await folderSyncService.runSyncNow(id);
      loadSchedules();
    } catch (error) {
      alert("Failed to start sync");
    }
  };

  const formatDateTime = (date: Date) => {
    return date.toLocaleString();
  };

  const getTimeRemaining = (nextSync: Date) => {
    const now = new Date();
    const diff = nextSync.getTime() - now.getTime();

    if (diff <= 0) return "Overdue";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const formatSchedule = (schedule: SyncSchedule) => {
    let text = `${schedule.interval} at ${schedule.time}`;

    if (schedule.interval === "weekly" && schedule.dayOfWeek !== undefined) {
      text += ` on ${dayOfWeekOptions[schedule.dayOfWeek]}`;
    } else if (
      schedule.interval === "monthly" &&
      schedule.dayOfMonth !== undefined
    ) {
      text += ` on day ${schedule.dayOfMonth}`;
    }

    return text;
  };

  const activeSchedules = schedules.filter(
    (s) => s.isActive && s.status !== "paused",
  );
  const pausedSchedules = schedules.filter((s) => s.status === "paused");
  const syncingSchedules = schedules.filter((s) => s.status === "syncing");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FolderSync className="h-5 w-5" />
            Folder Sync Schedules ({schedules.length})
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-y-auto dialog-scrollbar">
          {/* Summary Stats */}
          <div className="grid grid-cols-4 gap-4">
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-green-600">
                {activeSchedules.length}
              </div>
              <div className="text-xs text-muted-foreground">Active</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {syncingSchedules.length}
              </div>
              <div className="text-xs text-muted-foreground">Syncing</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-gray-600">
                {pausedSchedules.length}
              </div>
              <div className="text-xs text-muted-foreground">Paused</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {schedules.filter((s) => s.status === "completed").length}
              </div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </Card>
          </div>

          <Tabs defaultValue="active" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="create" className="text-xs">
                <Plus className="h-3 w-3 mr-1" />
                Create
              </TabsTrigger>
              <TabsTrigger value="active" className="text-xs">
                Active
              </TabsTrigger>
              <TabsTrigger value="history" className="text-xs">
                History
              </TabsTrigger>
              <TabsTrigger value="all" className="text-xs">
                All
              </TabsTrigger>
            </TabsList>

            <TabsContent value="create" className="space-y-4">
              <ScheduleCreationForm
                onSubmit={createSchedule}
                onCancel={() => setShowCreateForm(false)}
                loading={submitting}
              />
            </TabsContent>

            <TabsContent value="active" className="space-y-4">
              <ScrollArea className="h-[400px]">
                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-muted-foreground">
                      Loading sync schedules...
                    </div>
                  </div>
                ) : activeSchedules.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <FolderSync className="h-12 w-12 text-muted-foreground mb-2" />
                    <h3 className="font-medium">No active sync schedules</h3>
                    <p className="text-sm text-muted-foreground">
                      Create a new folder sync schedule to get started
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {activeSchedules.map((schedule) => (
                      <Card key={schedule.id} className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(schedule.status)}
                                <h3 className="font-medium">{schedule.name}</h3>
                                <Badge
                                  className={getStatusColor(schedule.status)}
                                >
                                  {schedule.status.toUpperCase()}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {schedule.sourceFolder} → {schedule.clientId}
                                /[auto-organized]
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatSchedule(schedule)} • Next:{" "}
                                {getTimeRemaining(schedule.nextSync)}
                              </p>
                            </div>

                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRunNow(schedule.id)}
                                disabled={schedule.status === "syncing"}
                                className="gap-1"
                              >
                                <Play className="h-3 w-3" />
                                Sync Now
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePause(schedule.id)}
                                className="gap-1"
                              >
                                <Pause className="h-3 w-3" />
                                Pause
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(schedule.id)}
                                className="gap-1 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {schedule.status === "syncing" && (
                            <div className="space-y-2">
                              <Progress
                                value={schedule.progress || 0}
                                className="w-full"
                              />
                              <div className="text-xs text-muted-foreground">
                                {schedule.progress || 0}% complete
                              </div>
                            </div>
                          )}

                          {schedule.exclusions.length > 0 && (
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Settings className="h-3 w-3" />
                              {schedule.exclusions.length} exclusion
                              {schedule.exclusions.length !== 1 ? "s" : ""}{" "}
                              configured
                            </div>
                          )}

                          {schedule.error && (
                            <Alert variant="destructive" className="mt-2">
                              <AlertDescription className="text-xs">
                                {schedule.error}
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {schedules
                    .filter((s) => s.syncHistory.length > 0)
                    .map((schedule) => (
                      <Card key={schedule.id} className="p-4">
                        <div className="space-y-2">
                          <h3 className="font-medium">{schedule.name}</h3>
                          <div className="space-y-1">
                            {schedule.syncHistory
                              .slice(0, 3)
                              .map((history, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between text-xs"
                                >
                                  <div className="flex items-center gap-2">
                                    {history.status === "success" ? (
                                      <CheckCircle className="h-3 w-3 text-green-500" />
                                    ) : (
                                      <XCircle className="h-3 w-3 text-red-500" />
                                    )}
                                    <span>
                                      {formatDateTime(
                                        new Date(history.startTime),
                                      )}
                                    </span>
                                  </div>
                                  <span className="text-muted-foreground">
                                    {history.filesSynced}/
                                    {history.filesProcessed} files
                                  </span>
                                </div>
                              ))}
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="all" className="space-y-4">
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {schedules.map((schedule) => (
                    <Card key={schedule.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(schedule.status)}
                            <h3 className="font-medium">{schedule.name}</h3>
                            <Badge className={getStatusColor(schedule.status)}>
                              {schedule.status.toUpperCase()}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {schedule.sourceFolder} → {schedule.clientId}
                            /[auto-organized]
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Created: {formatDateTime(schedule.createdAt)}
                          </p>
                        </div>

                        <div className="flex items-center gap-2">
                          {schedule.status === "paused" ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleResume(schedule.id)}
                              className="gap-1"
                            >
                              <Play className="h-3 w-3" />
                              Resume
                            </Button>
                          ) : schedule.isActive ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePause(schedule.id)}
                              className="gap-1"
                            >
                              <Pause className="h-3 w-3" />
                              Pause
                            </Button>
                          ) : null}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(schedule.id)}
                            className="gap-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end pt-4 border-t border-border/50 flex-shrink-0 bg-background">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
