import { Palette } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTheme } from "@/components/ThemeProvider";

const themes = [
  {
    name: "default",
    label: "Modern Light",
    color: "bg-blue-500",
    description: "Clean and professional",
  },
  {
    name: "arctic",
    label: "Arctic Blue",
    color: "bg-cyan-500",
    description: "Cool and crisp",
  },
  {
    name: "forest",
    label: "Forest Green",
    color: "bg-green-600",
    description: "Natural and calming",
  },
  {
    name: "sunset",
    label: "Sunset Orange",
    color: "bg-orange-500",
    description: "Warm and energetic",
  },
  {
    name: "lavender",
    label: "Lavender Purple",
    color: "bg-purple-500",
    description: "Elegant and refined",
  },
  {
    name: "midnight",
    label: "Midnight Dark",
    color: "bg-slate-800",
    description: "Rich and immersive",
  },
] as const;

export function ThemeSelector() {
  const { theme, setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Palette className="h-4 w-4" />
          Theme
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        {themes.map((themeOption) => (
          <DropdownMenuItem
            key={themeOption.name}
            onClick={() => setTheme(themeOption.name as any)}
            className="flex items-center gap-3 p-3 cursor-pointer"
          >
            <div
              className={`w-5 h-5 rounded-full ${themeOption.color} ring-2 ring-offset-2 ring-offset-background ${theme === themeOption.name ? "ring-primary" : "ring-transparent"}`}
            />
            <div className="flex-1">
              <div className="font-medium">{themeOption.label}</div>
              <div className="text-xs text-muted-foreground">
                {themeOption.description}
              </div>
            </div>
            {theme === themeOption.name && (
              <span className="text-primary text-sm font-medium">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
