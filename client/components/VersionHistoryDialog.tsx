import { useState, useEffect } from "react"
import { Clock, Download, RotateCcw, Trash2, User, FileText, AlertCircle, Upload } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { fileVersionService } from "@/services/fileVersionService"
import { cn } from "@/lib/utils"

interface VersionHistoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  fileId: string
  fileName: string
}

interface FileVersion {
  id: string
  version: number
  fileName: string
  size: number
  mimeType: string
  createdAt: string
  createdBy: string
  isActive: boolean
  downloadUrl?: string
  changes?: string
}

export function VersionHistoryDialog({ open, onOpenChange, fileId, fileName }: VersionHistoryDialogProps) {
  const [versions, setVersions] = useState<FileVersion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploadFile, setUploadFile] = useState<File | null>(null)
  const [uploadChanges, setUploadChanges] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)

  useEffect(() => {
    if (open && fileId) {
      loadVersionHistory()
    }
  }, [open, fileId])

  const loadVersionHistory = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fileVersionService.getVersionHistory(fileId)
      setVersions(response.versions)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load version history')
    } finally {
      setLoading(false)
    }
  }

  const handleRestore = async (versionId: string, version: number) => {
    try {
      const success = await fileVersionService.restoreVersion(fileId, versionId)
      if (success) {
        // Refresh version history
        await loadVersionHistory()
        alert(`Version ${version} has been restored as the current version`)
      } else {
        alert('Failed to restore version')
      }
    } catch (error) {
      alert('Error restoring version')
    }
  }

  const handleDelete = async (versionId: string, version: number) => {
    if (confirm(`Are you sure you want to delete version ${version}? This action cannot be undone.`)) {
      try {
        const success = await fileVersionService.deleteVersion(fileId, versionId)
        if (success) {
          await loadVersionHistory()
        } else {
          alert('Failed to delete version')
        }
      } catch (error) {
        alert('Error deleting version')
      }
    }
  }

  const handleUploadNewVersion = async () => {
    if (!uploadFile) return

    setUploadLoading(true)
    try {
      await fileVersionService.uploadNewVersion(fileId, uploadFile, uploadChanges)
      setShowUploadForm(false)
      setUploadFile(null)
      setUploadChanges("")
      await loadVersionHistory()
    } catch (error) {
      alert('Failed to upload new version')
    } finally {
      setUploadLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Version History - {fileName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {versions.length} version{versions.length !== 1 ? 's' : ''} found
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUploadForm(!showUploadForm)}
              className="gap-2"
            >
              <Upload className="h-4 w-4" />
              Upload New Version
            </Button>
          </div>

          {showUploadForm && (
            <Card className="p-4">
              <div className="space-y-4">
                <h3 className="font-medium">Upload New Version</h3>
                <div className="space-y-2">
                  <Label htmlFor="version-file">File</Label>
                  <Input
                    id="version-file"
                    type="file"
                    onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="version-changes">Changes (optional)</Label>
                  <Textarea
                    id="version-changes"
                    placeholder="Describe what changed in this version..."
                    value={uploadChanges}
                    onChange={(e) => setUploadChanges(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleUploadNewVersion}
                    disabled={!uploadFile || uploadLoading}
                    size="sm"
                  >
                    {uploadLoading ? "Uploading..." : "Upload"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowUploadForm(false)}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </Card>
          )}

          <ScrollArea className="h-[400px]">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-muted-foreground">Loading version history...</div>
              </div>
            ) : (
              <div className="space-y-3">
                {versions.map((version, index) => (
                  <Card key={version.id} className={cn(
                    "p-4",
                    version.isActive && "ring-2 ring-primary"
                  )}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={version.isActive ? "default" : "secondary"}>
                            Version {version.version}
                          </Badge>
                          {version.isActive && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              Current
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {version.createdBy}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(version.createdAt)}
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {formatFileSize(version.size)}
                          </div>
                        </div>

                        {version.changes && (
                          <div className="text-sm">
                            <span className="font-medium">Changes: </span>
                            <span className="text-muted-foreground">{version.changes}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button variant="ghost" size="sm" className="gap-1">
                          <Download className="h-3 w-3" />
                          Download
                        </Button>
                        
                        {!version.isActive && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="gap-1"
                            onClick={() => handleRestore(version.id, version.version)}
                          >
                            <RotateCcw className="h-3 w-3" />
                            Restore
                          </Button>
                        )}
                        
                        {!version.isActive && versions.length > 1 && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="gap-1 text-red-600 hover:text-red-700"
                            onClick={() => handleDelete(version.id, version.version)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {index < versions.length - 1 && <Separator className="mt-4" />}
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
