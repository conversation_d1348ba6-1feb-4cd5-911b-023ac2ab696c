import {
  Activity,
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle,
  XCircle,
  Zap,
  Download,
  Upload,
  Wifi,
  WifiOff,
  Play,
  Pause,
  MoreHorizontal,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useWebSocket } from "@/hooks/useWebSocket";

export function ProgressTracker() {
  const { progressItems, isConnected } = useWebSocket();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "running":
        return (
          <motion.div
            className="relative"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Zap className="h-4 w-4 text-primary" />
            <motion.div
              className="absolute inset-0 h-4 w-4 text-primary/30"
              animate={{ scale: [1, 1.5, 1], opacity: [1, 0, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Zap className="h-4 w-4" />
            </motion.div>
          </motion.div>
        );
      case "completed":
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 500, damping: 15 }}
          >
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </motion.div>
        );
      case "failed":
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ shake: 5 }}
          >
            <XCircle className="h-4 w-4 text-red-500" />
          </motion.div>
        );
      case "paused":
        return (
          <motion.div
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <Pause className="h-4 w-4 text-yellow-500" />
          </motion.div>
        );
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/30";
      case "completed":
        return "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/30";
      case "failed":
        return "bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-lg shadow-red-500/30";
      case "paused":
        return "bg-gradient-to-r from-yellow-500 to-amber-500 text-white shadow-lg shadow-yellow-500/30";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const formatDuration = (startTime: Date) => {
    const duration = Date.now() - startTime.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const runningItems = progressItems.filter(
    (item) => item.status === "running",
  );
  const completedItems = progressItems.filter(
    (item) => item.status === "completed",
  );

  return (
    <div className="panel p-6 relative overflow-hidden group">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-110 transition-transform duration-500" />

      <div className="relative z-10">
        {/* Header */}
        <motion.div
          className="flex items-center gap-4 mb-6"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative">
            <motion.div
              className="p-3 bg-primary/10 rounded-xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <TrendingUp className="h-6 w-6 text-primary" />
            </motion.div>
            {runningItems.length > 0 && (
              <motion.div
                className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-primary to-blue-500 rounded-full flex items-center justify-center shadow-lg"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 15 }}
              >
                <span className="text-xs text-white font-bold">
                  {runningItems.length}
                </span>
              </motion.div>
            )}
          </div>

          <div className="flex-1">
            <h3 className="font-bold text-xl text-gradient">
              Progress Tracking
            </h3>
            <div className="flex items-center gap-3 mt-1">
              <p className="text-muted-foreground">
                Monitor active imports and syncs
              </p>
              <motion.div
                className="flex items-center gap-1 text-xs"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {isConnected ? (
                  <>
                    <Wifi className="h-3 w-3 text-green-500" />
                    <span className="text-green-600 font-medium">Live</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-3 w-3 text-red-500" />
                    <span className="text-red-600 font-medium">Offline</span>
                  </>
                )}
              </motion.div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {completedItems.length > 0 && (
              <motion.div
                className="flex items-center gap-1 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <CheckCircle2 className="h-4 w-4" />
                <span className="font-medium">
                  {completedItems.length} completed
                </span>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Progress Items */}
        <ScrollArea className="h-80">
          <div className="space-y-4 pr-4">
            <AnimatePresence>
              {progressItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  className="card-modern p-6 space-y-4 group hover:shadow-lg transition-all duration-300 relative overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                >
                  {/* Background glow for running items */}
                  {item.status === "running" && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-lg"
                      animate={{ opacity: [0.3, 0.7, 0.3] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}

                  <div className="relative z-10">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(item.status)}
                        <div>
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors">
                            {item.title}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge
                              className={`${getStatusColor(item.status)} text-xs font-medium`}
                            >
                              {item.status.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatDuration(item.startTime)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-muted-foreground">
                          {formatTime(item.startTime)}
                        </span>
                        {item.status === "running" && (
                          <div className="flex items-center gap-1 mt-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <Pause className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium text-gradient">
                          {item.progress.toFixed(0)}% complete
                        </span>
                        {item.totalFiles && (
                          <span className="text-muted-foreground">
                            {item.processedFiles}/{item.totalFiles} files
                          </span>
                        )}
                      </div>

                      <div className="relative">
                        <div className="progress-modern h-3 bg-muted/50 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-gradient-to-r from-primary to-blue-500 rounded-full relative overflow-hidden"
                            initial={{ width: 0 }}
                            animate={{ width: `${item.progress}%` }}
                            transition={{ duration: 1, ease: "easeOut" }}
                          >
                            {item.status === "running" && (
                              <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                animate={{ x: ["-100%", "100%"] }}
                                transition={{
                                  duration: 1.5,
                                  repeat: Infinity,
                                  ease: "linear",
                                }}
                              />
                            )}
                          </motion.div>
                        </div>
                      </div>
                    </div>

                    {/* Current File */}
                    {item.currentFile && item.status === "running" && (
                      <motion.div
                        className="bg-primary/5 p-3 rounded-lg border border-primary/10 relative"
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <div className="text-xs text-primary font-medium mb-1 flex items-center gap-1">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <Activity className="h-3 w-3" />
                          </motion.div>
                          Currently processing
                        </div>
                        <div className="text-sm font-mono text-foreground truncate">
                          {item.currentFile}
                        </div>
                      </motion.div>
                    )}

                    {/* Footer Info */}
                    <div className="flex items-center justify-between pt-2">
                      <div className="flex items-center gap-4">
                        {item.estimatedCompletion &&
                          item.status === "running" && (
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              ETA: {formatTime(item.estimatedCompletion)}
                            </div>
                          )}
                      </div>

                      <div className="flex items-center gap-1">
                        {item.type === "upload" && (
                          <motion.div
                            animate={{ y: [-2, 2, -2] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                          >
                            <Upload className="h-4 w-4 text-blue-500" />
                          </motion.div>
                        )}
                        {item.type === "download" && (
                          <motion.div
                            animate={{ y: [2, -2, 2] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                          >
                            <Download className="h-4 w-4 text-green-500" />
                          </motion.div>
                        )}
                        {item.type === "sync" && (
                          <motion.div
                            animate={{ rotate: [0, 180, 360] }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <Activity className="h-4 w-4 text-purple-500" />
                          </motion.div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {progressItems.length === 0 && (
              <motion.div
                className="text-center py-12 text-muted-foreground"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Activity className="h-16 w-16 mx-auto mb-4 opacity-30" />
                </motion.div>
                <p className="text-lg font-medium mb-2">No active operations</p>
                <p className="text-sm">
                  Progress will appear here when tasks are running
                </p>
              </motion.div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
