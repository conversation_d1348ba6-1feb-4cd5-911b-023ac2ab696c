import {
  <PERSON>ertCircle,
  CheckCircle2,
  Info,
  Alert<PERSON><PERSON>gle,
  Clock,
  Filter,
  Search,
  FileText,
  Upload,
  Download,
  Settings,
  Shield,
  Activity,
  Trash2,
  RefreshCw,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

import { useWebSocket } from "@/hooks/useWebSocket";

export function EventLog() {
  const { events } = useWebSocket();
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  const getEventIcon = (type: string, category: string) => {
    const iconClass = "h-4 w-4";

    if (category === "upload")
      return <Upload className={`${iconClass} text-blue-500`} />;
    if (category === "download")
      return <Download className={`${iconClass} text-green-500`} />;
    if (category === "sync")
      return <RefreshCw className={`${iconClass} text-purple-500`} />;
    if (category === "auth")
      return <Shield className={`${iconClass} text-orange-500`} />;
    if (category === "system")
      return <Settings className={`${iconClass} text-gray-500`} />;

    switch (type) {
      case "success":
        return <CheckCircle2 className={`${iconClass} text-green-500`} />;
      case "error":
        return <AlertCircle className={`${iconClass} text-red-500`} />;
      case "warning":
        return <AlertTriangle className={`${iconClass} text-yellow-500`} />;
      case "info":
        return <Info className={`${iconClass} text-blue-500`} />;
      default:
        return <FileText className={`${iconClass} text-gray-500`} />;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/30";
      case "error":
        return "bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-lg shadow-red-500/30";
      case "warning":
        return "bg-gradient-to-r from-yellow-500 to-amber-500 text-white shadow-lg shadow-yellow-500/30";
      case "info":
        return "bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/30";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getBorderColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-green-500/20 bg-green-500/5";
      case "error":
        return "border-red-500/20 bg-red-500/5";
      case "warning":
        return "border-yellow-500/20 bg-yellow-500/5";
      case "info":
        return "border-blue-500/20 bg-blue-500/5";
      default:
        return "border-muted/20 bg-muted/5";
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / 60000);

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const filteredEvents = events.filter((event) => {
    const matchesFilter =
      filter === "all" || event.type === filter || event.category === filter;
    const matchesSearch =
      searchQuery === "" ||
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.message.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const eventCounts = {
    total: events.length,
    success: events.filter((e) => e.type === "success").length,
    error: events.filter((e) => e.type === "error").length,
    warning: events.filter((e) => e.type === "warning").length,
    info: events.filter((e) => e.type === "info").length,
  };

  return (
    <div className="panel p-6 relative overflow-hidden group">
      {/* Background decoration */}
      <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-secondary/5 to-transparent rounded-full transform -translate-x-16 translate-y-16 group-hover:scale-110 transition-transform duration-500" />

      <div className="relative z-10">
        {/* Header */}
        <motion.div
          className="flex items-center gap-4 mb-6"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative">
            <motion.div
              className="p-3 bg-secondary/10 rounded-xl"
              whileHover={{ scale: 1.1, rotate: -5 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Activity className="h-6 w-6 text-secondary" />
            </motion.div>
            {eventCounts.error > 0 && (
              <motion.div
                className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 15 }}
              >
                <span className="text-xs text-white font-bold">
                  {eventCounts.error}
                </span>
              </motion.div>
            )}
          </div>

          <div className="flex-1">
            <h3 className="font-bold text-xl text-gradient">Event Log</h3>
            <p className="text-muted-foreground">
              Recent system activities and notifications
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {eventCounts.total} events
            </Badge>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="flex items-center gap-3 mb-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="relative flex-1 max-w-xs">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search events..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-9 input-modern bg-background-secondary/50"
            />
          </div>

          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-32 h-9">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="success">Success</SelectItem>
              <SelectItem value="error">Errors</SelectItem>
              <SelectItem value="warning">Warnings</SelectItem>
              <SelectItem value="info">Info</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Event Statistics */}
        <motion.div
          className="grid grid-cols-4 gap-3 mb-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="text-center p-3 bg-green-500/5 border border-green-500/20 rounded-lg">
            <div className="text-lg font-bold text-green-600">
              {eventCounts.success}
            </div>
            <div className="text-xs text-green-600">Success</div>
          </div>
          <div className="text-center p-3 bg-red-500/5 border border-red-500/20 rounded-lg">
            <div className="text-lg font-bold text-red-600">
              {eventCounts.error}
            </div>
            <div className="text-xs text-red-600">Errors</div>
          </div>
          <div className="text-center p-3 bg-yellow-500/5 border border-yellow-500/20 rounded-lg">
            <div className="text-lg font-bold text-yellow-600">
              {eventCounts.warning}
            </div>
            <div className="text-xs text-yellow-600">Warnings</div>
          </div>
          <div className="text-center p-3 bg-blue-500/5 border border-blue-500/20 rounded-lg">
            <div className="text-lg font-bold text-blue-600">
              {eventCounts.info}
            </div>
            <div className="text-xs text-blue-600">Info</div>
          </div>
        </motion.div>

        {/* Events List */}
        <ScrollArea className="h-80">
          <div className="space-y-3 pr-4">
            <AnimatePresence>
              {filteredEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  className={`card-modern p-4 border-l-4 ${getBorderColor(event.type)} hover:shadow-lg transition-all duration-300 group cursor-pointer`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ scale: 1.01, x: 5 }}
                >
                  <div className="flex items-start gap-3">
                    <motion.div
                      className="mt-1"
                      whileHover={{ scale: 1.2, rotate: 10 }}
                    >
                      {getEventIcon(event.type, event.category)}
                    </motion.div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <div className="flex-1">
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors">
                            {event.title}
                          </h4>
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {event.message}
                          </p>
                        </div>

                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge
                            className={`${getEventColor(event.type)} text-xs font-medium`}
                          >
                            {event.type.toUpperCase()}
                          </Badge>
                          <div className="text-xs text-muted-foreground text-right">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTime(event.timestamp)}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* File path */}
                      {event.clientPath && (
                        <div className="bg-muted/30 p-2 rounded-md mb-2">
                          <div className="text-xs text-muted-foreground mb-1">
                            File Path:
                          </div>
                          <div className="text-sm font-mono text-foreground truncate">
                            {event.clientPath}
                          </div>
                        </div>
                      )}

                      {/* Details */}
                      {event.details && (
                        <motion.div
                          className="bg-primary/5 p-2 rounded-md border border-primary/10"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          transition={{ delay: 0.1 }}
                        >
                          <div className="text-xs text-primary font-medium mb-1">
                            Details:
                          </div>
                          <div className="text-sm text-foreground">
                            {event.details}
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {filteredEvents.length === 0 && (
              <motion.div
                className="text-center py-12 text-muted-foreground"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Activity className="h-16 w-16 mx-auto mb-4 opacity-30" />
                </motion.div>
                <p className="text-lg font-medium mb-2">No events found</p>
                <p className="text-sm">
                  {searchQuery
                    ? "Try adjusting your search terms"
                    : "Events will appear here as they occur"}
                </p>
              </motion.div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
