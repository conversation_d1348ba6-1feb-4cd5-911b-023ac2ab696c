import { useState, useEffect } from "react";
import { Building2, Save, MapPin, Mail, Phone, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface CompanyData {
  name: string;
  industry: string;
  address: string;
  email: string;
  phone: string;
  website: string;
  description: string;
}

export function CompanyInfo() {
  const { toast } = useToast();
  const [companyData, setCompanyData] = useState<CompanyData>({
    name: "",
    industry: "",
    address: "",
    email: "",
    phone: "",
    website: "",
    description: "",
  });

  const [loading, setLoading] = useState(false);

  // Load company data from localStorage on component mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem("w8file-company-info");
      if (savedData) {
        setCompanyData(JSON.parse(savedData));
      }
    } catch (error) {
      console.error("Error loading company data:", error);
    }
  }, []);

  const handleSave = async () => {
    setLoading(true);
    try {
      // Save to localStorage
      localStorage.setItem("w8file-company-info", JSON.stringify(companyData));

      // Simulate API call if needed
      await new Promise((resolve) => setTimeout(resolve, 500));

      toast({
        title: "Company Information Saved",
        description: "Your company details have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save company information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateField = (field: keyof CompanyData, value: string) => {
    setCompanyData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-blue-500/10 rounded-lg">
          <Building2 className="h-5 w-5 text-blue-500" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Company Information</h3>
          <p className="text-sm text-muted-foreground">
            Configure your organization details and contact information
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="company-name" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Company Name
            </Label>
            <Input
              id="company-name"
              value={companyData.name}
              onChange={(e) => updateField("name", e.target.value)}
              placeholder="Your Company Name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="industry">Industry</Label>
            <Input
              id="industry"
              value={companyData.industry}
              onChange={(e) => updateField("industry", e.target.value)}
              placeholder="e.g., Technology, Finance, Healthcare"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Address
            </Label>
            <Input
              id="address"
              value={companyData.address}
              onChange={(e) => updateField("address", e.target.value)}
              placeholder="Company Address"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Phone Number
            </Label>
            <Input
              id="phone"
              value={companyData.phone}
              onChange={(e) => updateField("phone", e.target.value)}
              placeholder="+****************"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Contact Email
            </Label>
            <Input
              id="email"
              type="email"
              value={companyData.email}
              onChange={(e) => updateField("email", e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="website" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Website
            </Label>
            <Input
              id="website"
              value={companyData.website}
              onChange={(e) => updateField("website", e.target.value)}
              placeholder="https://company.com"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Company Description</Label>
            <Textarea
              id="description"
              value={companyData.description}
              onChange={(e) => updateField("description", e.target.value)}
              placeholder="Brief description of your company..."
              rows={4}
              className="resize-none"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center pt-6 border-t border-border/50 mt-6">
        <div className="text-sm text-muted-foreground">
          Information is stored locally and used for report generation
        </div>
        <Button onClick={handleSave} disabled={loading} className="gap-2">
          <Save className="h-4 w-4" />
          {loading ? "Saving..." : "Save Information"}
        </Button>
      </div>
    </Card>
  );
}
