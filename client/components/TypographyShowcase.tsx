import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";

export function TypographyShowcase() {
  return (
    <div className="space-y-8 p-8">
      <div>
        <h1 className="text-display-large mb-4">Enhanced Typography System</h1>
        <p className="text-body-large text-muted-foreground">
          A comprehensive typography hierarchy built with professional design
          tokens and semantic classes.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Display Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Display Typography</CardTitle>
            <CardDescription>Large headings and hero text</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Display Large
              </p>
              <h1 className="text-display-large">Hero Title</h1>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">Display</p>
              <h1 className="text-display">Main Heading</h1>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Headline Large
              </p>
              <h2 className="text-headline-large">Section Title</h2>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Headline
              </p>
              <h2 className="text-headline">Component Title</h2>
            </div>
          </CardContent>
        </Card>

        {/* Content Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Content Typography</CardTitle>
            <CardDescription>
              Regular content and interface text
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Title Large
              </p>
              <h3 className="text-title-large">Card Title</h3>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">Title</p>
              <h4 className="text-title">Subsection</h4>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Label Large
              </p>
              <span className="text-label-large">Form Label</span>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">Label</p>
              <span className="text-label">Interface Label</span>
            </div>
          </CardContent>
        </Card>

        {/* Body Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Body Typography</CardTitle>
            <CardDescription>Reading content and descriptions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Body Large
              </p>
              <p className="text-body-large">
                This is large body text used for important descriptions and
                introductory content.
              </p>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">Body</p>
              <p className="text-body">
                This is regular body text used for most reading content and
                standard descriptions.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Supporting Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Supporting Typography</CardTitle>
            <CardDescription>Captions, overlines, and code</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-caption text-muted-foreground mb-2">Caption</p>
              <p className="text-caption">
                Small descriptive text and metadata
              </p>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Overline
              </p>
              <p className="text-overline">Navigation Label</p>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">Code</p>
              <code className="text-code bg-muted px-2 py-1 rounded">
                console.log('Hello World');
              </code>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gradient Text Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Gradient Text Effects</CardTitle>
          <CardDescription>
            Beautiful gradient text for emphasis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Primary Gradient
              </p>
              <h3 className="text-headline text-gradient">
                Beautiful Gradients
              </h3>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Success Gradient
              </p>
              <h3 className="text-headline text-gradient-success">
                Success Message
              </h3>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Warning Gradient
              </p>
              <h3 className="text-headline text-gradient-warning">
                Warning Alert
              </h3>
            </div>
            <div>
              <p className="text-caption text-muted-foreground mb-2">
                Destructive Gradient
              </p>
              <h3 className="text-headline text-gradient-destructive">
                Error State
              </h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Font Family Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Font Families</CardTitle>
          <CardDescription>
            Different typefaces for different purposes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <p className="text-caption text-muted-foreground mb-2">
              Sans Serif (Inter) - Interface
            </p>
            <p className="font-sans text-title">
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          <div>
            <p className="text-caption text-muted-foreground mb-2">
              Display (Space Grotesk) - Headlines
            </p>
            <p className="font-display text-title">
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          <div>
            <p className="text-caption text-muted-foreground mb-2">
              Serif (Playfair Display) - Editorial
            </p>
            <p className="font-serif text-title">
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          <div>
            <p className="text-caption text-muted-foreground mb-2">
              Mono (JetBrains Mono) - Code
            </p>
            <p className="font-mono text-title">
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
