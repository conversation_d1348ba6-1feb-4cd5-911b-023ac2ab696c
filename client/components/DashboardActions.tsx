import { useState } from "react";
import {
  Upload,
  FolderSync,
  Calendar,
  Settings,
  Play,
  RefreshCw,
  FileText,
  Database,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { useWebSocket } from "@/hooks/useWebSocket";

export function DashboardActions() {
  const {
    isConnected,
    uploadFiles,
    syncFolder,
    createSchedule,
    sendServiceCommand,
    progressItems,
    schedules,
  } = useWebSocket();

  const [selectedAction, setSelectedAction] = useState<string | null>(null);

  const handleFileUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.multiple = true;
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        uploadFiles(files);
        setSelectedAction("upload");
        setTimeout(() => setSelectedAction(null), 2000);
      }
    };
    input.click();
  };

  const handleQuickSync = () => {
    // Trigger a quick sync of the default Documents folder
    syncFolder("/documents", "cloud://default/documents");
    setSelectedAction("sync");
    setTimeout(() => setSelectedAction(null), 2000);
  };

  const handleCreateSchedule = () => {
    // Create a new quick schedule
    const schedule = {
      name: `Quick Sync ${Date.now()}`,
      folder: "/documents",
      destination: "cloud://default",
      frequency: "Every hour",
      nextRun: new Date(Date.now() + 3600000),
      lastRun: new Date(),
      status: "active" as const,
      filesCount: 0,
      isRunning: false,
    };
    createSchedule(schedule);
    setSelectedAction("schedule");
    setTimeout(() => setSelectedAction(null), 2000);
  };

  const handleStartService = () => {
    sendServiceCommand("start");
    setSelectedAction("service");
    setTimeout(() => setSelectedAction(null), 2000);
  };

  const runningTasks = progressItems.filter(
    (p) => p.status === "running",
  ).length;
  const activeSchedules = schedules.filter((s) => s.status === "active").length;

  const actions = [
    {
      id: "upload",
      label: "Quick Upload",
      description: "Upload files instantly",
      icon: Upload,
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-500/10",
      borderColor: "border-blue-500/20",
      action: handleFileUpload,
      badge: runningTasks > 0 ? `${runningTasks} active` : null,
      disabled: !isConnected,
    },
    {
      id: "sync",
      label: "Sync Folder",
      description: "Sync documents folder",
      icon: FolderSync,
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-500/10",
      borderColor: "border-green-500/20",
      action: handleQuickSync,
      badge: null,
      disabled: !isConnected,
    },
    {
      id: "schedule",
      label: "New Schedule",
      description: "Create sync schedule",
      icon: Calendar,
      color: "from-purple-500 to-violet-500",
      bgColor: "bg-purple-500/10",
      borderColor: "border-purple-500/20",
      action: handleCreateSchedule,
      badge: activeSchedules > 0 ? `${activeSchedules} active` : null,
      disabled: !isConnected,
    },
    {
      id: "service",
      label: "Start Service",
      description: "Initialize system",
      icon: Play,
      color: "from-orange-500 to-amber-500",
      bgColor: "bg-orange-500/10",
      borderColor: "border-orange-500/20",
      action: handleStartService,
      badge: isConnected ? "Online" : "Offline",
      disabled: false,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
      {actions.map((action, index) => {
        const ActionIcon = action.icon;
        const isSelected = selectedAction === action.id;

        return (
          <motion.div
            key={action.id}
            className={`card-modern p-6 cursor-pointer transition-all duration-300 relative overflow-hidden group border ${action.borderColor} ${action.bgColor} ${
              action.disabled
                ? "opacity-50 cursor-not-allowed"
                : "hover:shadow-lg hover:scale-105"
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={action.disabled ? {} : { y: -5 }}
            whileTap={action.disabled ? {} : { scale: 0.98 }}
            onClick={action.disabled ? undefined : action.action}
          >
            {/* Background gradient animation */}
            {isSelected && (
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r ${action.color} opacity-10`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5 }}
              />
            )}

            {/* Action content */}
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <motion.div
                  className={`p-3 rounded-xl ${action.bgColor} group-hover:scale-110 transition-transform`}
                  whileHover={action.disabled ? {} : { rotate: 10 }}
                >
                  <ActionIcon
                    className={`h-6 w-6 bg-gradient-to-r ${action.color} bg-clip-text text-transparent`}
                  />
                </motion.div>

                {action.badge && (
                  <Badge
                    variant={
                      action.id === "service" && isConnected
                        ? "default"
                        : "outline"
                    }
                    className="text-xs"
                  >
                    {action.badge}
                  </Badge>
                )}
              </div>

              <div>
                <div className="text-lg font-bold text-gradient mb-1">
                  {action.label}
                </div>
                <div className="text-sm text-muted-foreground">
                  {action.description}
                </div>
              </div>

              {/* Action feedback */}
              {isSelected && (
                <motion.div
                  className="absolute top-2 right-2"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 15 }}
                >
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                </motion.div>
              )}
            </div>

            {/* Hover effect overlay */}
            {!action.disabled && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
            )}
          </motion.div>
        );
      })}
    </div>
  );
}
