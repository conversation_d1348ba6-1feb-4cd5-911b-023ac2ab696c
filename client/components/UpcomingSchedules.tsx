import {
  Calendar,
  Clock,
  FolderSync,
  Play,
  Pause,
  Settings,
  MoreHorizontal,
  Plus,
  Edit,
  Trash2,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Repeat,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { motion, AnimatePresence } from "framer-motion";

import { useWebSocket } from "@/hooks/useWebSocket";

interface UpcomingSchedulesProps {
  onCreateScheduleClick?: () => void;
}

export function UpcomingSchedules({
  onCreateScheduleClick,
}: UpcomingSchedulesProps) {
  const { schedules, updateSchedule, deleteSchedule } = useWebSocket();

  const getStatusColor = (status: string, isRunning: boolean) => {
    if (isRunning) {
      return "bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/30 animate-pulse";
    }

    switch (status) {
      case "active":
        return "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/30";
      case "paused":
        return "bg-gradient-to-r from-yellow-500 to-amber-500 text-white shadow-lg shadow-yellow-500/30";
      case "error":
        return "bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-lg shadow-red-500/30";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getBorderColor = (status: string, isRunning: boolean) => {
    if (isRunning) {
      return "border-blue-500/30 bg-blue-500/5";
    }

    switch (status) {
      case "active":
        return "border-green-500/30 bg-green-500/5";
      case "paused":
        return "border-yellow-500/30 bg-yellow-500/5";
      case "error":
        return "border-red-500/30 bg-red-500/5";
      default:
        return "border-muted/30 bg-muted/5";
    }
  };

  const formatNextRun = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((date.getTime() - now.getTime()) / 60000);

    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const activeSchedules = schedules.filter((s) => s.status === "active").length;
  const runningSchedules = schedules.filter((s) => s.isRunning).length;

  return (
    <div className="panel p-6 relative overflow-hidden group">
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-green-500/5 to-transparent rounded-full transform -translate-x-20 -translate-y-20 group-hover:scale-110 transition-transform duration-500" />

      <div className="relative z-10">
        {/* Header */}
        <motion.div
          className="flex items-center justify-between mb-6"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center gap-4">
            <div className="relative">
              <motion.div
                className="p-3 bg-green-500/10 rounded-xl"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Calendar className="h-6 w-6 text-green-500" />
              </motion.div>
              {runningSchedules > 0 && (
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-lg"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 15 }}
                >
                  <span className="text-xs text-white font-bold">
                    {runningSchedules}
                  </span>
                </motion.div>
              )}
            </div>

            <div className="flex-1">
              <h3 className="font-bold text-xl text-gradient">
                Upcoming Schedules
              </h3>
              <div className="flex items-center gap-3 mt-1">
                <p className="text-muted-foreground">
                  Automated sync schedules
                </p>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {activeSchedules} active
                  </Badge>
                  {runningSchedules > 0 && (
                    <Badge className="bg-blue-500 text-white text-xs animate-pulse">
                      {runningSchedules} running
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          className="grid grid-cols-3 gap-4 mb-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="text-center p-3 bg-green-500/5 border border-green-500/20 rounded-lg">
            <div className="text-lg font-bold text-green-600">
              {activeSchedules}
            </div>
            <div className="text-xs text-green-600">Active</div>
          </div>
          <div className="text-center p-3 bg-blue-500/5 border border-blue-500/20 rounded-lg">
            <div className="text-lg font-bold text-blue-600">
              {runningSchedules}
            </div>
            <div className="text-xs text-blue-600">Running</div>
          </div>
          <div className="text-center p-3 bg-purple-500/5 border border-purple-500/20 rounded-lg">
            <div className="text-lg font-bold text-purple-600">
              {schedules.reduce((acc, s) => acc + s.filesCount, 0)}
            </div>
            <div className="text-xs text-purple-600">Total Files</div>
          </div>
        </motion.div>

        {/* Schedules List */}
        <ScrollArea className="h-80">
          <div className="space-y-4 pr-4">
            <AnimatePresence>
              {schedules.map((schedule, index) => (
                <motion.div
                  key={schedule.id}
                  className={`card-modern p-5 border-l-4 ${getBorderColor(schedule.status, schedule.isRunning)} hover:shadow-lg transition-all duration-300 group cursor-pointer relative overflow-hidden`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, x: 5 }}
                >
                  {/* Running indicator animation */}
                  {schedule.isRunning && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5"
                      animate={{ opacity: [0.3, 0.7, 0.3] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}

                  <div className="relative z-10">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-3">
                        <motion.div
                          className="mt-1"
                          whileHover={{ scale: 1.2, rotate: 10 }}
                        >
                          {schedule.isRunning ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "linear",
                              }}
                            >
                              <FolderSync className="h-5 w-5 text-blue-500" />
                            </motion.div>
                          ) : (
                            <FolderSync className="h-5 w-5 text-muted-foreground" />
                          )}
                        </motion.div>

                        <div className="flex-1">
                          <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                            {schedule.name}
                          </h4>
                          <div className="text-sm text-muted-foreground mt-1">
                            {schedule.folder} → {schedule.destination}
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge
                              className={`${getStatusColor(schedule.status, schedule.isRunning)} text-xs font-medium`}
                            >
                              {schedule.isRunning
                                ? "RUNNING"
                                : schedule.status.toUpperCase()}
                            </Badge>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Repeat className="h-3 w-3" />
                              {schedule.frequency}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          {schedule.status === "paused" ? (
                            <Play className="h-4 w-4" />
                          ) : (
                            <Pause className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Schedule Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-green-500" />
                          <span className="text-muted-foreground">
                            Next run:
                          </span>
                          <span className="font-medium text-green-600">
                            {formatNextRun(schedule.nextRun)} (
                            {formatTime(schedule.nextRun)})
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-blue-500" />
                          <span className="text-muted-foreground">
                            Last run:
                          </span>
                          <span className="font-medium">
                            {formatTime(schedule.lastRun)}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground">Files:</span>
                          <span className="font-medium">
                            {schedule.filesCount.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground">Status:</span>
                          <span
                            className={`font-medium ${
                              schedule.isRunning
                                ? "text-blue-600"
                                : schedule.status === "active"
                                  ? "text-green-600"
                                  : "text-yellow-600"
                            }`}
                          >
                            {schedule.isRunning
                              ? "Running"
                              : schedule.status === "active"
                                ? "Ready"
                                : "Paused"}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Progress for running schedules */}
                    {schedule.isRunning && (
                      <motion.div
                        className="mt-4 pt-4 border-t border-border/50"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        transition={{ delay: 0.2 }}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <FolderSync className="h-4 w-4 text-blue-500" />
                          </motion.div>
                          <span className="text-sm font-medium text-blue-600">
                            Syncing in progress...
                          </span>
                        </div>
                        <div className="progress-modern h-2 bg-blue-500/10 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"
                            initial={{ width: "0%" }}
                            animate={{ width: "65%" }}
                            transition={{ duration: 2, ease: "easeOut" }}
                          >
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                              animate={{ x: ["-100%", "100%"] }}
                              transition={{
                                duration: 1.5,
                                repeat: Infinity,
                                ease: "linear",
                              }}
                            />
                          </motion.div>
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>Processing: project-docs.pdf</span>
                          <span>65% (102/156 files)</span>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {schedules.length === 0 && (
              <motion.div
                className="text-center py-12 text-muted-foreground"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Calendar className="h-16 w-16 mx-auto mb-4 opacity-30" />
                </motion.div>
                <p className="text-lg font-medium mb-2">
                  No schedules configured
                </p>
                <p className="text-sm mb-4">
                  Create your first sync schedule to automate file
                  synchronization
                </p>
                <Button className="btn-modern" onClick={onCreateScheduleClick}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Schedule
                </Button>
              </motion.div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
