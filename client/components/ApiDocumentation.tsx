import { useState } from "react";
import {
  BookOpen,
  Code,
  GitBranch,
  Globe,
  Key,
  Layers,
  Network,
  PlayCircle,
  Server,
  Smartphone,
  Terminal,
  Zap,
  ChevronRight,
  Copy,
  Check,
  ExternalLink,
  Settings,
  Database,
  Shield,
  Clock,
  Activity,
  FileText,
  Users,
  Monitor,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { motion } from "framer-motion";
import { MermaidChart } from "./MermaidChart";

export function ApiDocumentation() {
  const [copied, setCopied] = useState<string | null>(null);

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopied(id);
    setTimeout(() => setCopied(null), 2000);
  };

  const CodeBlock = ({
    code,
    language,
    id,
  }: {
    code: string;
    language: string;
    id: string;
  }) => (
    <div className="relative group">
      <pre className="bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto text-sm border">
        <code className={`language-${language}`}>{code}</code>
      </pre>
      <Button
        size="sm"
        variant="ghost"
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={() => copyToClipboard(code, id)}
      >
        {copied === id ? (
          <Check className="h-4 w-4" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
    </div>
  );

  // Mermaid chart definitions
  const architectureChart = `
graph TB
    subgraph "External Applications"
        A1[Python App]
        A2[Node.js Service]
        A3[cURL Scripts]
        A4[Other Services]
    end
    
    subgraph "W8 File Importer API Layer"
        B1[Authentication Middleware]
        B2[Rate Limiting]
        B3[Request Validation]
        B4[WebSocket Router]
    end
    
    subgraph "Core Services"
        C1[Settings Manager]
        C2[File Operations]
        C3[Schedule Manager]
        C4[Progress Tracker]
        C5[Event Logger]
        C6[System Monitor]
    end
    
    subgraph "WebSocket Hub"
        D1[Connection Manager]
        D2[Message Broker]
        D3[Client Subscriptions]
        D4[Event Dispatcher]
    end
    
    subgraph "Frontend Dashboard"
        E1[Real-time UI]
        E2[Settings Panel]
        E3[Progress Display]
        E4[Event Log]
        E5[System Status]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
    B4 --> C5
    B4 --> C6
    
    C1 --> D2
    C2 --> D2
    C3 --> D2
    C4 --> D2
    C5 --> D2
    C6 --> D2
    
    D1 --> D2
    D2 --> D3
    D3 --> D4
    
    D4 --> E1
    D4 --> E2
    D4 --> E3
    D4 --> E4
    D4 --> E5
    
    style A1 fill:#e1f5fe
    style B4 fill:#fff3e0
    style D2 fill:#e8f5e8
    style E1 fill:#f3e5f5
`;

  const sequenceChart = `
sequenceDiagram
    participant EA as External App
    participant API as API Gateway
    participant WS as WebSocket Hub
    participant UI as Dashboard UI
    participant SVC as Core Services
    
    Note over EA,UI: Complete WebSocket Communication Flow
    
    EA->>API: POST /api/settings/update
    API->>API: Authenticate & Validate
    API->>SVC: Process Setting Change
    SVC->>WS: Broadcast Setting Update
    WS->>UI: Real-time Setting Change
    API->>EA: Success Response
    
    EA->>API: POST /api/progress/start
    API->>SVC: Create Progress Task
    SVC->>WS: Broadcast Progress Created
    WS->>UI: Show New Progress
    
    loop Progress Updates
        EA->>API: POST /api/progress/update
        API->>SVC: Update Progress
        SVC->>WS: Broadcast Progress Update
        WS->>UI: Update Progress Bar
    end
    
    EA->>API: POST /api/events/log
    API->>SVC: Log Event
    SVC->>WS: Broadcast Event
    WS->>UI: Show Event in Log
    
    UI->>WS: Service Command Request
    WS->>SVC: Execute Command
    SVC->>WS: Command Result
    WS->>UI: Command Response
    WS->>EA: Service Status Change (if subscribed)
    
    Note over EA,UI: All communication flows through WebSocket Hub
`;

  const dataFlowChart = `
graph LR
    subgraph "Data Sources"
        A1[File Operations]
        A2[User Settings]
        A3[System Events]
        A4[Schedule Tasks]
        A5[External APIs]
    end
    
    subgraph "Processing Layer"
        B1[Data Validator]
        B2[Message Transformer]
        B3[Event Aggregator]
        B4[State Manager]
    end
    
    subgraph "WebSocket Channels"
        C1[progress]
        C2[events]
        C3[settings]
        C4[system-status]
        C5[notifications]
        C6[schedules]
    end
    
    subgraph "UI Components"
        D1[Progress Tracker]
        D2[Event Log]
        D3[Settings Panel]
        D4[System Monitor]
        D5[Notifications]
        D6[Schedule Manager]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    A5 --> B1
    
    B1 --> C1
    B2 --> C3
    B3 --> C2
    B4 --> C4
    B1 --> C5
    B4 --> C6
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    C5 --> D5
    C6 --> D6
    
    style A1 fill:#fff2cc
    style C1 fill:#d5e8d4
    style D1 fill:#f8cecc
`;

  // Comprehensive endpoint examples
  const endpoints = {
    settings: {
      curl: `# Update Application Settings
curl -X POST http://localhost:8080/api/settings/update \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "category": "sync",
    "settings": {
      "autoSync": true,
      "syncInterval": 300,
      "maxConcurrentSyncs": 5,
      "retryAttempts": 3
    }
  }'

# Update Theme Settings
curl -X POST http://localhost:8080/api/settings/theme \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "theme": "dark",
    "accentColor": "#3b82f6",
    "customCSS": ":root { --custom-bg: #1a1a1a; }"
  }'

# Update API Configuration
curl -X POST http://localhost:8080/api/settings/api \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "rateLimit": 100,
    "enableCors": true,
    "allowedOrigins": ["http://localhost:3000"]
  }'`,

      python: `import requests
import json

API_KEY = "demo-api-key-12345"
BASE_URL = "http://localhost:8080/api"
headers = {"X-API-Key": API_KEY, "Content-Type": "application/json"}

# Update sync settings
sync_settings = {
    "category": "sync",
    "settings": {
        "autoSync": True,
        "syncInterval": 300,
        "compressionEnabled": True,
        "encryptionLevel": "AES256"
    }
}

response = requests.post(f"{BASE_URL}/settings/update", 
                        json=sync_settings, headers=headers)
print(f"Settings updated: {response.status_code}")

# Update notification preferences
notification_settings = {
    "category": "notifications",
    "settings": {
        "emailNotifications": True,
        "slackWebhook": "https://hooks.slack.com/...",
        "discordWebhook": "https://discord.com/api/webhooks/...",
        "notifyOnError": True,
        "notifyOnSuccess": False
    }
}

response = requests.post(f"{BASE_URL}/settings/notifications", 
                        json=notification_settings, headers=headers)`,

      javascript: `const API_KEY = "demo-api-key-12345";
const BASE_URL = "http://localhost:8080/api";
const headers = {"X-API-Key": API_KEY, "Content-Type": "application/json"};

// Update performance settings
const performanceSettings = {
    category: "performance",
    settings: {
        maxMemoryUsage: 1024, // MB
        threadPoolSize: 8,
        cacheTTL: 3600,
        enablePrefetch: true,
        compressionLevel: 6
    }
};

fetch(\`\${BASE_URL}/settings/performance\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(performanceSettings)
})
.then(response => response.json())
.then(data => console.log('Performance settings updated:', data));

// Update security settings
const securitySettings = {
    category: "security",
    settings: {
        enableTwoFactor: true,
        sessionTimeout: 1800,
        maxLoginAttempts: 5,
        ipWhitelist: ["***********/24"],
        auditLogging: true
    }
};

fetch(\`\${BASE_URL}/settings/security\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(securitySettings)
});`,
    },

    operations: {
      curl: `# Start File Operation
curl -X POST http://localhost:8080/api/operations/start \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "operationType": "batch-upload",
    "source": "/local/documents",
    "destination": "ClientA/Documents/2025",
    "options": {
      "preserveStructure": true,
      "skipExisting": false,
      "compressionLevel": 5,
      "encryptFiles": true
    }
  }'

# Control Operation (pause/resume/cancel)
curl -X POST http://localhost:8080/api/operations/control \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "operationId": "op_12345",
    "action": "pause"
  }'

# Get Operation Status
curl -X GET http://localhost:8080/api/operations/status/op_12345 \\
  -H "X-API-Key: demo-api-key-12345"`,

      python: `# Start sync operation with advanced options
sync_operation = {
    "operationType": "bidirectional-sync",
    "source": "/local/project",
    "destination": "ClientB/Projects/Active",
    "options": {
        "conflictResolution": "newer-wins",
        "deleteOrphaned": False,
        "maxFileSize": 100 * 1024 * 1024,  # 100MB
        "excludePatterns": ["*.tmp", "node_modules/*"],
        "includePatterns": ["*.py", "*.js", "*.ts"],
        "bandwidth_limit": 1024 * 1024,  # 1MB/s
        "retryAttempts": 3,
        "checksumVerification": True
    }
}

response = requests.post(f"{BASE_URL}/operations/start", 
                        json=sync_operation, headers=headers)
operation_id = response.json()['operationId']

# Monitor operation progress
import time
while True:
    status_response = requests.get(f"{BASE_URL}/operations/status/{operation_id}", 
                                  headers=headers)
    status = status_response.json()
    
    if status['status'] in ['completed', 'failed', 'cancelled']:
        break
    
    print(f"Progress: {status['progress']}% - {status['currentFile']}")
    time.sleep(2)`,

      javascript: `// Start download operation with retry logic
const downloadOperation = {
    operationType: "bulk-download",
    source: "ClientC/Archives/2024",
    destination: "/local/downloads",
    options: {
        preserveTimestamps: true,
        verifyIntegrity: true,
        resumeInterrupted: true,
        parallelConnections: 4,
        retryDelay: 5000,
        maxRetries: 5
    }
};

const response = await fetch(\`\${BASE_URL}/operations/start\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(downloadOperation)
});

const { operationId } = await response.json();

// Set up WebSocket listener for real-time updates
const ws = new WebSocket('ws://localhost:8080/ws');
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    if (message.type === 'progress' && message.data.id === operationId) {
        console.log(\`Download progress: \${message.data.progress}%\`);
        console.log(\`Current file: \${message.data.currentFile}\`);
        console.log(\`Speed: \${message.data.transferRate} MB/s\`);
    }
};`,
    },

    schedules: {
      curl: `# Create Advanced Schedule
curl -X POST http://localhost:8080/api/schedules/create \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "Daily Client Data Sync",
    "description": "Automated sync of client data every day at 2 AM",
    "schedule": {
      "type": "cron",
      "expression": "0 2 * * *",
      "timezone": "America/New_York"
    },
    "operation": {
      "type": "bidirectional-sync",
      "source": "/local/client-data",
      "destination": "Production/ClientData",
      "options": {
        "deleteOrphaned": false,
        "conflictResolution": "manual",
        "notifyOnConflict": true
      }
    },
    "conditions": {
      "minFreeSpace": 1024,
      "maxCpuUsage": 70,
      "requiredConnections": ["primary-server", "backup-server"]
    },
    "notifications": {
      "onStart": true,
      "onComplete": true,
      "onError": true,
      "channels": ["email", "slack"]
    }
  }'

# Update Schedule Status
curl -X POST http://localhost:8080/api/schedules/toggle \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "scheduleId": "sched_12345",
    "enabled": false,
    "reason": "Maintenance window"
  }'`,

      python: `# Create complex conditional schedule
schedule_config = {
    "name": "Intelligent Backup Schedule",
    "description": "Smart backup that adapts to system load",
    "schedule": {
        "type": "adaptive",
        "baseInterval": 3600,  # 1 hour
        "conditions": {
            "lowCpuThreshold": 30,
            "lowNetworkThreshold": 10,
            "preferredTimeWindows": [
                {"start": "22:00", "end": "06:00"},  # Night hours
                {"start": "12:00", "end": "13:00"}   # Lunch hour
            ]
        }
    },
    "operation": {
        "type": "incremental-backup",
        "source": "/critical/data",
        "destination": "Backups/Incremental",
        "options": {
            "compressionLevel": 9,
            "encryptionKey": "backup-key-2025",
            "deduplicate": True,
            "verifyBackup": True
        }
    },
    "failover": {
        "enabled": True,
        "alternativeDestinations": [
            "Backups/Secondary",
            "CloudStorage/Emergency"
        ],
        "maxRetries": 3
    }
}

response = requests.post(f"{BASE_URL}/schedules/create", 
                        json=schedule_config, headers=headers)
schedule_id = response.json()['scheduleId']

# Add schedule dependency
dependency = {
    "scheduleId": schedule_id,
    "dependsOn": ["daily-cleanup", "system-health-check"],
    "waitTimeout": 1800  # 30 minutes
}

requests.post(f"{BASE_URL}/schedules/dependency", 
             json=dependency, headers=headers)`,

      javascript: `// Create event-driven schedule
const eventSchedule = {
    name: "File Watcher Sync",
    description: "Sync files when changes are detected",
    schedule: {
        type: "event-driven",
        triggers: [
            {
                type: "file-change",
                path: "/watched/directory",
                events: ["create", "modify", "delete"],
                debounceTime: 5000  // Wait 5s for batch changes
            },
            {
                type: "api-webhook",
                endpoint: "/webhook/external-update",
                authentication: "bearer-token"
            }
        ]
    },
    operation: {
        type: "smart-sync",
        source: "/watched/directory",
        destination: "Live/Updates",
        options: {
            batchChanges: true,
            conflictStrategy: "timestamp-wins",
            preserveDeletes: false,
            maxBatchSize: 100
        }
    },
    throttling: {
        maxExecutionsPerHour: 12,
        cooldownPeriod: 300,  // 5 minutes
        skipIfBusy: true
    }
};

fetch(\`\${BASE_URL}/schedules/create\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(eventSchedule)
})
.then(response => response.json())
.then(data => {
    console.log('Event-driven schedule created:', data.scheduleId);
    
    // Subscribe to schedule events
    const ws = new WebSocket('ws://localhost:8080/ws');
    ws.send(JSON.stringify({
        type: 'subscribe',
        channel: 'schedule-events',
        scheduleId: data.scheduleId
    }));
});`,
    },

    monitoring: {
      curl: `# Get System Metrics
curl -X GET http://localhost:8080/api/monitoring/metrics \\
  -H "X-API-Key: demo-api-key-12345"

# Set Performance Alerts
curl -X POST http://localhost:8080/api/monitoring/alerts \\
  -H "X-API-Key: demo-api-key-12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "High CPU Usage Alert",
    "condition": {
      "metric": "cpu_usage",
      "operator": ">",
      "threshold": 85,
      "duration": 300
    },
    "actions": [
      {
        "type": "throttle-operations",
        "severity": "warning"
      },
      {
        "type": "send-notification",
        "channels": ["email", "slack"]
      }
    ]
  }'

# Get Operation Logs
curl -X GET "http://localhost:8080/api/monitoring/logs?level=error&limit=100" \\
  -H "X-API-Key: demo-api-key-12345"`,

      python: `# Advanced monitoring setup
monitoring_config = {
    "metrics": {
        "collection_interval": 30,  # seconds
        "retention_period": 7776000,  # 90 days
        "aggregation_levels": ["1m", "5m", "1h", "1d"]
    },
    "alerts": [
        {
            "name": "Memory Usage Critical",
            "condition": {
                "metric": "memory_usage_percent",
                "operator": ">",
                "threshold": 90,
                "consecutive_breaches": 3
            },
            "actions": [
                {"type": "pause-non-critical-operations"},
                {"type": "trigger-gc"},
                {"type": "send-alert", "severity": "critical"}
            ]
        },
        {
            "name": "Sync Operation Failure Rate",
            "condition": {
                "metric": "sync_failure_rate",
                "operator": ">",
                "threshold": 5,  # 5% failure rate
                "time_window": 3600  # 1 hour
            },
            "actions": [
                {"type": "enable-debug-logging"},
                {"type": "send-diagnostic-report"}
            ]
        }
    ],
    "health_checks": [
        {
            "name": "WebSocket Connectivity",
            "interval": 60,
            "timeout": 10,
            "critical": True
        },
        {
            "name": "Disk Space",
            "interval": 300,
            "threshold": 1024,  # MB
            "critical": True
        }
    ]
}

# Configure monitoring
response = requests.post(f"{BASE_URL}/monitoring/configure", 
                        json=monitoring_config, headers=headers)

# Get real-time metrics via WebSocket
ws = websocket.create_connection("ws://localhost:8080/ws")
ws.send(json.dumps({
    "type": "subscribe",
    "channel": "system-metrics",
    "interval": 5000  # 5 seconds
}))

while True:
    message = json.loads(ws.recv())
    if message['type'] == 'system-metrics':
        metrics = message['data']
        print(f"CPU: {metrics['cpu']}%, Memory: {metrics['memory']}%, Disk: {metrics['disk']}%")`,

      javascript: `// Real-time monitoring dashboard
class MonitoringDashboard {
    constructor() {
        this.ws = new WebSocket('ws://localhost:8080/ws');
        this.metrics = {};
        this.alerts = [];
        this.setupWebSocket();
    }
    
    setupWebSocket() {
        this.ws.onopen = () => {
            // Subscribe to multiple monitoring channels
            this.subscribe('system-metrics');
            this.subscribe('operation-status');
            this.subscribe('alert-notifications');
            this.subscribe('performance-counters');
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
    }
    
    subscribe(channel) {
        this.ws.send(JSON.stringify({
            type: 'subscribe',
            channel: channel,
            options: {
                interval: 1000,  // Real-time updates
                includeHistory: 60  // Last 60 data points
            }
        }));
    }
    
    handleMessage(message) {
        switch(message.type) {
            case 'system-metrics':
                this.updateSystemMetrics(message.data);
                break;
            case 'operation-status':
                this.updateOperationStatus(message.data);
                break;
            case 'alert-notification':
                this.handleAlert(message.data);
                break;
            case 'performance-counters':
                this.updatePerformanceCounters(message.data);
                break;
        }
    }
    
    async configureCustomAlert(alertConfig) {
        const response = await fetch(\`\${BASE_URL}/monitoring/alerts\`, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(alertConfig)
        });
        return response.json();
    }
}

// Initialize monitoring
const dashboard = new MonitoringDashboard();`,
    },
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <div className="p-3 bg-blue-500/10 rounded-xl">
          <BookOpen className="h-6 w-6 text-blue-500" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-gradient">
            WebSocket API Documentation
          </h2>
          <p className="text-muted-foreground mt-1">
            Complete WebSocket-based API for real-time file operations and
            system management
          </p>
        </div>
      </div>

      {/* Quick Start Alert */}
      <Alert className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
        <Zap className="h-4 w-4 text-blue-500" />
        <AlertTitle className="text-blue-700 dark:text-blue-300">
          WebSocket-First Architecture
        </AlertTitle>
        <AlertDescription className="text-blue-600 dark:text-blue-400">
          All operations are WebSocket-driven. POST requests trigger actions,
          WebSocket delivers real-time updates. API key:{" "}
          <code className="bg-blue-100 dark:bg-blue-900 px-1 rounded">
            demo-api-key-12345
          </code>
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="architecture">Architecture</TabsTrigger>
          <TabsTrigger value="endpoints">All Endpoints</TabsTrigger>
          <TabsTrigger value="websocket">WebSocket</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-green-200 bg-green-50/50 dark:bg-green-950/20">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <Settings className="h-5 w-5" />
                  Settings API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-green-600 dark:text-green-400 mb-3">
                  Configure all application settings via WebSocket
                </p>
                <div className="space-y-1">
                  <div className="text-xs text-green-500">
                    • POST /api/settings/update
                  </div>
                  <div className="text-xs text-green-500">
                    • POST /api/settings/theme
                  </div>
                  <div className="text-xs text-green-500">
                    • POST /api/settings/notifications
                  </div>
                  <div className="text-xs text-green-500">
                    • POST /api/settings/performance
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Database className="h-5 w-5" />
                  Operations API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-blue-600 dark:text-blue-400 mb-3">
                  File operations with real-time progress
                </p>
                <div className="space-y-1">
                  <div className="text-xs text-blue-500">
                    • POST /api/operations/start
                  </div>
                  <div className="text-xs text-blue-500">
                    • POST /api/operations/control
                  </div>
                  <div className="text-xs text-blue-500">
                    • GET /api/operations/status
                  </div>
                  <div className="text-xs text-blue-500">
                    • POST /api/operations/batch
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-purple-50/50 dark:bg-purple-950/20">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
                  <Clock className="h-5 w-5" />
                  Schedules API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-purple-600 dark:text-purple-400 mb-3">
                  Advanced scheduling with conditions
                </p>
                <div className="space-y-1">
                  <div className="text-xs text-purple-500">
                    • POST /api/schedules/create
                  </div>
                  <div className="text-xs text-purple-500">
                    • POST /api/schedules/toggle
                  </div>
                  <div className="text-xs text-purple-500">
                    • POST /api/schedules/dependency
                  </div>
                  <div className="text-xs text-purple-500">
                    • GET /api/schedules/status
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-orange-200 bg-orange-50/50 dark:bg-orange-950/20">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-700 dark:text-orange-300">
                  <Monitor className="h-5 w-5" />
                  Monitoring API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-orange-600 dark:text-orange-400 mb-3">
                  System monitoring and alerting
                </p>
                <div className="space-y-1">
                  <div className="text-xs text-orange-500">
                    • GET /api/monitoring/metrics
                  </div>
                  <div className="text-xs text-orange-500">
                    • POST /api/monitoring/alerts
                  </div>
                  <div className="text-xs text-orange-500">
                    • GET /api/monitoring/logs
                  </div>
                  <div className="text-xs text-orange-500">
                    • POST /api/monitoring/configure
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Architecture Diagram */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                WebSocket-Driven Architecture
              </CardTitle>
              <CardDescription>
                Complete system architecture showing WebSocket-first
                communication flow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MermaidChart
                chart={architectureChart}
                id="architecture-overview"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Architecture Tab */}
        <TabsContent value="architecture" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  Communication Flow
                </CardTitle>
                <CardDescription>
                  Complete WebSocket message flow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MermaidChart chart={sequenceChart} id="sequence-diagram" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="h-5 w-5" />
                  Data Flow Architecture
                </CardTitle>
                <CardDescription>
                  How data flows through the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MermaidChart chart={dataFlowChart} id="data-flow" />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>WebSocket Channel Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-700 dark:text-blue-300 mb-2">
                    Real-time Channels
                  </h4>
                  <ul className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                    <li>• progress - Live operation progress</li>
                    <li>• events - System event stream</li>
                    <li>• system-status - Service health</li>
                    <li>• notifications - User alerts</li>
                  </ul>
                </div>
                <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">
                    Configuration Channels
                  </h4>
                  <ul className="text-sm text-green-600 dark:text-green-400 space-y-1">
                    <li>• settings - App configuration</li>
                    <li>• schedules - Task scheduling</li>
                    <li>• user-preferences - UI settings</li>
                    <li>• security-config - Auth settings</li>
                  </ul>
                </div>
                <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200">
                  <h4 className="font-semibold text-purple-700 dark:text-purple-300 mb-2">
                    Monitoring Channels
                  </h4>
                  <ul className="text-sm text-purple-600 dark:text-purple-400 space-y-1">
                    <li>• system-metrics - Performance data</li>
                    <li>• operation-logs - Detailed logs</li>
                    <li>• alert-notifications - System alerts</li>
                    <li>• health-checks - Service monitoring</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* All Endpoints Tab */}
        <TabsContent value="endpoints" className="space-y-6">
          <div className="grid gap-6">
            {/* Settings Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Settings Management Endpoints
                </CardTitle>
                <CardDescription>
                  Configure all application settings via POST requests
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/update</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Update general application settings
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/theme</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Update theme and appearance settings
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/notifications</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Configure notification preferences
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/performance</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Adjust performance and resource settings
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/security</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Configure security and authentication
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/settings/api</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      API configuration and rate limiting
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Operations Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  File Operations Endpoints
                </CardTitle>
                <CardDescription>
                  Control file operations with real-time WebSocket feedback
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/operations/start</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Start upload, download, or sync operation
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/operations/control</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pause, resume, or cancel operations
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/operations/status/:id</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get current status of operation
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/operations/batch</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Execute multiple operations in sequence
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/operations/list</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      List all active and recent operations
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/operations/priority</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Change operation priority and queue order
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Schedules Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Schedule Management Endpoints
                </CardTitle>
                <CardDescription>
                  Advanced scheduling with dependencies and conditions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/schedules/create</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Create new scheduled operation
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/schedules/toggle</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Enable or disable schedule
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/schedules/dependency</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Add dependencies between schedules
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/schedules/status/:id</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get schedule execution status
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/schedules/execute</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Manually trigger schedule execution
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/schedules/history/:id</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get execution history and statistics
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Monitoring Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  System Monitoring Endpoints
                </CardTitle>
                <CardDescription>
                  Comprehensive system monitoring and alerting
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/monitoring/metrics</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get current system metrics
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/monitoring/alerts</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Configure alert conditions
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/monitoring/logs</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Retrieve filtered system logs
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/monitoring/configure</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Configure monitoring settings
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">GET</Badge>
                      <code>/api/monitoring/health</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get comprehensive health status
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">POST</Badge>
                      <code>/api/monitoring/export</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Export metrics and logs data
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* WebSocket Tab */}
        <TabsContent value="websocket" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                WebSocket Communication Protocol
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-lg">
                <h4 className="font-semibold mb-3">Connection Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>URL:</strong> ws://localhost:8080/ws
                    <br />
                    <strong>Protocol:</strong> WebSocket (ws/wss)
                    <br />
                    <strong>Authentication:</strong> Connection-based
                    <br />
                    <strong>Reconnection:</strong> Automatic with backoff
                  </div>
                  <div>
                    <strong>Message Format:</strong> JSON
                    <br />
                    <strong>Encoding:</strong> UTF-8
                    <br />
                    <strong>Compression:</strong> Optional gzip
                    <br />
                    <strong>Max Message Size:</strong> 10MB
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Channel Subscription</h4>
                <CodeBlock
                  id="ws-subscribe"
                  language="json"
                  code={`{
  "type": "subscribe",
  "channel": "progress",
  "options": {
    "interval": 1000,
    "includeHistory": 10,
    "filters": {
      "operationType": ["upload", "sync"],
      "status": ["running", "paused"]
    }
  }
}

// Available channels:
// - progress: Real-time operation progress
// - events: System event stream  
// - settings: Configuration changes
// - system-status: Service health updates
// - notifications: User notifications
// - schedules: Schedule execution events
// - system-metrics: Performance metrics
// - operation-logs: Detailed operation logs
// - alert-notifications: System alerts
// - health-checks: Service monitoring`}
                />
              </div>

              <div>
                <h4 className="font-semibold mb-3">Message Types</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200">
                      <h5 className="font-medium text-blue-700 dark:text-blue-300">
                        Inbound (Client → Server)
                      </h5>
                      <ul className="text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1">
                        <li>• subscribe - Subscribe to channel</li>
                        <li>• unsubscribe - Unsubscribe from channel</li>
                        <li>• service-command - Execute service command</li>
                        <li>• settings-update - Update configuration</li>
                        <li>• operation-control - Control file operations</li>
                      </ul>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded border border-green-200">
                      <h5 className="font-medium text-green-700 dark:text-green-300">
                        Outbound (Server → Client)
                      </h5>
                      <ul className="text-sm text-green-600 dark:text-green-400 mt-2 space-y-1">
                        <li>• progress - Operation progress updates</li>
                        <li>• event - System events and logs</li>
                        <li>• notification - User notifications</li>
                        <li>• settings-changed - Config updates</li>
                        <li>• system-metrics - Performance data</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Examples Tab */}
        <TabsContent value="examples" className="space-y-6">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Settings Configuration Examples
                </CardTitle>
                <CardDescription>
                  Examples for configuring application settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="curl" className="w-full">
                  <TabsList>
                    <TabsTrigger value="curl">cURL</TabsTrigger>
                    <TabsTrigger value="python">Python</TabsTrigger>
                    <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  </TabsList>
                  <TabsContent value="curl">
                    <CodeBlock
                      id="settings-curl"
                      language="bash"
                      code={endpoints.settings.curl}
                    />
                  </TabsContent>
                  <TabsContent value="python">
                    <CodeBlock
                      id="settings-python"
                      language="python"
                      code={endpoints.settings.python}
                    />
                  </TabsContent>
                  <TabsContent value="javascript">
                    <CodeBlock
                      id="settings-js"
                      language="javascript"
                      code={endpoints.settings.javascript}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  File Operations Examples
                </CardTitle>
                <CardDescription>
                  Examples for file upload, download, and sync operations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="curl" className="w-full">
                  <TabsList>
                    <TabsTrigger value="curl">cURL</TabsTrigger>
                    <TabsTrigger value="python">Python</TabsTrigger>
                    <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  </TabsList>
                  <TabsContent value="curl">
                    <CodeBlock
                      id="operations-curl"
                      language="bash"
                      code={endpoints.operations.curl}
                    />
                  </TabsContent>
                  <TabsContent value="python">
                    <CodeBlock
                      id="operations-python"
                      language="python"
                      code={endpoints.operations.python}
                    />
                  </TabsContent>
                  <TabsContent value="javascript">
                    <CodeBlock
                      id="operations-js"
                      language="javascript"
                      code={endpoints.operations.javascript}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Advanced Scheduling Examples
                </CardTitle>
                <CardDescription>
                  Examples for creating complex schedules with dependencies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="curl" className="w-full">
                  <TabsList>
                    <TabsTrigger value="curl">cURL</TabsTrigger>
                    <TabsTrigger value="python">Python</TabsTrigger>
                    <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  </TabsList>
                  <TabsContent value="curl">
                    <CodeBlock
                      id="schedules-curl"
                      language="bash"
                      code={endpoints.schedules.curl}
                    />
                  </TabsContent>
                  <TabsContent value="python">
                    <CodeBlock
                      id="schedules-python"
                      language="python"
                      code={endpoints.schedules.python}
                    />
                  </TabsContent>
                  <TabsContent value="javascript">
                    <CodeBlock
                      id="schedules-js"
                      language="javascript"
                      code={endpoints.schedules.javascript}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                System Monitoring & Alerting
              </CardTitle>
              <CardDescription>
                Comprehensive monitoring examples with real-time metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="curl" className="w-full">
                <TabsList>
                  <TabsTrigger value="curl">cURL</TabsTrigger>
                  <TabsTrigger value="python">Python</TabsTrigger>
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                </TabsList>
                <TabsContent value="curl">
                  <CodeBlock
                    id="monitoring-curl"
                    language="bash"
                    code={endpoints.monitoring.curl}
                  />
                </TabsContent>
                <TabsContent value="python">
                  <CodeBlock
                    id="monitoring-python"
                    language="python"
                    code={endpoints.monitoring.python}
                  />
                </TabsContent>
                <TabsContent value="javascript">
                  <CodeBlock
                    id="monitoring-js"
                    language="javascript"
                    code={endpoints.monitoring.javascript}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Available Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200">
                    <h5 className="font-medium text-blue-700 dark:text-blue-300">
                      System Metrics
                    </h5>
                    <ul className="text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1">
                      <li>• CPU usage percentage</li>
                      <li>• Memory usage (used/total)</li>
                      <li>• Disk space (free/total)</li>
                      <li>• Network I/O rates</li>
                      <li>• System uptime</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded border border-green-200">
                    <h5 className="font-medium text-green-700 dark:text-green-300">
                      Application Metrics
                    </h5>
                    <ul className="text-sm text-green-600 dark:text-green-400 mt-2 space-y-1">
                      <li>• Active operations count</li>
                      <li>• WebSocket connections</li>
                      <li>• API request rates</li>
                      <li>• Error rates by category</li>
                      <li>• Response times</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alert Conditions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-orange-50 dark:bg-orange-950/20 rounded border border-orange-200">
                    <h5 className="font-medium text-orange-700 dark:text-orange-300">
                      Performance Alerts
                    </h5>
                    <ul className="text-sm text-orange-600 dark:text-orange-400 mt-2 space-y-1">
                      <li>• High CPU usage (&gt;85%)</li>
                      <li>• Low memory (&lt;100MB)</li>
                      <li>• Disk space critical (&lt;1GB)</li>
                      <li>• High error rate (&gt;5%)</li>
                      <li>• Slow response times (&gt;2s)</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-red-50 dark:bg-red-950/20 rounded border border-red-200">
                    <h5 className="font-medium text-red-700 dark:text-red-300">
                      Critical Alerts
                    </h5>
                    <ul className="text-sm text-red-600 dark:text-red-400 mt-2 space-y-1">
                      <li>• Service unavailable</li>
                      <li>• WebSocket disconnections</li>
                      <li>• Operation failures</li>
                      <li>• Security breaches</li>
                      <li>• Data corruption detected</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
