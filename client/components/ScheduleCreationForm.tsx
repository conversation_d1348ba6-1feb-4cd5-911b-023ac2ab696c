import { useState } from "react";
import {
  Calendar,
  Clock,
  FolderOpen,
  Plus,
  X,
  <PERSON>ting<PERSON>,
  Save,
  AlertCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { motion, AnimatePresence } from "framer-motion";

interface ScheduleFormData {
  name: string;
  sourceFolders: string[];
  interval: "daily" | "weekly" | "monthly";
  time: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  isActive: boolean;
  exclusions: string[];
  description?: string;
}

interface ScheduleCreationFormProps {
  onSubmit: (data: ScheduleFormData) => Promise<boolean>;
  onCancel: () => void;
  loading?: boolean;
}

const dayOfWeekOptions = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

export function ScheduleCreationForm({
  onSubmit,
  onCancel,
  loading = false,
}: ScheduleCreationFormProps) {
  const [formData, setFormData] = useState<ScheduleFormData>({
    name: "",
    sourceFolders: [],
    interval: "daily",
    time: "09:00",
    isActive: true,
    exclusions: [],
    description: "",
  });

  const [newExclusion, setNewExclusion] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Schedule name is required";
    }

    if (formData.sourceFolders.length === 0) {
      newErrors.sourceFolders = "At least one source folder is required";
    }

    if (!formData.time.trim()) {
      newErrors.time = "Time is required";
    }

    if (formData.interval === "weekly" && formData.dayOfWeek === undefined) {
      newErrors.dayOfWeek = "Day of week is required for weekly schedules";
    }

    if (formData.interval === "monthly" && formData.dayOfMonth === undefined) {
      newErrors.dayOfMonth = "Day of month is required for monthly schedules";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Add default values for destination folder and client ID
    const scheduleData = {
      ...formData,
      sourceFolder: formData.sourceFolders[0], // Use first folder as primary for API compatibility
      destinationFolder: "/default/destination", // Default destination
      clientId: "DEFAULT_CLIENT", // Default client ID
    };

    const success = await onSubmit(scheduleData);
    if (success) {
      // Reset form on success
      setFormData({
        name: "",
        sourceFolders: [],
        interval: "daily",
        time: "09:00",
        isActive: true,
        exclusions: [],
        description: "",
      });
    }
  };

  const addExclusion = () => {
    if (
      newExclusion.trim() &&
      !formData.exclusions.includes(newExclusion.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        exclusions: [...prev.exclusions, newExclusion.trim()],
      }));
      setNewExclusion("");
    }
  };

  const removeExclusion = (exclusion: string) => {
    setFormData((prev) => ({
      ...prev,
      exclusions: prev.exclusions.filter((e) => e !== exclusion),
    }));
  };

  const selectSourceFolder = async () => {
    try {
      // This would integrate with the file system API
      const result = await (window as any).electronAPI?.selectFolder();
      if (result && !formData.sourceFolders.includes(result)) {
        setFormData((prev) => ({
          ...prev,
          sourceFolders: [...prev.sourceFolders, result],
        }));
      }
    } catch (error) {
      // Fallback to manual input
      console.log("Folder selection not available, use manual input");
    }
  };

  const selectExclusionPattern = async () => {
    try {
      // This would integrate with the file system API to select files/folders for exclusion
      const result = await (window as any).electronAPI?.selectFolder();
      if (result && !formData.exclusions.includes(result)) {
        setFormData((prev) => ({
          ...prev,
          exclusions: [...prev.exclusions, result],
        }));
      }
    } catch (error) {
      // Fallback to manual input
      console.log("File selection not available, use manual input");
    }
  };

  const removeSourceFolder = (folderToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      sourceFolders: prev.sourceFolders.filter(
        (folder) => folder !== folderToRemove,
      ),
    }));
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-500/10 rounded-lg">
          <Calendar className="h-5 w-5 text-blue-500" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gradient">
            Create New Schedule
          </h3>
          <p className="text-sm text-muted-foreground">
            Set up a new automated sync schedule
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card className="p-4">
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Basic Information
          </h4>

          <div className="space-y-2">
            <Label htmlFor="name">Schedule Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="e.g., Daily Document Sync"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief description of what this schedule does..."
              className="resize-none"
              rows={2}
            />
          </div>
        </Card>

        {/* Folder Configuration */}
        <Card className="p-4">
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Source Folders Configuration
          </h4>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Source Folders *</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add source folder path..."
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      const value = e.currentTarget.value.trim();
                      if (value && !formData.sourceFolders.includes(value)) {
                        setFormData((prev) => ({
                          ...prev,
                          sourceFolders: [...prev.sourceFolders, value],
                        }));
                        e.currentTarget.value = "";
                      }
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectSourceFolder}
                >
                  <FolderOpen className="h-4 w-4 mr-1" />
                  Browse
                </Button>
              </div>
              {errors.sourceFolders && (
                <p className="text-sm text-red-500">{errors.sourceFolders}</p>
              )}
            </div>

            {/* Source Folders List */}
            {formData.sourceFolders.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm text-muted-foreground">
                  Selected Folders:
                </Label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  <AnimatePresence>
                    {formData.sourceFolders.map((folder, index) => (
                      <motion.div
                        key={folder}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2 p-2 bg-muted/50 rounded-md"
                      >
                        <FolderOpen className="h-4 w-4 text-muted-foreground" />
                        <span className="flex-1 text-sm truncate">
                          {folder}
                        </span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSourceFolder(folder)}
                          className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Schedule Configuration */}
        <Card className="p-4">
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Schedule Configuration
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="space-y-2">
              <Label htmlFor="interval">Frequency *</Label>
              <Select
                value={formData.interval}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    interval: value as "daily" | "weekly" | "monthly",
                    dayOfWeek: value === "weekly" ? 1 : undefined,
                    dayOfMonth: value === "monthly" ? 1 : undefined,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <Input
                id="time"
                type="time"
                value={formData.time}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, time: e.target.value }))
                }
                className={errors.time ? "border-red-500" : ""}
              />
              {errors.time && (
                <p className="text-sm text-red-500">{errors.time}</p>
              )}
            </div>

            {formData.interval === "weekly" && (
              <div className="space-y-2">
                <Label htmlFor="dayOfWeek">Day of Week *</Label>
                <Select
                  value={formData.dayOfWeek?.toString()}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      dayOfWeek: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger
                    className={errors.dayOfWeek ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {dayOfWeekOptions.map((day) => (
                      <SelectItem key={day.value} value={day.value.toString()}>
                        {day.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.dayOfWeek && (
                  <p className="text-sm text-red-500">{errors.dayOfWeek}</p>
                )}
              </div>
            )}

            {formData.interval === "monthly" && (
              <div className="space-y-2">
                <Label htmlFor="dayOfMonth">Day of Month *</Label>
                <Select
                  value={formData.dayOfMonth?.toString()}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      dayOfMonth: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger
                    className={errors.dayOfMonth ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 28 }, (_, i) => i + 1).map((day) => (
                      <SelectItem key={day} value={day.toString()}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.dayOfMonth && (
                  <p className="text-sm text-red-500">{errors.dayOfMonth}</p>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, isActive: checked }))
              }
            />
            <Label htmlFor="isActive" className="text-sm">
              Enable schedule immediately
            </Label>
          </div>
        </Card>

        {/* File Exclusions */}
        <Card className="p-4">
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <X className="h-4 w-4" />
            File Exclusions (Optional)
          </h4>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={newExclusion}
                onChange={(e) => setNewExclusion(e.target.value)}
                placeholder="e.g., *.tmp, node_modules, .git"
                onKeyPress={(e) =>
                  e.key === "Enter" && (e.preventDefault(), addExclusion())
                }
              />
              <Button type="button" variant="outline" onClick={addExclusion}>
                <Plus className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={selectExclusionPattern}
                title="Browse for folder to exclude"
              >
                <FolderOpen className="h-4 w-4" />
              </Button>
            </div>

            {formData.exclusions.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <AnimatePresence>
                  {formData.exclusions.map((exclusion, index) => (
                    <motion.div
                      key={exclusion}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Badge
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {exclusion}
                        <button
                          type="button"
                          onClick={() => removeExclusion(exclusion)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Use patterns like *.tmp for file extensions, or exact folder
                names to exclude. Separate multiple patterns with commas.
              </AlertDescription>
            </Alert>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="btn-modern">
            {loading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Save className="h-4 w-4 mr-2" />
              </motion.div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Create Schedule
          </Button>
        </div>
      </form>
    </motion.div>
  );
}
