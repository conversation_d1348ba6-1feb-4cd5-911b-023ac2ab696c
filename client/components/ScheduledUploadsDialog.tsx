import { useState, useEffect } from "react"
import { Clock, Calendar, PlayCircle, StopCircle, Trash2, Edit, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { uploadSchedulerService } from "@/services/uploadSchedulerService"

interface ScheduledUploadsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface ScheduledUpload {
  id: string
  files: File[]
  scheduledTime: Date
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled'
  folderId?: string
  description?: string
  createdAt: Date
  progress?: number
  error?: string
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return <Clock className="h-4 w-4 text-blue-500" />
    case 'uploading':
      return <PlayCircle className="h-4 w-4 text-yellow-500" />
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case 'failed':
      return <XCircle className="h-4 w-4 text-red-500" />
    case 'cancelled':
      return <StopCircle className="h-4 w-4 text-gray-500" />
    default:
      return <AlertCircle className="h-4 w-4 text-gray-500" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'uploading':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

export function ScheduledUploadsDialog({ open, onOpenChange }: ScheduledUploadsDialogProps) {
  const [uploads, setUploads] = useState<ScheduledUpload[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open) {
      loadUploads()
      
      // Set up polling to refresh uploads
      const interval = setInterval(loadUploads, 5000)
      return () => clearInterval(interval)
    }
  }, [open])

  const loadUploads = () => {
    setLoading(true)
    try {
      const scheduledUploads = uploadSchedulerService.getScheduledUploads()
      setUploads(scheduledUploads)
    } catch (error) {
      console.error('Failed to load scheduled uploads:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = (id: string) => {
    const success = uploadSchedulerService.cancelScheduledUpload(id)
    if (success) {
      loadUploads()
    } else {
      alert('Failed to cancel upload')
    }
  }

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this scheduled upload?')) {
      const success = uploadSchedulerService.deleteScheduledUpload(id)
      if (success) {
        loadUploads()
      } else {
        alert('Failed to delete upload')
      }
    }
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString()
  }

  const getTimeRemaining = (scheduledTime: Date) => {
    const now = new Date()
    const diff = scheduledTime.getTime() - now.getTime()
    
    if (diff <= 0) return 'Overdue'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}d ${hours % 24}h`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const pendingUploads = uploads.filter(u => u.status === 'pending')
  const activeUploads = uploads.filter(u => u.status === 'uploading')
  const completedUploads = uploads.filter(u => ['completed', 'failed', 'cancelled'].includes(u.status))

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Scheduled Uploads ({uploads.length})
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-4 gap-4">
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">{pendingUploads.length}</div>
              <div className="text-xs text-muted-foreground">Pending</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-yellow-600">{activeUploads.length}</div>
              <div className="text-xs text-muted-foreground">Uploading</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-green-600">
                {uploads.filter(u => u.status === 'completed').length}
              </div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </Card>
            <Card className="p-3 text-center">
              <div className="text-2xl font-bold text-red-600">
                {uploads.filter(u => u.status === 'failed').length}
              </div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </Card>
          </div>

          <ScrollArea className="h-[400px]">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-muted-foreground">Loading scheduled uploads...</div>
              </div>
            ) : uploads.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 text-center">
                <Calendar className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="font-medium">No scheduled uploads</h3>
                <p className="text-sm text-muted-foreground">
                  Use the "Schedule Upload" feature to upload files at specific times
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Active Uploads */}
                {activeUploads.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2 flex items-center gap-2">
                      <PlayCircle className="h-4 w-4 text-yellow-500" />
                      Currently Uploading
                    </h3>
                    <div className="space-y-2">
                      {activeUploads.map((upload) => (
                        <Card key={upload.id} className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(upload.status)}
                                <Badge className={getStatusColor(upload.status)}>
                                  {upload.status.toUpperCase()}
                                </Badge>
                                <span className="font-medium">
                                  {upload.files.length} file(s)
                                </span>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {formatDateTime(upload.scheduledTime)}
                              </span>
                            </div>
                            
                            {upload.description && (
                              <p className="text-sm text-muted-foreground">{upload.description}</p>
                            )}
                            
                            <Progress value={upload.progress || 0} className="w-full" />
                            <div className="text-xs text-muted-foreground">
                              {upload.progress || 0}% complete
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* Pending Uploads */}
                {pendingUploads.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      Pending Uploads
                    </h3>
                    <div className="space-y-2">
                      {pendingUploads.map((upload) => (
                        <Card key={upload.id} className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(upload.status)}
                                <Badge className={getStatusColor(upload.status)}>
                                  {upload.status.toUpperCase()}
                                </Badge>
                                <span className="font-medium">
                                  {upload.files.length} file(s)
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  in {getTimeRemaining(upload.scheduledTime)}
                                </span>
                              </div>
                              
                              <div className="text-sm text-muted-foreground">
                                Scheduled: {formatDateTime(upload.scheduledTime)}
                              </div>
                              
                              {upload.description && (
                                <p className="text-sm text-muted-foreground">{upload.description}</p>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCancel(upload.id)}
                                className="gap-1"
                              >
                                <StopCircle className="h-3 w-3" />
                                Cancel
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(upload.id)}
                                className="gap-1 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* Completed Uploads */}
                {completedUploads.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Recent Activity</h3>
                    <div className="space-y-2">
                      {completedUploads.slice(0, 5).map((upload) => (
                        <Card key={upload.id} className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(upload.status)}
                              <Badge className={getStatusColor(upload.status)}>
                                {upload.status.toUpperCase()}
                              </Badge>
                              <span className="text-sm">
                                {upload.files.length} file(s)
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {formatDateTime(upload.scheduledTime)}
                              </span>
                            </div>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(upload.id)}
                              className="gap-1 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          {upload.error && (
                            <Alert variant="destructive" className="mt-2">
                              <AlertDescription className="text-xs">
                                {upload.error}
                              </AlertDescription>
                            </Alert>
                          )}
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
