import { useState, useRef } from "react";
import {
  Upload,
  X,
  File,
  FileText,
  Image,
  Video,
  AlertCircle,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { fileUploadService } from "@/services/fileUploadService";

interface UploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: "pending" | "uploading" | "completed" | "error";
  errorMessage?: string;
  uploadedFileId?: string;
}

const getFileIcon = (file: File) => {
  const type = file.type;
  if (type.startsWith("image/"))
    return <Image className="h-4 w-4 text-purple-500" />;
  if (type.startsWith("video/"))
    return <Video className="h-4 w-4 text-red-500" />;
  if (type.includes("text") || type.includes("document"))
    return <FileText className="h-4 w-4 text-blue-500" />;
  return <File className="h-4 w-4 text-gray-500" />;
};

export function UploadDialog({ open, onOpenChange }: UploadDialogProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadFile[] = Array.from(files).map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      progress: 0,
      status: "pending",
    }));

    setUploadFiles((prev) => [...prev, ...newFiles]);
    setApiKeyError(null);

    // Start real upload
    try {
      const filesToUpload = Array.from(files);
      await fileUploadService.uploadFiles(
        filesToUpload,
        (fileIndex, progress) => {
          const targetFile = newFiles[fileIndex];
          if (targetFile) {
            setUploadFiles((prev) =>
              prev.map((f) =>
                f.id === targetFile.id
                  ? {
                      ...f,
                      progress: progress.percentage,
                      status:
                        progress.percentage >= 100 ? "completed" : "uploading",
                    }
                  : f,
              ),
            );
          }
        },
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Upload failed";
      setApiKeyError(errorMessage);

      // Mark all new files as error
      setUploadFiles((prev) =>
        prev.map((f) => {
          const isNewFile = newFiles.some((nf) => nf.id === f.id);
          return isNewFile ? { ...f, status: "error", errorMessage } : f;
        }),
      );
    }
  };

  const removeFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleClose = () => {
    setUploadFiles([]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Upload Files</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-y-auto pr-2 dialog-scrollbar">
          {/* API Configuration Alert */}
          {apiKeyError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {apiKeyError.includes("Network") ||
                apiKeyError.includes("CORS") ? (
                  <>
                    API connection failed. Please ensure:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Your API key is configured in the service</li>
                      <li>The API endpoint URL is correct</li>
                      <li>CORS is properly configured on the server</li>
                    </ul>
                  </>
                ) : (
                  apiKeyError
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Upload Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25",
              "hover:border-primary hover:bg-primary/5",
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
            <div className="space-y-2">
              <p className="text-sm font-medium">
                Drop files here or{" "}
                <Button
                  variant="link"
                  className="p-0 h-auto"
                  onClick={() => fileInputRef.current?.click()}
                >
                  browse
                </Button>
              </p>
              <p className="text-xs text-muted-foreground">
                Supports all file types up to 100MB
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={(e) => handleFileSelect(e.target.files)}
            />
          </div>

          {/* Upload Progress */}
          {uploadFiles.length > 0 && (
            <div className="space-y-3 max-h-60 overflow-y-auto">
              <h4 className="text-sm font-medium">
                Uploading {uploadFiles.length} files
              </h4>
              {uploadFiles.map((uploadFile) => (
                <div
                  key={uploadFile.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  {getFileIcon(uploadFile.file)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {uploadFile.file.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    {uploadFile.status === "uploading" && (
                      <Progress
                        value={uploadFile.progress}
                        className="mt-2 h-1"
                      />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {uploadFile.status === "completed" && (
                      <div
                        className="h-2 w-2 bg-green-500 rounded-full"
                        title="Upload completed"
                      />
                    )}
                    {uploadFile.status === "error" && (
                      <div
                        className="h-2 w-2 bg-red-500 rounded-full"
                        title={uploadFile.errorMessage || "Upload failed"}
                      />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(uploadFile.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
          {uploadFiles.some((f) => f.status === "completed") && (
            <Button onClick={handleClose}>Done</Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
