import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";

export type Theme =
  | "auto"
  | "default"
  | "arctic"
  | "forest"
  | "sunset"
  | "lavender"
  | "midnight"
  | "neon"
  | "rose"
  | "ocean"
  | "custom";

export type ThemeMode = "light" | "dark";

export interface ThemeConfig {
  name: string;
  label: string;
  description: string;
  primaryColor: string;
  gradientFrom: string;
  gradientTo: string;
  mode: ThemeMode;
  category: "light" | "dark" | "auto" | "custom";
  customCSS?: string;
}

export const themeConfigs: Record<Theme, ThemeConfig> = {
  auto: {
    name: "auto",
    label: "System",
    description: "Follows system preference",
    primaryColor: "#3b82f6",
    gradientFrom: "#3b82f6",
    gradientTo: "#06b6d4",
    mode: "light",
    category: "auto",
  },
  default: {
    name: "default",
    label: "Modern Light",
    description: "Clean and professional design",
    primaryColor: "#3b82f6",
    gradientFrom: "#3b82f6",
    gradientTo: "#06b6d4",
    mode: "light",
    category: "light",
  },
  arctic: {
    name: "arctic",
    label: "Arctic Blue",
    description: "Cool and crisp interface",
    primaryColor: "#0ea5e9",
    gradientFrom: "#0ea5e9",
    gradientTo: "#06b6d4",
    mode: "light",
    category: "light",
  },
  forest: {
    name: "forest",
    label: "Forest Green",
    description: "Natural and calming experience",
    primaryColor: "#059669",
    gradientFrom: "#059669",
    gradientTo: "#10b981",
    mode: "light",
    category: "light",
  },
  sunset: {
    name: "sunset",
    label: "Sunset Orange",
    description: "Warm and energetic vibes",
    primaryColor: "#ea580c",
    gradientFrom: "#ea580c",
    gradientTo: "#f97316",
    mode: "light",
    category: "light",
  },
  lavender: {
    name: "lavender",
    label: "Lavender Purple",
    description: "Elegant and refined aesthetics",
    primaryColor: "#8b5cf6",
    gradientFrom: "#8b5cf6",
    gradientTo: "#a855f7",
    mode: "light",
    category: "light",
  },
  midnight: {
    name: "midnight",
    label: "Midnight Dark",
    description: "Rich and immersive dark mode",
    primaryColor: "#3b82f6",
    gradientFrom: "#3b82f6",
    gradientTo: "#06b6d4",
    mode: "dark",
    category: "dark",
  },
  neon: {
    name: "neon",
    label: "Neon Cyberpunk",
    description: "Futuristic neon aesthetic",
    primaryColor: "#10b981",
    gradientFrom: "#10b981",
    gradientTo: "#06b6d4",
    mode: "dark",
    category: "dark",
  },
  rose: {
    name: "rose",
    label: "Rose Gold",
    description: "Elegant rose and gold tones",
    primaryColor: "#f43f5e",
    gradientFrom: "#f43f5e",
    gradientTo: "#ec4899",
    mode: "light",
    category: "light",
  },
  ocean: {
    name: "ocean",
    label: "Deep Ocean",
    description: "Deep blue oceanic theme",
    primaryColor: "#1e40af",
    gradientFrom: "#1e40af",
    gradientTo: "#3730a3",
    mode: "dark",
    category: "dark",
  },
  custom: {
    name: "custom",
    label: "Custom Theme",
    description: "Your personalized theme",
    primaryColor: "#3b82f6",
    gradientFrom: "#3b82f6",
    gradientTo: "#06b6d4",
    mode: "light",
    category: "custom",
  },
};

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableAnimations?: boolean;
  enableSystemDetection?: boolean;
};

type ThemeProviderState = {
  theme: Theme;
  actualTheme: Theme; // Resolved theme (auto -> light/dark)
  themeMode: ThemeMode;
  systemTheme: ThemeMode;
  isLoading: boolean;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  exportTheme: () => string;
  importTheme: (themeData: string) => boolean;
  previewTheme: (theme: Theme) => void;
  clearPreview: () => void;
  isPreviewMode: boolean;
  getThemeConfig: (theme: Theme) => ThemeConfig;
  createCustomTheme: (config: Partial<ThemeConfig>) => void;
};

const initialState: ThemeProviderState = {
  theme: "default",
  actualTheme: "default",
  themeMode: "light",
  systemTheme: "light",
  isLoading: true,
  setTheme: () => null,
  toggleTheme: () => null,
  exportTheme: () => "",
  importTheme: () => false,
  previewTheme: () => null,
  clearPreview: () => null,
  isPreviewMode: false,
  getThemeConfig: (theme) => themeConfigs[theme],
  createCustomTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function EnhancedThemeProvider({
  children,
  defaultTheme = "auto",
  storageKey = "w8file-enhanced-theme",
  enableAnimations = true,
  enableSystemDetection = true,
  ...props
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [actualTheme, setActualTheme] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<ThemeMode>("light");
  const [isLoading, setIsLoading] = useState(true);
  const [previewTheme, setPreviewTheme] = useState<Theme | null>(null);
  const [customThemes, setCustomThemes] = useState<Record<string, ThemeConfig>>(
    {},
  );

  // Detect system theme preference
  const detectSystemTheme = useCallback(() => {
    if (!enableSystemDetection) return "light";
    return window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
  }, [enableSystemDetection]);

  // Resolve actual theme from user preference
  const resolveActualTheme = useCallback(
    (userTheme: Theme, systemMode: ThemeMode): Theme => {
      if (userTheme === "auto") {
        return systemMode === "dark" ? "midnight" : "default";
      }
      return userTheme;
    },
    [],
  );

  // Apply theme to document
  const applyTheme = useCallback(
    (themeToApply: Theme, isPreview = false) => {
      const root = window.document.documentElement;

      if (enableAnimations && !isPreview) {
        root.style.setProperty(
          "--theme-transition",
          "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        );
      }

      // Remove all theme classes
      Object.keys(themeConfigs).forEach((themeName) => {
        if (themeName !== "auto") {
          root.classList.remove(`theme-${themeName}`);
        }
      });

      // Add current theme class (default theme has no class)
      if (themeToApply !== "default" && themeToApply !== "auto") {
        root.classList.add(`theme-${themeToApply}`);
      }

      // Add theme mode class
      const config = themeConfigs[themeToApply];
      root.classList.remove("light", "dark");
      root.classList.add(config.mode);

      // Apply custom CSS if it exists
      const customStyleId = "custom-theme-styles";
      let customStyleEl = document.getElementById(customStyleId);

      if (config.customCSS) {
        if (!customStyleEl) {
          customStyleEl = document.createElement("style");
          customStyleEl.id = customStyleId;
          document.head.appendChild(customStyleEl);
        }
        customStyleEl.textContent = config.customCSS;
      } else if (customStyleEl) {
        customStyleEl.remove();
      }

      // Set CSS custom properties for dynamic theming
      root.style.setProperty("--theme-primary", config.primaryColor);
      root.style.setProperty("--theme-gradient-from", config.gradientFrom);
      root.style.setProperty("--theme-gradient-to", config.gradientTo);

      // Reset transition after theme application
      if (enableAnimations && !isPreview) {
        setTimeout(() => {
          root.style.removeProperty("--theme-transition");
        }, 300);
      }
    },
    [enableAnimations],
  );

  // Initialize theme from localStorage
  useEffect(() => {
    try {
      const storedTheme = localStorage.getItem(storageKey) as Theme;
      const storedCustomThemes = localStorage.getItem(`${storageKey}-custom`);

      if (storedCustomThemes) {
        setCustomThemes(JSON.parse(storedCustomThemes));
      }

      const initialTheme =
        storedTheme && Object.keys(themeConfigs).includes(storedTheme)
          ? storedTheme
          : defaultTheme;

      const currentSystemTheme = detectSystemTheme();
      setSystemTheme(currentSystemTheme);
      setThemeState(initialTheme);

      const resolved = resolveActualTheme(initialTheme, currentSystemTheme);
      setActualTheme(resolved);
      applyTheme(resolved);

      setIsLoading(false);
    } catch (error) {
      console.error("Error loading theme:", error);
      setIsLoading(false);
    }
  }, [
    defaultTheme,
    storageKey,
    detectSystemTheme,
    resolveActualTheme,
    applyTheme,
  ]);

  // Listen for system theme changes
  useEffect(() => {
    if (!enableSystemDetection) return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = (e: MediaQueryListEvent) => {
      const newSystemTheme = e.matches ? "dark" : "light";
      setSystemTheme(newSystemTheme);

      if (theme === "auto") {
        const resolved = resolveActualTheme(theme, newSystemTheme);
        setActualTheme(resolved);
        applyTheme(resolved);
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [theme, enableSystemDetection, resolveActualTheme, applyTheme]);

  const setTheme = useCallback(
    (newTheme: Theme) => {
      setThemeState(newTheme);
      localStorage.setItem(storageKey, newTheme);

      const resolved = resolveActualTheme(newTheme, systemTheme);
      setActualTheme(resolved);
      applyTheme(resolved);
    },
    [storageKey, systemTheme, resolveActualTheme, applyTheme],
  );

  const toggleTheme = useCallback(() => {
    const currentConfig = themeConfigs[actualTheme];
    const newTheme = currentConfig.mode === "light" ? "midnight" : "default";
    setTheme(newTheme);
  }, [actualTheme, setTheme]);

  const exportTheme = useCallback(() => {
    const themeData = {
      theme,
      customThemes,
      timestamp: new Date().toISOString(),
      version: "1.0",
    };
    return JSON.stringify(themeData, null, 2);
  }, [theme, customThemes]);

  const importTheme = useCallback(
    (themeData: string): boolean => {
      try {
        const parsed = JSON.parse(themeData);
        if (parsed.theme && Object.keys(themeConfigs).includes(parsed.theme)) {
          setTheme(parsed.theme);
          if (parsed.customThemes) {
            setCustomThemes(parsed.customThemes);
            localStorage.setItem(
              `${storageKey}-custom`,
              JSON.stringify(parsed.customThemes),
            );
          }
          return true;
        }
        return false;
      } catch {
        return false;
      }
    },
    [setTheme, storageKey],
  );

  const handlePreviewTheme = useCallback(
    (themeToPreview: Theme) => {
      setPreviewTheme(themeToPreview);
      applyTheme(themeToPreview, true);
    },
    [applyTheme],
  );

  const clearPreview = useCallback(() => {
    if (previewTheme) {
      setPreviewTheme(null);
      applyTheme(actualTheme);
    }
  }, [previewTheme, actualTheme, applyTheme]);

  const getThemeConfig = useCallback(
    (themeName: Theme) => {
      return customThemes[themeName] || themeConfigs[themeName];
    },
    [customThemes],
  );

  const createCustomTheme = useCallback(
    (config: Partial<ThemeConfig>) => {
      const customTheme: ThemeConfig = {
        name: config.name || "custom",
        label: config.label || "Custom Theme",
        description: config.description || "A custom theme",
        primaryColor: config.primaryColor || "#3b82f6",
        gradientFrom: config.gradientFrom || config.primaryColor || "#3b82f6",
        gradientTo: config.gradientTo || config.primaryColor || "#3b82f6",
        mode: config.mode || "light",
        category: "custom",
        customCSS: config.customCSS,
      };

      const updatedCustomThemes = {
        ...customThemes,
        [customTheme.name]: customTheme,
      };
      setCustomThemes(updatedCustomThemes);
      localStorage.setItem(
        `${storageKey}-custom`,
        JSON.stringify(updatedCustomThemes),
      );
    },
    [customThemes, storageKey],
  );

  const value: ThemeProviderState = {
    theme,
    actualTheme,
    themeMode: themeConfigs[actualTheme].mode,
    systemTheme,
    isLoading,
    setTheme,
    toggleTheme,
    exportTheme,
    importTheme,
    previewTheme: handlePreviewTheme,
    clearPreview,
    isPreviewMode: previewTheme !== null,
    getThemeConfig,
    createCustomTheme,
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useEnhancedTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined) {
    throw new Error(
      "useEnhancedTheme must be used within an EnhancedThemeProvider",
    );
  }

  return context;
};
