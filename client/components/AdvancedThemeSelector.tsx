import { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Sun,
  Moon,
  Download,
  Upload,
  Plus,
  <PERSON>tings,
  Eye,
  EyeOff,
  Sparkles,
  Wand2,
  <PERSON>,
  X,
  Co<PERSON>,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import {
  useEnhancedTheme,
  themeConfigs,
  Theme,
  ThemeConfig,
} from "@/components/EnhancedThemeProvider";

const themeCategories = {
  auto: "Auto",
  light: "Light Themes",
  dark: "Dark Themes",
  custom: "Custom Themes",
};

const getThemeIcon = (theme: Theme) => {
  switch (theme) {
    case "auto":
      return <Monitor className="h-4 w-4" />;
    case "midnight":
    case "neon":
    case "ocean":
      return <Moon className="h-4 w-4" />;
    default:
      return <Sun className="h-4 w-4" />;
  }
};

const getThemeColorPreview = (config: ThemeConfig) => {
  return (
    <div className="flex gap-1">
      <div
        className="w-3 h-3 rounded-full border border-white/20"
        style={{ background: config.gradientFrom }}
      />
      <div
        className="w-3 h-3 rounded-full border border-white/20"
        style={{ background: config.gradientTo }}
      />
    </div>
  );
};

export function AdvancedThemeSelector() {
  const {
    theme,
    setTheme,
    toggleTheme,
    exportTheme,
    importTheme,
    previewTheme,
    clearPreview,
    isPreviewMode,
    getThemeConfig,
    createCustomTheme,
  } = useEnhancedTheme();

  const [isOpen, setIsOpen] = useState(false);
  const [previewingTheme, setPreviewingTheme] = useState<Theme | null>(null);
  const [showCustomBuilder, setShowCustomBuilder] = useState(false);
  const [customThemeData, setCustomThemeData] = useState<Partial<ThemeConfig>>({
    name: "",
    label: "",
    description: "",
    primaryColor: "#3b82f6",
    gradientFrom: "#3b82f6",
    gradientTo: "#06b6d4",
    mode: "light",
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const groupedThemes = Object.entries(themeConfigs).reduce(
    (acc, [key, config]) => {
      const category = config.category;
      if (!acc[category]) acc[category] = [];
      acc[category].push({ key: key as Theme, config });
      return acc;
    },
    {} as Record<string, Array<{ key: Theme; config: ThemeConfig }>>,
  );

  const handlePreview = (themeKey: Theme) => {
    if (previewingTheme === themeKey) {
      clearPreview();
      setPreviewingTheme(null);
    } else {
      previewTheme(themeKey);
      setPreviewingTheme(themeKey);
    }
  };

  const handleExport = () => {
    const data = exportTheme();
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `w8file-theme-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (importTheme(content)) {
          alert("Theme imported successfully!");
        } else {
          alert("Failed to import theme. Please check the file format.");
        }
      };
      reader.readAsText(file);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const createCustom = () => {
    if (customThemeData.name && customThemeData.label) {
      createCustomTheme(customThemeData);
      setShowCustomBuilder(false);
      setCustomThemeData({
        name: "",
        label: "",
        description: "",
        primaryColor: "#3b82f6",
        gradientFrom: "#3b82f6",
        gradientTo: "#06b6d4",
        mode: "light",
      });
    }
  };

  const currentConfig = getThemeConfig(theme);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="gap-2 hover:bg-primary/10 transition-all duration-200"
        >
          <div className="flex items-center gap-2">
            {getThemeIcon(theme)}
            <Sparkles className="h-3 w-3 text-primary" />
          </div>
          <span className="hidden sm:inline">Theme</span>
          {isPreviewMode && (
            <Badge
              variant="secondary"
              className="text-xs bg-primary/20 text-primary animate-pulse"
            >
              Preview
            </Badge>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl">
              <Palette className="h-5 w-5 text-primary" />
            </div>
            <div>
              <span className="text-gradient">Advanced Theme Studio</span>
              <div className="text-sm text-muted-foreground font-normal">
                Customize your application's appearance
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto dialog-scrollbar">
          <Tabs defaultValue="themes" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-6">
              <TabsTrigger value="themes" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Themes
              </TabsTrigger>
              <TabsTrigger value="custom" className="flex items-center gap-2">
                <Wand2 className="h-4 w-4" />
                Builder
              </TabsTrigger>
              <TabsTrigger value="manage" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Manage
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="themes" className="space-y-6">
              {/* Quick Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    onClick={toggleTheme}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    {currentConfig.mode === "light" ? (
                      <Moon className="h-4 w-4" />
                    ) : (
                      <Sun className="h-4 w-4" />
                    )}
                    Toggle Mode
                  </Button>

                  {isPreviewMode && (
                    <Button
                      onClick={() => {
                        clearPreview();
                        setPreviewingTheme(null);
                      }}
                      variant="destructive"
                      size="sm"
                      className="gap-2"
                    >
                      <EyeOff className="h-4 w-4" />
                      Exit Preview
                    </Button>
                  )}
                </div>

                <Badge variant="outline" className="gap-2">
                  Current: {currentConfig.label}
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ background: currentConfig.primaryColor }}
                  />
                </Badge>
              </div>

              {/* Theme Grid */}
              <div className="space-y-8">
                {Object.entries(groupedThemes).map(([category, themes]) => (
                  <div key={category}>
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      {category === "auto" && <Monitor className="h-4 w-4" />}
                      {category === "light" && <Sun className="h-4 w-4" />}
                      {category === "dark" && <Moon className="h-4 w-4" />}
                      {category === "custom" && <Wand2 className="h-4 w-4" />}
                      {
                        themeCategories[
                          category as keyof typeof themeCategories
                        ]
                      }
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <AnimatePresence>
                        {themes.map(({ key, config }) => (
                          <motion.div
                            key={key}
                            layout
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Card
                              className={`relative p-4 cursor-pointer transition-all duration-200 hover:shadow-lg group ${
                                theme === key
                                  ? "ring-2 ring-primary shadow-lg shadow-primary/20"
                                  : ""
                              } ${previewingTheme === key ? "ring-2 ring-orange-500 shadow-lg shadow-orange-500/20" : ""}`}
                              onClick={() => setTheme(key)}
                            >
                              {/* Theme Preview Bar */}
                              <div
                                className="w-full h-3 rounded-md mb-3 border border-border/50"
                                style={{
                                  background: `linear-gradient(135deg, ${config.gradientFrom}, ${config.gradientTo})`,
                                }}
                              />

                              <div className="flex items-start justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  {getThemeIcon(key)}
                                  <h4 className="font-medium">
                                    {config.label}
                                  </h4>
                                </div>

                                <div className="flex items-center gap-1">
                                  {theme === key && (
                                    <Check className="h-4 w-4 text-primary" />
                                  )}

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handlePreview(key);
                                    }}
                                  >
                                    {previewingTheme === key ? (
                                      <EyeOff className="h-3 w-3" />
                                    ) : (
                                      <Eye className="h-3 w-3" />
                                    )}
                                  </Button>
                                </div>
                              </div>

                              <p className="text-xs text-muted-foreground mb-3">
                                {config.description}
                              </p>

                              <div className="flex items-center justify-between">
                                <Badge variant="outline" className="text-xs">
                                  {config.mode}
                                </Badge>
                                {getThemeColorPreview(config)}
                              </div>

                              {previewingTheme === key && (
                                <div className="absolute inset-0 bg-orange-500/10 rounded-lg border-2 border-orange-500/30 flex items-center justify-center">
                                  <Badge className="bg-orange-500 text-white">
                                    PREVIEWING
                                  </Badge>
                                </div>
                              )}
                            </Card>
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Wand2 className="h-5 w-5 text-primary" />
                  Custom Theme Builder
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="theme-name">Theme Name</Label>
                      <Input
                        id="theme-name"
                        value={customThemeData.name}
                        onChange={(e) =>
                          setCustomThemeData((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        placeholder="my-custom-theme"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="theme-label">Display Label</Label>
                      <Input
                        id="theme-label"
                        value={customThemeData.label}
                        onChange={(e) =>
                          setCustomThemeData((prev) => ({
                            ...prev,
                            label: e.target.value,
                          }))
                        }
                        placeholder="My Custom Theme"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="theme-description">Description</Label>
                      <Textarea
                        id="theme-description"
                        value={customThemeData.description}
                        onChange={(e) =>
                          setCustomThemeData((prev) => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                        placeholder="A beautiful custom theme"
                        rows={2}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="theme-mode">Theme Mode</Label>
                      <Select
                        value={customThemeData.mode}
                        onValueChange={(value) =>
                          setCustomThemeData((prev) => ({
                            ...prev,
                            mode: value as "light" | "dark",
                          }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="primary-color">Primary Color</Label>
                      <div className="flex gap-2">
                        <Input
                          id="primary-color"
                          type="color"
                          value={customThemeData.primaryColor}
                          onChange={(e) =>
                            setCustomThemeData((prev) => ({
                              ...prev,
                              primaryColor: e.target.value,
                              gradientFrom: e.target.value,
                            }))
                          }
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customThemeData.primaryColor}
                          onChange={(e) =>
                            setCustomThemeData((prev) => ({
                              ...prev,
                              primaryColor: e.target.value,
                              gradientFrom: e.target.value,
                            }))
                          }
                          placeholder="#3b82f6"
                          className="flex-1"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="gradient-to">Gradient End Color</Label>
                      <div className="flex gap-2">
                        <Input
                          id="gradient-to"
                          type="color"
                          value={customThemeData.gradientTo}
                          onChange={(e) =>
                            setCustomThemeData((prev) => ({
                              ...prev,
                              gradientTo: e.target.value,
                            }))
                          }
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customThemeData.gradientTo}
                          onChange={(e) =>
                            setCustomThemeData((prev) => ({
                              ...prev,
                              gradientTo: e.target.value,
                            }))
                          }
                          placeholder="#06b6d4"
                          className="flex-1"
                        />
                      </div>
                    </div>

                    {/* Preview */}
                    <div className="space-y-2">
                      <Label>Preview</Label>
                      <div
                        className="w-full h-16 rounded-lg border border-border/50 relative overflow-hidden"
                        style={{
                          background: `linear-gradient(135deg, ${customThemeData.gradientFrom}, ${customThemeData.gradientTo})`,
                        }}
                      >
                        <div className="absolute inset-0 flex items-center justify-center text-white font-medium text-sm">
                          {customThemeData.label || "Custom Theme"}
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={createCustom}
                      className="w-full"
                      disabled={!customThemeData.name || !customThemeData.label}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Theme
                    </Button>
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="manage" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Download className="h-5 w-5 text-green-500" />
                    Export Themes
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Save your current theme configuration to a file for backup
                    or sharing.
                  </p>
                  <Button onClick={handleExport} className="w-full gap-2">
                    <Download className="h-4 w-4" />
                    Export Theme Data
                  </Button>
                </Card>

                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Upload className="h-5 w-5 text-blue-500" />
                    Import Themes
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Load theme configuration from a previously exported file.
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json"
                    onChange={handleImport}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="w-full gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    Import Theme Data
                  </Button>
                </Card>

                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Copy className="h-5 w-5 text-purple-500" />
                    Current Theme Info
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        Name:
                      </span>
                      <Badge variant="outline">{currentConfig.label}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        Mode:
                      </span>
                      <Badge
                        variant={
                          currentConfig.mode === "dark"
                            ? "secondary"
                            : "default"
                        }
                      >
                        {currentConfig.mode}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        Primary:
                      </span>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded border border-border/50"
                          style={{ background: currentConfig.primaryColor }}
                        />
                        <code className="text-xs bg-muted px-1 rounded">
                          {currentConfig.primaryColor}
                        </code>
                      </div>
                    </div>
                    <Button
                      onClick={() =>
                        copyToClipboard(JSON.stringify(currentConfig, null, 2))
                      }
                      variant="outline"
                      size="sm"
                      className="w-full gap-2"
                    >
                      <Copy className="h-3 w-3" />
                      Copy Config
                    </Button>
                  </div>
                </Card>

                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Settings className="h-5 w-5 text-orange-500" />
                    Reset Options
                  </h3>
                  <div className="space-y-3">
                    <Button
                      onClick={() => setTheme("default")}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      Reset to Default
                    </Button>
                    <Button
                      onClick={() => setTheme("auto")}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      Use System Theme
                    </Button>
                  </div>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-500" />
                  Theme Preview Mode
                </h3>
                <p className="text-muted-foreground mb-6">
                  Preview how different themes look without applying them
                  permanently. Click "Preview" on any theme to see it in action.
                </p>

                {isPreviewMode ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse" />
                        <div>
                          <div className="font-medium text-orange-700 dark:text-orange-300">
                            Preview Mode Active
                          </div>
                          <div className="text-sm text-orange-600 dark:text-orange-400">
                            Previewing: {getThemeConfig(previewingTheme!).label}
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => {
                          clearPreview();
                          setPreviewingTheme(null);
                        }}
                        variant="outline"
                        size="sm"
                        className="gap-2"
                      >
                        <X className="h-4 w-4" />
                        Exit Preview
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Button
                        onClick={() => {
                          if (previewingTheme) {
                            setTheme(previewingTheme);
                            clearPreview();
                            setPreviewingTheme(null);
                          }
                        }}
                        className="gap-2"
                      >
                        <Check className="h-4 w-4" />
                        Apply This Theme
                      </Button>
                      <Button
                        onClick={() => {
                          clearPreview();
                          setPreviewingTheme(null);
                        }}
                        variant="outline"
                        className="gap-2"
                      >
                        <X className="h-4 w-4" />
                        Cancel Preview
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      No theme is currently being previewed. Go to the Themes
                      tab and click the eye icon on any theme to preview it.
                    </p>
                  </div>
                )}
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end pt-4 border-t border-border/50 flex-shrink-0 bg-background">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
