import { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, Wifi, WifiOff, Al<PERSON><PERSON>riangle, X } from "lucide-react";

interface SplashScreenProps {
  isConnected: boolean;
  docsLoaded: boolean;
  onLoadComplete: () => void;
}

const flowers = [
  { id: 1, x: 20, y: 30, color: "#ff6b9d", petals: "#ffb3d6" },
  { id: 2, x: 70, y: 60, color: "#4ecdc4", petals: "#a8e6cf" },
  { id: 3, x: 85, y: 20, color: "#ffe66d", petals: "#fff2a8" },
  { id: 4, x: 30, y: 70, color: "#ff8b94", petals: "#ffaab0" },
  { id: 5, x: 60, y: 25, color: "#a8e6cf", petals: "#d4f4dd" },
  { id: 6, x: 15, y: 80, color: "#dcedc1", petals: "#eff7e6" },
  { id: 7, x: 90, y: 45, color: "#ffd93d", petals: "#ffe66d" },
  { id: 8, x: 45, y: 85, color: "#ff6b9d", petals: "#ffb3d6" },
];

const BeeComponent = ({
  targetFlower,
  currentPosition,
}: {
  targetFlower: number;
  currentPosition: { x: number; y: number };
}) => {
  return (
    <motion.div
      className="absolute z-30"
      animate={{
        left: `${currentPosition.x}%`,
        top: `${currentPosition.y}%`,
      }}
      transition={{
        duration: 2.5,
        ease: [0.25, 0.8, 0.25, 1], // Custom cubic-bezier for more natural movement
      }}
      style={{
        transform: "translate(-50%, -50%)",
      }}
    >
      <motion.div
        className="relative w-24 h-24"
        animate={{
          rotate: [0, 3, -3, 0], // Slight rotation during flight
        }}
        transition={{
          duration: 1.2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <img
          src="/Bee%20-%20lounging.gif"
          alt="Lounging Bee"
          style={{
            width: "96px",
            height: "96px",
            objectFit: "contain",
          }}
        />

        {/* Flight trail with particles */}
        <motion.div
          className="absolute left-1/2 top-1/2 w-12 h-2 -translate-x-1/2 -translate-y-1/2"
          animate={{
            opacity: [0, 0.6, 0],
            scale: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeOut",
          }}
        >
          <div className="w-full h-full bg-gradient-to-r from-transparent via-yellow-300/40 to-transparent rounded-full" />

          {/* Pollen particles */}
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-yellow-400 rounded-full"
              style={{
                left: `${20 + i * 20}%`,
                top: "50%",
              }}
              animate={{
                y: [0, -8, 0],
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeOut",
              }}
            />
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

const FlowerComponent = ({
  flower,
  isTarget,
}: {
  flower: (typeof flowers)[0];
  isTarget: boolean;
}) => {
  return (
    <motion.div
      className="absolute"
      style={{
        left: `${flower.x}%`,
        top: `${flower.y}%`,
        transform: "translate(-50%, -50%)",
      }}
      animate={{
        scale: isTarget ? [1, 1.2, 1] : 1,
        rotate: isTarget ? [0, 5, -5, 0] : 0,
      }}
      transition={{
        duration: isTarget ? 1 : 0,
        repeat: isTarget ? Infinity : 0,
        ease: "easeInOut",
      }}
    >
      {/* Flower petals */}
      <div className="relative">
        {Array.from({ length: 6 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-5 rounded-full"
            style={{
              backgroundColor: flower.petals,
              transform: `rotate(${i * 60}deg) translateY(-8px)`,
              transformOrigin: "center bottom",
            }}
            animate={{
              scale: isTarget ? [1, 1.1, 1] : [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.1,
              ease: "easeInOut",
            }}
          />
        ))}

        {/* Flower center */}
        <motion.div
          className="absolute w-4 h-4 rounded-full left-1/2 top-1/2"
          style={{
            backgroundColor: flower.color,
            transform: "translate(-50%, -50%)",
          }}
          animate={{
            scale: isTarget ? [1, 1.3, 1] : [1, 1.1, 1],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        {/* Stem */}
        <div
          className="absolute w-1 h-8 bg-green-500 left-1/2 top-1/2"
          style={{ transform: "translate(-50%, 50%)" }}
        />

        {/* Leaves */}
        <div
          className="absolute w-2 h-3 bg-green-400 rounded-full left-1/2 top-1/2"
          style={{ transform: "translate(-50%, 100%) rotate(-30deg)" }}
        />
        <div
          className="absolute w-2 h-3 bg-green-400 rounded-full left-1/2 top-1/2"
          style={{ transform: "translate(-50%, 120%) rotate(30deg)" }}
        />
      </div>
    </motion.div>
  );
};

export function SplashScreen({
  isConnected,
  docsLoaded,
  onLoadComplete,
}: SplashScreenProps) {
  const [currentFlowerIndex, setCurrentFlowerIndex] = useState(0);
  const [beePosition, setBeePosition] = useState({
    x: flowers[0].x,
    y: flowers[0].y,
  });
  const [loadingStage, setLoadingStage] = useState("Initializing...");
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(300); // 5 minutes in seconds
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  // Connection required timeout mechanism
  useEffect(() => {
    if (!isConnected && !hasTimedOut) {
      console.log("🚨 Starting 5-minute connection timeout");

      // Start countdown timer
      countdownRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            // Time's up - kill the application
            setHasTimedOut(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // Set main timeout to kill app after 5 minutes
      timeoutRef.current = setTimeout(() => {
        if (!isConnected) {
          console.error("💀 FATAL: Connection timeout - killing application");
          setHasTimedOut(true);

          // Kill the application after a short delay to show error
          setTimeout(() => {
            document.body.innerHTML = `
              <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                background: #000;
                color: #ff4444;
                font-family: monospace;
                text-align: center;
              ">
                <h1 style="font-size: 3rem; margin-bottom: 1rem;">💀 CONNECTION TIMEOUT</h1>
                <p style="font-size: 1.2rem; margin-bottom: 2rem;">Failed to connect within 5 minutes</p>
                <p style="font-size: 1rem; opacity: 0.7;">Application terminated</p>
                <button onclick="location.reload()" style="
                  margin-top: 2rem;
                  padding: 1rem 2rem;
                  background: #ff4444;
                  color: white;
                  border: none;
                  border-radius: 8px;
                  font-size: 1rem;
                  cursor: pointer;
                ">RESTART</button>
              </div>
            `;
          }, 3000); // Show error for 3 seconds before killing
        }
      }, 300000); // 5 minutes
    } else if (isConnected && !hasTimedOut) {
      // Connection established - clear timers
      console.log("✅ Connection established - clearing timeout");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
        countdownRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, [isConnected, hasTimedOut]);

  // Update loading stage based on connection status - require connection
  useEffect(() => {
    if (hasTimedOut) {
      setLoadingStage(
        "FATAL ERROR: Connection timeout - Application will terminate",
      );
    } else if (!isConnected) {
      setLoadingStage("Connecting to service... (REQUIRED)");
    } else if (!docsLoaded) {
      setLoadingStage("Loading documents...");
    } else {
      setLoadingStage("Ready!");
      // Only proceed if we have a valid connection
      if (isConnected) {
        setTimeout(() => {
          onLoadComplete();
        }, 1000);
      }
    }
  }, [isConnected, docsLoaded, onLoadComplete, hasTimedOut]);

  // Bee flying animation with random target selection
  useEffect(() => {
    const flyToRandomFlower = () => {
      setCurrentFlowerIndex((prev) => {
        // Choose a random flower that's not the current one
        let nextIndex;
        do {
          nextIndex = Math.floor(Math.random() * flowers.length);
        } while (nextIndex === prev && flowers.length > 1);

        const targetFlower = flowers[nextIndex];

        // Add some randomness to the flight path (slight deviation)
        const deviation = 5; // 5% deviation
        const randomX = targetFlower.x + (Math.random() - 0.5) * deviation;
        const randomY = targetFlower.y + (Math.random() - 0.5) * deviation;

        setBeePosition({
          x: Math.max(5, Math.min(95, randomX)),
          y: Math.max(10, Math.min(90, randomY)),
        });
        return nextIndex;
      });
    };

    // Initial flight
    flyToRandomFlower();

    // Random intervals between 2-4 seconds
    const scheduleNextFlight = () => {
      const randomDelay = 2000 + Math.random() * 2000;
      setTimeout(() => {
        flyToRandomFlower();
        scheduleNextFlight();
      }, randomDelay);
    };

    scheduleNextFlight();
  }, []);

  const currentFlower = flowers[currentFlowerIndex];
  // Progress only advances with connection - no progression without it
  const progress = hasTimedOut ? 0 : isConnected ? (docsLoaded ? 100 : 60) : 0;

  return (
    <motion.div
      className="fixed inset-0 z-50 bg-gradient-to-br from-sky-100 via-green-50 to-yellow-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Animated background clouds */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 5 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-20 h-12 bg-white/30 rounded-full"
            style={{
              left: `${10 + i * 20}%`,
              top: `${5 + i * 10}%`,
            }}
            animate={{
              x: [0, 50, 0],
              y: [0, -10, 0],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Sun */}
      <motion.div
        className="absolute top-8 right-8 w-16 h-16 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full"
        animate={{
          rotate: [0, 360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 20, repeat: Infinity, ease: "linear" },
          scale: { duration: 4, repeat: Infinity, ease: "easeInOut" },
        }}
      >
        {/* Sun rays */}
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-6 bg-yellow-400 rounded-full"
            style={{
              left: "50%",
              top: "50%",
              transform: `translate(-50%, -50%) rotate(${i * 45}deg) translateY(-20px)`,
              transformOrigin: "center center",
            }}
            animate={{
              opacity: [0.6, 1, 0.6],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.25,
              ease: "easeInOut",
            }}
          />
        ))}
      </motion.div>

      {/* Main content container */}
      <div className="relative h-full flex flex-col">
        {/* Garden area with flowers and bee */}
        <div
          className="flex-1 relative"
          style={{
            backgroundImage:
              "url(https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2Ffeb8814bc39c4bc891959f9535cf263e)",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
            backgroundSize: "cover",
          }}
        >
          {/* Render flowers */}
          {flowers.map((flower, index) => (
            <FlowerComponent
              key={flower.id}
              flower={flower}
              isTarget={index === currentFlowerIndex}
            />
          ))}

          {/* Render bee */}
          <BeeComponent
            targetFlower={currentFlowerIndex}
            currentPosition={beePosition}
          />

          {/* Grass */}
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-green-300 to-green-200">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-0.5 h-6 bg-green-400"
                style={{
                  left: `${i * 5}%`,
                  bottom: 0,
                }}
                animate={{
                  height: [24, 28, 24],
                  rotate: [0, 2, -2, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        </div>

        {/* New positioned image */}
        <img
          loading="lazy"
          srcSet="https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=100 100w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=200 200w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=400 400w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=800 800w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=1200 1200w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=1600 1600w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49?width=2000 2000w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F3b0936d2c2d64da78e25fc6d05b25b49"
          className="absolute bottom-0 left-0 w-[50px] max-w-[50px] min-w-[20px] aspect-square object-contain overflow-hidden"
          style={{ zIndex: 9999999, float: "left", flexGrow: 0 }}
          alt="Corner Image"
        />

        {/* Loading UI */}
        <div
          className="bg-white/90 backdrop-blur-sm border-t border-white/50"
          style={{
            backgroundImage:
              "url(https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F96753f57181a4e34809084ce799e5374)",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
            backgroundSize: "cover",
          }}
        >
          <div
            className="max-w-md text-center flex flex-col justify-center items-center"
            style={{
              backgroundImage:
                "url(https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F7054c4a82690471a82386da89d869517)",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
              backgroundSize: "cover",
              margin: "141px auto 0",
              padding: "0 32px",
            }}
          >
            <img
              loading="lazy"
              srcSet="https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=100 100w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=200 200w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=400 400w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=800 800w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=1200 1200w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=1600 1600w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c?width=2000 2000w, https://cdn.builder.io/api/v1/image/assets%2F2492e7fff4254faabe13931e320cbcff%2F698caf61b1734762aa0acdff8727f49c"
              className="w-full min-h-5 min-w-5 aspect-square object-cover object-center overflow-hidden"
              style={{ marginTop: "-361px", paddingBottom: "4px" }}
              alt="W8 File Importer Logo"
            />
            {/* App Logo and Title */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6 }}
              style={{ margin: "1px 0 24px 1px" }}
            >
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                W8 File Importer
              </h1>
              <p className="text-gray-600">Professional Desktop Tool</p>
            </motion.div>

            {/* Connection Status */}
            <motion.div
              className="flex items-center justify-center gap-2 mb-4"
              animate={{ opacity: hasTimedOut ? 1 : [0.7, 1, 0.7] }}
              transition={{
                duration: hasTimedOut ? 0 : 1.5,
                repeat: hasTimedOut ? 0 : Infinity,
              }}
            >
              {hasTimedOut ? (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              ) : isConnected ? (
                <Wifi className="h-5 w-5 text-green-500" />
              ) : (
                <WifiOff className="h-5 w-5 text-red-500" />
              )}
              <span
                className={`text-sm font-medium ${
                  hasTimedOut
                    ? "text-red-600"
                    : isConnected
                      ? "text-green-600"
                      : "text-red-600"
                }`}
              >
                {hasTimedOut
                  ? "FATAL: Connection Failed"
                  : isConnected
                    ? "Connected ✓"
                    : "Connecting... (REQUIRED)"}
              </span>
            </motion.div>

            {/* Timeout Countdown */}
            {!isConnected && !hasTimedOut && (
              <motion.div
                className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-center gap-2 text-yellow-700">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">
                    Timeout in {Math.floor(timeRemaining / 60)}:
                    {(timeRemaining % 60).toString().padStart(2, "0")}
                  </span>
                </div>
                <p className="text-xs text-yellow-600 mt-1">
                  CONNECTION REQUIRED - Application will terminate if not
                  established
                </p>
              </motion.div>
            )}

            {/* Error Message for Timeout */}
            {hasTimedOut && (
              <motion.div
                className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-center gap-2 text-red-700 mb-2">
                  <X className="h-5 w-5" />
                  <span className="font-semibold">Connection Timeout</span>
                </div>
                <p className="text-sm text-red-600 mb-2">
                  FATAL ERROR: Failed to establish required connection within 5
                  minutes.
                </p>
                <p className="text-xs text-red-500">
                  Application terminating automatically...
                </p>
              </motion.div>
            )}

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4 overflow-hidden">
              <motion.div
                className={`h-full ${
                  hasTimedOut
                    ? "bg-gradient-to-r from-red-500 to-red-600"
                    : "bg-gradient-to-r from-blue-500 to-green-500"
                }`}
                initial={{ width: "0%" }}
                animate={{ width: hasTimedOut ? "100%" : `${progress}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>

            {/* Loading Text */}
            <motion.p
              className="text-gray-600 mb-4"
              key={loadingStage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {loadingStage}
            </motion.p>

            {/* Animated Loading Dots */}
            {!hasTimedOut && (
              <div className="flex justify-center gap-1">
                {Array.from({ length: 3 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-blue-500 rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 0.8,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut",
                    }}
                  />
                ))}
              </div>
            )}

            {/* Error Icon for Timeout */}
            {hasTimedOut && (
              <motion.div
                className="flex justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
              >
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <X className="h-5 w-5 text-white" />
                </div>
              </motion.div>
            )}

            {/* Progress Text */}
            <motion.div
              className={`mt-4 text-xs ${hasTimedOut ? "text-red-500" : "text-gray-500"}`}
              animate={{ opacity: hasTimedOut ? 1 : [0.5, 1, 0.5] }}
              transition={{
                duration: hasTimedOut ? 0 : 2,
                repeat: hasTimedOut ? 0 : Infinity,
              }}
            >
              {hasTimedOut && "FATAL ERROR - Application terminating..."}
              {!hasTimedOut &&
                !isConnected &&
                "Establishing required connection..."}
              {!hasTimedOut &&
                isConnected &&
                !docsLoaded &&
                "Synchronizing data..."}
              {!hasTimedOut &&
                isConnected &&
                docsLoaded &&
                "Initialization complete!"}
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
