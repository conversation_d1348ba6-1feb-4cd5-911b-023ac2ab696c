import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-overline font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover-lift",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-gradient-primary text-primary-foreground hover:shadow-glow status-primary",
        secondary:
          "border-transparent bg-gradient-secondary text-secondary-foreground hover:shadow-md",
        destructive:
          "border-transparent bg-gradient-destructive text-destructive-foreground hover:shadow-glow status-destructive",
        success:
          "border-transparent bg-gradient-success text-success-foreground hover:shadow-glow status-success",
        warning:
          "border-transparent bg-gradient-warning text-warning-foreground hover:shadow-glow status-warning",
        outline: "text-foreground border-border hover:bg-muted",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
