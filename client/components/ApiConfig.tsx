import { useState, useEffect } from "react";
import { Settings, Key, Save, TestTube, Building, Users } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { fileUploadService } from "@/services/fileUploadService";

export function ApiConfig() {
  const [open, setOpen] = useState(false);
  const [tenantId, setTenantId] = useState("");
  const [providerId, setProviderId] = useState("");
  const [isConfigured, setIsConfigured] = useState(false);
  const [testStatus, setTestStatus] = useState<
    "idle" | "testing" | "success" | "error"
  >("idle");
  const [testMessage, setTestMessage] = useState("");

  useEffect(() => {
    // Load saved configuration from localStorage
    const savedTenantId = localStorage.getItem("w8file_tenant_id");
    const savedProviderId = localStorage.getItem("w8file_provider_id");

    if (savedTenantId) {
      setTenantId(savedTenantId);
    }

    if (savedProviderId) {
      setProviderId(savedProviderId);
    }

    // Check if all required fields are configured
    if (savedTenantId && savedProviderId) {
      setIsConfigured(true);
    }
  }, []);

  const handleSave = () => {
    if (tenantId.trim() && providerId.trim()) {
      localStorage.setItem("w8file_tenant_id", tenantId.trim());
      localStorage.setItem("w8file_provider_id", providerId.trim());
      setIsConfigured(true);
      setOpen(false);
    } else {
      alert("Please fill in all required fields: Tenant ID and Provider ID");
    }
  };

  const handleTest = async () => {
    if (!tenantId.trim() || !providerId.trim()) {
      setTestStatus("error");
      setTestMessage("Please fill in all required fields before testing");
      return;
    }

    setTestStatus("testing");
    setTestMessage("Validating configuration...");

    try {
      // Simulate configuration validation
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setTestStatus("success");
      setTestMessage("Configuration validated successfully!");
    } catch (error) {
      setTestStatus("error");
      setTestMessage(
        error instanceof Error
          ? error.message
          : "Configuration validation failed",
      );
    }
  };

  const handleClear = () => {
    localStorage.removeItem("w8file_tenant_id");
    localStorage.removeItem("w8file_provider_id");
    setTenantId("");
    setProviderId("");
    setIsConfigured(false);
    setTestStatus("idle");
    setTestMessage("");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Settings className="h-4 w-4" />
          Settings
          {isConfigured && (
            <Badge variant="secondary" className="ml-1">
              ✓
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            W8 File Importer Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* API Configuration Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              <h3 className="font-medium">Organization Configuration</h3>
            </div>

            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="tenantId">
                  Tenant ID <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="tenantId"
                  value={tenantId}
                  onChange={(e) => setTenantId(e.target.value)}
                  placeholder="Enter your tenant ID"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="providerId">
                  Provider ID <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="providerId"
                  value={providerId}
                  onChange={(e) => setProviderId(e.target.value)}
                  placeholder="Enter your provider ID"
                />
              </div>
            </div>
          </div>

          <Separator />

          {testStatus !== "idle" && (
            <Alert variant={testStatus === "error" ? "destructive" : "default"}>
              <AlertDescription>{testMessage}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between gap-2 pt-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleTest}
                disabled={
                  !tenantId.trim() ||
                  !providerId.trim() ||
                  testStatus === "testing"
                }
                className="gap-2"
              >
                <TestTube className="h-4 w-4" />
                {testStatus === "testing" ? "Validating..." : "Validate"}
              </Button>
              {isConfigured && (
                <Button variant="destructive" size="sm" onClick={handleClear}>
                  Clear
                </Button>
              )}
            </div>

            <Button
              onClick={handleSave}
              disabled={!tenantId.trim() || !providerId.trim()}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              Save
            </Button>
          </div>
        </div>

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Setup Instructions:</h4>
          <ol className="text-sm space-y-1 list-decimal list-inside text-muted-foreground">
            <li>Get your organization credentials from your administrator</li>
            <li>Enter your Tenant ID and Provider ID</li>
            <li>Click "Validate" to verify the configuration</li>
            <li>Click "Save" to store the settings</li>
          </ol>
          <p className="text-xs text-muted-foreground mt-2">
            <span className="text-red-500">*</span> Required fields
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
