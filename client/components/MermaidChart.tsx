import { useEffect, useRef } from "react";
import mermaid from "mermaid";

interface MermaidChartProps {
  chart: string;
  id?: string;
  className?: string;
}

export function MermaidChart({
  chart,
  id,
  className = "w-full",
}: MermaidChartProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const chartId = id || `mermaid-${Math.random().toString(36).substr(2, 9)}`;

  useEffect(() => {
    mermaid.initialize({
      startOnLoad: false,
      theme: "default",
      themeVariables: {
        primaryColor: "#3b82f6",
        primaryTextColor: "#1f2937",
        primaryBorderColor: "#2563eb",
        lineColor: "#6b7280",
        sectionBkgColor: "#f8fafc",
        altSectionBkgColor: "#f1f5f9",
        gridColor: "#e5e7eb",
        secondaryColor: "#f1f5f9",
        tertiaryColor: "#f8fafc",
        background: "#ffffff",
        mainBkg: "#ffffff",
        secondBkg: "#f8fafc",
        tertiaryBkg: "#f1f5f9",
      },
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: "basis",
      },
      sequence: {
        useMaxWidth: true,
        showSequenceNumbers: true,
        wrap: true,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35,
      },
      gantt: {
        useMaxWidth: true,
        leftPadding: 75,
        gridLineStartPadding: 35,
        fontSize: 11,
        sectionFontSize: 11,
        numberSectionStyles: 4,
      },
      journey: {
        useMaxWidth: true,
      },
      gitgraph: {
        useMaxWidth: true,
      },
    });

    if (elementRef.current) {
      elementRef.current.innerHTML = chart;
      mermaid
        .run({
          nodes: [elementRef.current],
        })
        .catch((error) => {
          console.error("Mermaid rendering error:", error);
          if (elementRef.current) {
            elementRef.current.innerHTML = `<div class="text-red-500 p-4 border border-red-200 rounded">
            <p class="font-semibold">Chart Rendering Error</p>
            <p class="text-sm">Failed to render Mermaid chart. Please check the chart syntax.</p>
          </div>`;
          }
        });
    }
  }, [chart, chartId]);

  return (
    <div
      ref={elementRef}
      className={`mermaid-chart ${className}`}
      style={{ minHeight: "200px" }}
    />
  );
}
