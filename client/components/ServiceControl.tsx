import { useState } from "react";
import {
  Power,
  Play,
  Pause,
  RotateCcw,
  Square,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Server,
  Wifi,
  WifiOff,
  Shield,
  Cpu,
  HardDrive,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from "framer-motion";

interface SystemMetrics {
  cpu: number;
  memory: number;
  storage: number;
  uptime: string;
}

interface ServiceControlProps {
  isConnected: boolean;
  serviceStatus:
    | "running"
    | "stopped"
    | "paused"
    | "starting"
    | "stopping"
    | "error";
  systemMetrics: SystemMetrics;
  onSendCommand: (
    command: "start" | "stop" | "pause" | "resume" | "restart" | "shutdown",
  ) => void;
}

export function ServiceControl({
  isConnected,
  serviceStatus,
  systemMetrics,
  onSendCommand,
}: ServiceControlProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleCommand = async (
    command: "start" | "stop" | "pause" | "resume" | "restart" | "shutdown",
  ) => {
    setIsLoading(command);
    onSendCommand(command);

    // Clear loading state after a delay
    setTimeout(() => setIsLoading(null), 2000);
  };

  const getStatusInfo = () => {
    switch (serviceStatus) {
      case "running":
        return {
          icon: CheckCircle,
          color: "text-green-500",
          bgColor: "bg-gradient-to-br from-green-500/20 to-emerald-500/10",
          borderColor: "border-green-500/20",
          label: "Running",
          description: "All systems operational",
          pulseColor: "bg-green-500",
        };
      case "stopped":
        return {
          icon: Square,
          color: "text-red-500",
          bgColor: "bg-gradient-to-br from-red-500/20 to-rose-500/10",
          borderColor: "border-red-500/20",
          label: "Stopped",
          description: "Service is not running",
          pulseColor: "bg-red-500",
        };
      case "paused":
        return {
          icon: Pause,
          color: "text-yellow-500",
          bgColor: "bg-gradient-to-br from-yellow-500/20 to-amber-500/10",
          borderColor: "border-yellow-500/20",
          label: "Paused",
          description: "Service temporarily paused",
          pulseColor: "bg-yellow-500",
        };
      case "starting":
        return {
          icon: Clock,
          color: "text-blue-500",
          bgColor: "bg-gradient-to-br from-blue-500/20 to-cyan-500/10",
          borderColor: "border-blue-500/20",
          label: "Starting",
          description: "Service is initializing",
          pulseColor: "bg-blue-500",
        };
      case "stopping":
        return {
          icon: Clock,
          color: "text-orange-500",
          bgColor: "bg-gradient-to-br from-orange-500/20 to-amber-500/10",
          borderColor: "border-orange-500/20",
          label: "Stopping",
          description: "Service is shutting down",
          pulseColor: "bg-orange-500",
        };
      case "error":
        return {
          icon: AlertCircle,
          color: "text-red-500",
          bgColor: "bg-gradient-to-br from-red-500/20 to-rose-500/10",
          borderColor: "border-red-500/20",
          label: "Error",
          description: "Service encountered an error",
          pulseColor: "bg-red-500",
        };
      default:
        return {
          icon: Activity,
          color: "text-muted-foreground",
          bgColor: "bg-muted/10",
          borderColor: "border-muted/20",
          label: "Unknown",
          description: "Status unknown",
          pulseColor: "bg-muted",
        };
    }
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  const controlButtons = [
    {
      command: "start" as const,
      icon: Play,
      label: "Start",
      variant: "default" as const,
      disabled: serviceStatus === "running" || serviceStatus === "starting",
      color: "from-green-500 to-emerald-500",
    },
    {
      command: "pause" as const,
      icon: Pause,
      label: "Pause",
      variant: "secondary" as const,
      disabled: serviceStatus !== "running",
      color: "from-yellow-500 to-amber-500",
    },
    {
      command: "resume" as const,
      icon: Play,
      label: "Resume",
      variant: "default" as const,
      disabled: serviceStatus !== "paused",
      color: "from-blue-500 to-cyan-500",
    },
    {
      command: "restart" as const,
      icon: RotateCcw,
      label: "Restart",
      variant: "outline" as const,
      disabled: serviceStatus === "stopped" || serviceStatus === "stopping",
      color: "from-purple-500 to-violet-500",
    },
    {
      command: "stop" as const,
      icon: Square,
      label: "Stop",
      variant: "destructive" as const,
      disabled: serviceStatus === "stopped" || serviceStatus === "stopping",
      color: "from-red-500 to-rose-500",
    },
    {
      command: "shutdown" as const,
      icon: Power,
      label: "Shutdown",
      variant: "destructive" as const,
      disabled: false,
      color: "from-gray-500 to-slate-500",
    },
  ];

  return (
    <div className="panel p-8 relative overflow-hidden group">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/2 via-transparent to-secondary/2" />
      <motion.div
        className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-primary/10 to-transparent rounded-full"
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
        style={{ transform: "translate(50%, -50%)" }}
      />

      <div className="relative z-10">
        {/* Header */}
        <motion.div
          className="flex items-start justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center gap-6">
            <motion.div
              className={`p-4 rounded-2xl ${statusInfo.bgColor} border ${statusInfo.borderColor} relative overflow-hidden backdrop-blur-sm`}
              whileHover={{ scale: 1.05, rotate: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <StatusIcon className={`h-8 w-8 ${statusInfo.color}`} />
              {serviceStatus === "running" && (
                <motion.div
                  className="absolute inset-0 bg-green-500/10 rounded-2xl"
                  animate={{ opacity: [0, 0.5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              )}
              {(serviceStatus === "starting" ||
                serviceStatus === "stopping") && (
                <motion.div
                  className="absolute inset-0 border-2 border-primary/30 rounded-2xl"
                  animate={{ scale: [1, 1.1, 1], opacity: [1, 0, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                />
              )}
            </motion.div>

            <div>
              <h3 className="text-headline text-gradient mb-2 flex items-center gap-3">
                <span className="font-display font-bold tracking-wider">
                  Service Control
                </span>
                <motion.div
                  animate={{ rotate: serviceStatus === "running" ? 360 : 0 }}
                  transition={{
                    duration: 2,
                    repeat: serviceStatus === "running" ? Infinity : 0,
                    ease: "linear",
                  }}
                >
                  <Server className="h-6 w-6 text-primary/50" />
                </motion.div>
              </h3>
              <p className="text-body-large text-muted-foreground">
                {statusInfo.description}
              </p>
              <div className="flex items-center gap-4 mt-2">
                <Badge
                  className={`${statusInfo.bgColor} ${statusInfo.color} border-0 font-medium`}
                >
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusInfo.label}
                </Badge>
                <div className="text-caption text-muted-foreground">
                  Uptime: {systemMetrics.uptime}
                </div>
              </div>
            </div>
          </div>

          {/* Connection Status */}
          <motion.div
            className="flex items-center gap-3 bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl p-3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <motion.div
              className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
              animate={{
                scale: isConnected ? [1, 1.2, 1] : 1,
                opacity: isConnected ? [1, 0.7, 1] : 0.5,
              }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-caption font-medium">
              {isConnected ? "Connected" : "Disconnected"}
            </span>
          </motion.div>
        </motion.div>

        {/* System Metrics */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/5 p-4 rounded-xl border border-blue-500/20">
            <div className="flex items-center gap-3 mb-2">
              <Cpu className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-blue-600">
                CPU Usage
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Progress value={systemMetrics.cpu} className="flex-1 h-2" />
              <span className="text-sm font-bold text-blue-600">
                {systemMetrics.cpu}%
              </span>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/5 p-4 rounded-xl border border-green-500/20">
            <div className="flex items-center gap-3 mb-2">
              <Activity className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-green-600">Memory</span>
            </div>
            <div className="flex items-center gap-2">
              <Progress value={systemMetrics.memory} className="flex-1 h-2" />
              <span className="text-sm font-bold text-green-600">
                {systemMetrics.memory}%
              </span>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500/10 to-violet-500/5 p-4 rounded-xl border border-purple-500/20">
            <div className="flex items-center gap-3 mb-2">
              <HardDrive className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium text-purple-600">
                Storage
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Progress value={systemMetrics.storage} className="flex-1 h-2" />
              <span className="text-sm font-bold text-purple-600">
                {systemMetrics.storage}%
              </span>
            </div>
          </div>
        </motion.div>

        {/* Control Buttons Grid */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          {controlButtons.map((button, index) => {
            const ButtonIcon = button.icon;
            const isCurrentlyLoading = isLoading === button.command;

            return (
              <motion.div
                key={button.command}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.1 * index }}
                whileHover={{ scale: button.disabled ? 1 : 1.05 }}
                whileTap={{ scale: button.disabled ? 1 : 0.95 }}
              >
                <Button
                  variant={button.variant}
                  size="lg"
                  onClick={() => handleCommand(button.command)}
                  disabled={
                    !isConnected || button.disabled || isLoading !== null
                  }
                  className={`w-full h-16 btn-modern relative overflow-hidden group ${
                    !button.disabled && isConnected
                      ? `hover:bg-gradient-to-r hover:${button.color} hover:text-white hover:shadow-lg transition-all duration-300`
                      : ""
                  }`}
                >
                  <div className="flex flex-col items-center gap-1">
                    {isCurrentlyLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "linear",
                        }}
                      >
                        <RotateCcw className="w-5 h-5" />
                      </motion.div>
                    ) : (
                      <ButtonIcon className="w-5 h-5" />
                    )}
                    <span className="text-sm font-medium">{button.label}</span>
                  </div>

                  {/* Button shine effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full"
                    animate={{ x: ["0%", "200%"] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                    style={{
                      display:
                        !button.disabled && isConnected ? "block" : "none",
                    }}
                  />
                </Button>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Service Information */}
        <motion.div
          className="pt-6 border-t border-border/50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-lg text-gradient flex items-center gap-2">
                <Shield className="h-5 w-5 text-primary" />
                System Status
              </h4>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <motion.div
                    className={`w-2 h-2 rounded-full ${statusInfo.pulseColor}`}
                    animate={{
                      scale: serviceStatus === "running" ? [1, 1.3, 1] : 1,
                      opacity: serviceStatus === "running" ? [1, 0.7, 1] : 0.7,
                    }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  />
                  <span className="text-sm text-muted-foreground">
                    Service Status:
                  </span>
                  <span className={`font-medium ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <motion.div
                    className={`w-2 h-2 rounded-full ${isConnected ? "bg-primary" : "bg-red-500"}`}
                    animate={{
                      scale: isConnected ? [1, 1.3, 1] : 1,
                      opacity: isConnected ? [1, 0.7, 1] : 0.7,
                    }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  />
                  <span className="text-sm text-muted-foreground">
                    Connection:
                  </span>
                  <span
                    className={`font-medium ${isConnected ? "text-primary" : "text-red-500"}`}
                  >
                    {isConnected ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-lg text-gradient flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                Performance
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Active Tasks:</span>
                  <span className="font-medium">0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Usage:</span>
                  <span className="font-medium">{systemMetrics.memory}%</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
