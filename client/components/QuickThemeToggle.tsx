import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEnhancedTheme } from "@/components/EnhancedThemeProvider";
import { motion, AnimatePresence } from "framer-motion";

export function QuickThemeToggle() {
  const { theme, actualTheme, themeMode, toggleTheme, setTheme } =
    useEnhancedTheme();

  const getIcon = () => {
    if (theme === "auto") {
      return <Monitor className="h-4 w-4" />;
    }
    return themeMode === "dark" ? (
      <Moon className="h-4 w-4" />
    ) : (
      <Sun className="h-4 w-4" />
    );
  };

  const getNextMode = () => {
    if (theme === "auto") return "light";
    return themeMode === "dark" ? "light" : "auto";
  };

  const handleToggle = () => {
    const nextMode = getNextMode();
    if (nextMode === "auto") {
      setTheme("auto");
    } else if (nextMode === "light") {
      setTheme("default");
    } else {
      setTheme("midnight");
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggle}
      className="gap-2 hover:bg-primary/10 transition-all duration-200 group"
      title={`Current: ${theme === "auto" ? "Auto" : themeMode === "dark" ? "Dark" : "Light"} - Click to toggle`}
    >
      <motion.div
        key={theme + themeMode}
        initial={{ rotate: -90, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        exit={{ rotate: 90, opacity: 0 }}
        transition={{ type: "spring", stiffness: 200, damping: 20 }}
        className="group-hover:scale-110 transition-transform duration-200"
      >
        {getIcon()}
      </motion.div>
      <span className="hidden sm:inline text-sm">
        {theme === "auto" ? "Auto" : themeMode === "dark" ? "Dark" : "Light"}
      </span>
    </Button>
  );
}
