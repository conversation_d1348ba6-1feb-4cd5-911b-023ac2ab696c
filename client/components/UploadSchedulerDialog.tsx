import { useState, useRef } from "react"
import { Calendar, Clock, Upload, X, File, FileText, Image, Video, Plus, Settings } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { uploadSchedulerService } from "@/services/uploadSchedulerService"
import { cn } from "@/lib/utils"

interface UploadSchedulerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const getFileIcon = (file: File) => {
  const type = file.type
  if (type.startsWith("image/")) return <Image className="h-4 w-4 text-purple-500" />
  if (type.startsWith("video/")) return <Video className="h-4 w-4 text-red-500" />
  if (type.includes("text") || type.includes("document")) return <FileText className="h-4 w-4 text-blue-500" />
  return <File className="h-4 w-4 text-gray-500" />
}

export function UploadSchedulerDialog({ open, onOpenChange }: UploadSchedulerDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [scheduledDate, setScheduledDate] = useState("")
  const [scheduledTime, setScheduledTime] = useState("")
  const [description, setDescription] = useState("")
  const [isDragOver, setIsDragOver] = useState(false)
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return
    setSelectedFiles(Array.from(files))
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleSchedule = () => {
    if (selectedFiles.length === 0 || !scheduledDate || !scheduledTime) {
      alert("Please select files and set a schedule time")
      return
    }

    const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`)
    const now = new Date()

    if (scheduledDateTime <= now) {
      alert("Scheduled time must be in the future")
      return
    }

    const uploadId = uploadSchedulerService.scheduleUpload(
      selectedFiles,
      scheduledDateTime,
      {
        description: description.trim() || undefined
      }
    )

    alert(`Upload scheduled successfully! ID: ${uploadId}`)
    handleClose()
  }

  const handleClose = () => {
    setSelectedFiles([])
    setScheduledDate("")
    setScheduledTime("")
    setDescription("")
    setIsDragOver(false)
    onOpenChange(false)
  }

  const enableNotifications = async () => {
    const granted = await uploadSchedulerService.requestNotificationPermission()
    setNotificationsEnabled(granted)
    if (!granted) {
      alert("Notifications permission denied. You won't receive upload completion notifications.")
    }
  }

  // Set default date/time to 1 hour from now
  const setDefaultSchedule = () => {
    const now = new Date()
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000)
    
    setScheduledDate(oneHourLater.toISOString().split('T')[0])
    setScheduledTime(oneHourLater.toTimeString().slice(0, 5))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Schedule Upload
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Selection */}
          <div className="space-y-4">
            <Label>Select Files</Label>
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                "hover:border-primary hover:bg-primary/5"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <Upload className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
              <div className="space-y-2">
                <p className="text-sm font-medium">
                  Drop files here or{" "}
                  <Button 
                    variant="link" 
                    className="p-0 h-auto"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    browse
                  </Button>
                </p>
                <p className="text-xs text-muted-foreground">
                  Select files to upload at scheduled time
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={(e) => handleFileSelect(e.target.files)}
              />
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Files ({selectedFiles.length})</Label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 border rounded-lg">
                      {getFileIcon(file)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Schedule Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Schedule Settings</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={setDefaultSchedule}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Set Default (+1h)
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={scheduledTime}
                  onChange={(e) => setScheduledTime(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Add notes about this scheduled upload..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          {/* Notifications */}
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Notifications</h4>
                <p className="text-sm text-muted-foreground">
                  Get notified when uploads complete
                </p>
              </div>
              <Button
                variant={notificationsEnabled ? "default" : "outline"}
                size="sm"
                onClick={enableNotifications}
                className="gap-2"
              >
                <Settings className="h-4 w-4" />
                {notificationsEnabled ? "Enabled" : "Enable"}
              </Button>
            </div>
          </Card>

          {/* Schedule Preview */}
          {scheduledDate && scheduledTime && (
            <Alert>
              <Calendar className="h-4 w-4" />
              <AlertDescription>
                Files will be uploaded on{" "}
                <strong>
                  {new Date(`${scheduledDate}T${scheduledTime}`).toLocaleString()}
                </strong>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSchedule}
            disabled={selectedFiles.length === 0 || !scheduledDate || !scheduledTime}
            className="gap-2"
          >
            <Clock className="h-4 w-4" />
            Schedule Upload
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
