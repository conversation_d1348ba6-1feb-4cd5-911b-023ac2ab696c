import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/**/*.{ts,tsx}"],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      fontFamily: {
        sans: "var(--font-sans)",
        mono: "var(--font-mono)",
        display: "var(--font-display)",
        serif: "var(--font-serif)",
      },
      fontSize: {
        xs: "var(--text-xs)",
        sm: "var(--text-sm)",
        base: "var(--text-base)",
        lg: "var(--text-lg)",
        xl: "var(--text-xl)",
        "2xl": "var(--text-2xl)",
        "3xl": "var(--text-3xl)",
        "4xl": "var(--text-4xl)",
        "5xl": "var(--text-5xl)",
        "6xl": "var(--text-6xl)",
        "display-large": [
          "var(--text-6xl)",
          {
            lineHeight: "var(--leading-none)",
            letterSpacing: "var(--tracking-tighter)",
            fontWeight: "var(--font-extrabold)",
          },
        ],
        display: [
          "var(--text-5xl)",
          {
            lineHeight: "var(--leading-tight)",
            letterSpacing: "var(--tracking-tight)",
            fontWeight: "var(--font-bold)",
          },
        ],
        "headline-large": [
          "var(--text-4xl)",
          {
            lineHeight: "var(--leading-tight)",
            letterSpacing: "var(--tracking-tight)",
            fontWeight: "var(--font-bold)",
          },
        ],
        headline: [
          "var(--text-3xl)",
          {
            lineHeight: "var(--leading-tight)",
            letterSpacing: "var(--tracking-tight)",
            fontWeight: "var(--font-semibold)",
          },
        ],
        "title-large": [
          "var(--text-2xl)",
          {
            lineHeight: "var(--leading-snug)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-semibold)",
          },
        ],
        title: [
          "var(--text-xl)",
          {
            lineHeight: "var(--leading-snug)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-medium)",
          },
        ],
        "label-large": [
          "var(--text-lg)",
          {
            lineHeight: "var(--leading-normal)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-medium)",
          },
        ],
        label: [
          "var(--text-base)",
          {
            lineHeight: "var(--leading-normal)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-normal)",
          },
        ],
        "body-large": [
          "var(--text-lg)",
          {
            lineHeight: "var(--leading-relaxed)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-normal)",
          },
        ],
        body: [
          "var(--text-base)",
          {
            lineHeight: "var(--leading-normal)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-normal)",
          },
        ],
        caption: [
          "var(--text-sm)",
          {
            lineHeight: "var(--leading-normal)",
            letterSpacing: "var(--tracking-wide)",
            fontWeight: "var(--font-normal)",
          },
        ],
        overline: [
          "var(--text-xs)",
          {
            lineHeight: "var(--leading-normal)",
            letterSpacing: "var(--tracking-widest)",
            fontWeight: "var(--font-medium)",
            textTransform: "uppercase",
          },
        ],
        code: [
          "var(--text-sm)",
          {
            lineHeight: "var(--leading-relaxed)",
            letterSpacing: "var(--tracking-normal)",
            fontWeight: "var(--font-normal)",
          },
        ],
      },
      lineHeight: {
        none: "var(--leading-none)",
        tight: "var(--leading-tight)",
        snug: "var(--leading-snug)",
        normal: "var(--leading-normal)",
        relaxed: "var(--leading-relaxed)",
        loose: "var(--leading-loose)",
      },
      letterSpacing: {
        tighter: "var(--tracking-tighter)",
        tight: "var(--tracking-tight)",
        normal: "var(--tracking-normal)",
        wide: "var(--tracking-wide)",
        wider: "var(--tracking-wider)",
        widest: "var(--tracking-widest)",
      },
      fontWeight: {
        thin: "var(--font-thin)",
        light: "var(--font-light)",
        normal: "var(--font-normal)",
        medium: "var(--font-medium)",
        semibold: "var(--font-semibold)",
        bold: "var(--font-bold)",
        extrabold: "var(--font-extrabold)",
        black: "var(--font-black)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        xl: "var(--radius-xl)",
        "2xl": "var(--radius-2xl)",
      },
      boxShadow: {
        sm: "var(--shadow-sm)",
        DEFAULT: "var(--shadow)",
        md: "var(--shadow-md)",
        lg: "var(--shadow-lg)",
        xl: "var(--shadow-xl)",
        "2xl": "var(--shadow-2xl)",
        colored: "var(--shadow-colored)",
        glow: "var(--shadow-glow)",
      },
      transitionTimingFunction: {
        spring: "var(--spring)",
      },
      transitionDuration: {
        fast: "var(--transition-fast)",
        base: "var(--transition-base)",
        slow: "var(--transition-slow)",
      },
      backgroundImage: {
        "gradient-primary": "var(--primary-gradient)",
        "gradient-secondary": "var(--secondary-gradient)",
        "gradient-success": "var(--success-gradient)",
        "gradient-warning": "var(--warning-gradient)",
        "gradient-destructive": "var(--destructive-gradient)",
        "gradient-card": "var(--card-gradient)",
        "gradient-panel": "var(--panel-gradient)",
        "gradient-sidebar": "var(--sidebar-background)",
        "gradient-input": "var(--input-gradient)",
        "gradient-accent": "var(--accent-gradient)",
        "gradient-border": "var(--border-gradient)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        "progress-shine": {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(200%)" },
        },
        "glow-pulse": {
          "0%, 100%": { boxShadow: "var(--shadow-glow)" },
          "50%": { boxShadow: "0 0 40px hsla(var(--primary), 0.6)" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-3px)" },
        },
        "gradient-shift": {
          "0%, 100%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
        },
        "pulse-ring": {
          "0%": { transform: "scale(0.33)" },
          "40%, 50%": { opacity: "1" },
          "100%": { opacity: "0", transform: "scale(1.33)" },
        },
        "slide-in-bottom": {
          "0%": { transform: "translateY(20px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        "fade-in": {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        "theme-pulse": {
          "0%, 100%": { opacity: "1", transform: "scale(1)" },
          "50%": { opacity: "0.7", transform: "scale(0.95)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        shimmer: "shimmer 2s infinite",
        "progress-shine": "progress-shine 2s infinite",
        "glow-pulse": "glow-pulse 2s ease-in-out infinite",
        float: "float 3s ease-in-out infinite",
        "gradient-shift": "gradient-shift 3s ease infinite",
        "pulse-ring": "pulse-ring 1.5s infinite",
        "slide-in": "slide-in-bottom 0.5s ease-out",
        "fade-in": "fade-in 0.3s ease-out",
        "theme-pulse": "theme-pulse 2s ease-in-out infinite",
      },
      backdropBlur: {
        xs: "2px",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    // Custom plugin for semantic typography classes
    function ({ addUtilities, theme }: any) {
      const newUtilities = {
        ".text-display-large": {
          fontFamily: "var(--font-display)",
          fontSize: "var(--text-6xl)",
          lineHeight: "var(--leading-none)",
          letterSpacing: "var(--tracking-tighter)",
          fontWeight: "var(--font-extrabold)",
        },
        ".text-display": {
          fontFamily: "var(--font-display)",
          fontSize: "var(--text-5xl)",
          lineHeight: "var(--leading-tight)",
          letterSpacing: "var(--tracking-tight)",
          fontWeight: "var(--font-bold)",
        },
        ".text-headline-large": {
          fontFamily: "var(--font-display)",
          fontSize: "var(--text-4xl)",
          lineHeight: "var(--leading-tight)",
          letterSpacing: "var(--tracking-tight)",
          fontWeight: "var(--font-bold)",
        },
        ".text-headline": {
          fontFamily: "var(--font-display)",
          fontSize: "var(--text-3xl)",
          lineHeight: "var(--leading-tight)",
          letterSpacing: "var(--tracking-tight)",
          fontWeight: "var(--font-semibold)",
        },
        ".text-title-large": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-2xl)",
          lineHeight: "var(--leading-snug)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-semibold)",
        },
        ".text-title": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-xl)",
          lineHeight: "var(--leading-snug)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-medium)",
        },
        ".text-label-large": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-lg)",
          lineHeight: "var(--leading-normal)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-medium)",
        },
        ".text-label": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-base)",
          lineHeight: "var(--leading-normal)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-normal)",
        },
        ".text-body-large": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-lg)",
          lineHeight: "var(--leading-relaxed)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-normal)",
        },
        ".text-body": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-base)",
          lineHeight: "var(--leading-normal)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-normal)",
        },
        ".text-caption": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-sm)",
          lineHeight: "var(--leading-normal)",
          letterSpacing: "var(--tracking-wide)",
          fontWeight: "var(--font-normal)",
        },
        ".text-overline": {
          fontFamily: "var(--font-sans)",
          fontSize: "var(--text-xs)",
          lineHeight: "var(--leading-normal)",
          letterSpacing: "var(--tracking-widest)",
          fontWeight: "var(--font-medium)",
          textTransform: "uppercase",
        },
        ".text-code": {
          fontFamily: "var(--font-mono)",
          fontSize: "var(--text-sm)",
          lineHeight: "var(--leading-relaxed)",
          letterSpacing: "var(--tracking-normal)",
          fontWeight: "var(--font-normal)",
        },
      };

      addUtilities(newUtilities);
    },
  ],
} satisfies Config;
