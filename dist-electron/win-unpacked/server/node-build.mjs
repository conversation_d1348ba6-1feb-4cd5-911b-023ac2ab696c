import path from "path";
import express from "express";
import "dotenv/config";
import cors from "cors";
import { createServer as createServer$1 } from "http";
import { z } from "zod";
import { WebSocketServer, WebSocket } from "ws";
const handleDemo = (req, res) => {
  const response = {
    message: "Hello from Express server"
  };
  res.status(200).json(response);
};
const ProgressUpdateSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.enum(["sync", "upload", "download"]),
  progress: z.number().min(0).max(100),
  status: z.enum(["running", "completed", "failed", "paused"]),
  startTime: z.string().datetime().optional(),
  estimatedCompletion: z.string().datetime().optional(),
  currentFile: z.string().optional(),
  totalFiles: z.number().optional(),
  processedFiles: z.number().optional()
});
const EventUpdateSchema = z.object({
  id: z.string(),
  title: z.string(),
  message: z.string(),
  type: z.enum(["success", "error", "warning", "info"]),
  category: z.enum(["sync", "upload", "download", "system", "auth"]),
  timestamp: z.string().datetime().optional(),
  clientPath: z.string().optional(),
  details: z.string().optional()
});
let wsManager = null;
function setWebSocketManager(manager) {
  console.log("🔗 WebSocket manager initialized:", !!manager);
  wsManager = manager;
}
const handleProgressUpdate = (req, res) => {
  try {
    const progressData = ProgressUpdateSchema.parse(req.body);
    const processedData = {
      ...progressData,
      startTime: progressData.startTime ? new Date(progressData.startTime) : /* @__PURE__ */ new Date(),
      estimatedCompletion: progressData.estimatedCompletion ? new Date(progressData.estimatedCompletion) : void 0
    };
    if (wsManager) {
      wsManager.broadcast({
        type: "progress",
        data: processedData
      });
      console.log(
        `📊 Progress update broadcasted: ${progressData.title} - ${progressData.progress}%`
      );
      res.json({ success: true, message: "Progress update broadcasted" });
    } else {
      console.warn("⚠️ WebSocket manager not initialized");
      res.status(503).json({ error: "WebSocket service not available" });
    }
  } catch (error) {
    console.error("❌ Invalid progress data:", error);
    res.status(400).json({
      error: "Invalid progress data",
      details: error instanceof z.ZodError ? error.errors : error
    });
  }
};
const handleEventUpdate = (req, res) => {
  try {
    const eventData = EventUpdateSchema.parse(req.body);
    const processedData = {
      ...eventData,
      timestamp: eventData.timestamp ? new Date(eventData.timestamp) : /* @__PURE__ */ new Date()
    };
    if (wsManager) {
      wsManager.broadcast({
        type: "event",
        data: processedData
      });
      console.log(
        `📋 Event broadcasted: ${eventData.title} - ${eventData.type}`
      );
      res.json({ success: true, message: "Event broadcasted" });
    } else {
      console.warn("⚠️ WebSocket manager not initialized");
      res.status(503).json({ error: "WebSocket service not available" });
    }
  } catch (error) {
    console.error("❌ Invalid event data:", error);
    res.status(400).json({
      error: "Invalid event data",
      details: error instanceof z.ZodError ? error.errors : error
    });
  }
};
const handleBulkUpdate = (req, res) => {
  try {
    const { progress = [], events = [] } = req.body;
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    progress.forEach((item, index) => {
      try {
        const progressData = ProgressUpdateSchema.parse(item);
        const processedData = {
          ...progressData,
          startTime: progressData.startTime ? new Date(progressData.startTime) : /* @__PURE__ */ new Date(),
          estimatedCompletion: progressData.estimatedCompletion ? new Date(progressData.estimatedCompletion) : void 0
        };
        if (wsManager) {
          wsManager.broadcast({
            type: "progress",
            data: processedData
          });
          successCount++;
        }
      } catch (error) {
        errorCount++;
        errors.push({
          type: "progress",
          index,
          error: error instanceof z.ZodError ? error.errors : error
        });
      }
    });
    events.forEach((item, index) => {
      try {
        const eventData = EventUpdateSchema.parse(item);
        const processedData = {
          ...eventData,
          timestamp: eventData.timestamp ? new Date(eventData.timestamp) : /* @__PURE__ */ new Date()
        };
        if (wsManager) {
          wsManager.broadcast({
            type: "event",
            data: processedData
          });
          successCount++;
        }
      } catch (error) {
        errorCount++;
        errors.push({
          type: "event",
          index,
          error: error instanceof z.ZodError ? error.errors : error
        });
      }
    });
    console.log(
      `📦 Bulk update: ${successCount} successful, ${errorCount} errors`
    );
    res.json({
      success: errorCount === 0,
      processed: successCount,
      errors: errorCount,
      errorDetails: errors.length > 0 ? errors : void 0
    });
  } catch (error) {
    console.error("❌ Bulk update error:", error);
    res.status(400).json({ error: "Invalid bulk update data" });
  }
};
const handleWebSocketHealth = (_req, res) => {
  try {
    const isDevelopment = false;
    const wsManagerExists = !!wsManager;
    let connectedClients = 0;
    let isHealthy = false;
    if (wsManagerExists) {
      try {
        connectedClients = wsManager.getConnectedClientsCount();
        isHealthy = true;
        console.log(
          "✅ WebSocket manager is healthy, connected clients:",
          connectedClients
        );
      } catch (error) {
        console.error("❌ Error getting connected clients count:", error);
        isHealthy = false;
      }
    } else {
      console.warn("⚠️ WebSocket manager not available for health check");
    }
    const response = {
      status: isHealthy ? "healthy" : "unhealthy",
      environment: isDevelopment ? "development" : "production",
      connectedClients,
      wsManagerAvailable: wsManagerExists,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      details: wsManagerExists ? "WebSocket manager available" : "WebSocket manager not initialized"
    };
    res.json(response);
  } catch (error) {
    console.error("💀 Fatal error in WebSocket health check:", error);
    res.status(500).json({
      status: "error",
      environment: "production",
      connectedClients: 0,
      wsManagerAvailable: false,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      error: error.message
    });
  }
};
const VALID_API_KEYS = /* @__PURE__ */ new Set([
  process.env.API_KEY || "demo-api-key-12345",
  "external-app-key-67890"
  // Add more keys as needed
]);
const authenticateApiKey = (req, res, next) => {
  const apiKey = req.headers["x-api-key"] || req.headers["authorization"]?.replace("Bearer ", "");
  if (!apiKey) {
    return res.status(401).json({
      error: "Missing API key. Include X-API-Key header or Authorization: Bearer token"
    });
  }
  if (!VALID_API_KEYS.has(apiKey)) {
    return res.status(403).json({
      error: "Invalid API key"
    });
  }
  req.apiKey = apiKey;
  next();
};
const requestCounts = /* @__PURE__ */ new Map();
const RATE_LIMIT = 100;
const RATE_WINDOW = 60 * 1e3;
const rateLimitByApiKey = (req, res, next) => {
  const apiKey = req.apiKey || "unknown";
  const now = Date.now();
  const keyData = requestCounts.get(apiKey);
  if (!keyData || now > keyData.resetTime) {
    requestCounts.set(apiKey, { count: 1, resetTime: now + RATE_WINDOW });
    next();
  } else if (keyData.count < RATE_LIMIT) {
    keyData.count++;
    next();
  } else {
    res.status(429).json({
      error: "Rate limit exceeded",
      limit: RATE_LIMIT,
      windowMs: RATE_WINDOW,
      resetTime: new Date(keyData.resetTime).toISOString()
    });
  }
};
const logApiUsage = (req, res, next) => {
  const apiKey = req.apiKey || "unknown";
  const timestamp = (/* @__PURE__ */ new Date()).toISOString();
  console.log(`📡 API Request: ${req.method} ${req.path} from ${apiKey} at ${timestamp}`);
  next();
};
const CreateScheduleSchema = z.object({
  name: z.string().min(1, "Schedule name is required"),
  sourceFolder: z.string().min(1, "Source folder is required"),
  destinationFolder: z.string().min(1, "Destination folder is required"),
  clientId: z.string().min(1, "Client ID is required"),
  interval: z.enum(["daily", "weekly", "monthly"]),
  time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  dayOfWeek: z.number().min(0).max(6).optional(),
  dayOfMonth: z.number().min(1).max(28).optional(),
  isActive: z.boolean(),
  exclusions: z.array(z.string()),
  description: z.string().optional()
});
let schedules = [];
function calculateNextSync(schedule) {
  const now = /* @__PURE__ */ new Date();
  const [hours, minutes] = schedule.time.split(":").map(Number);
  let nextSync = /* @__PURE__ */ new Date();
  nextSync.setHours(hours, minutes, 0, 0);
  if (nextSync <= now) {
    switch (schedule.interval) {
      case "daily":
        nextSync.setDate(nextSync.getDate() + 1);
        break;
      case "weekly":
        nextSync.setDate(nextSync.getDate() + 7);
        break;
      case "monthly":
        nextSync.setMonth(nextSync.getMonth() + 1);
        break;
    }
  }
  if (schedule.interval === "weekly" && schedule.dayOfWeek !== void 0) {
    const daysDiff = (schedule.dayOfWeek - nextSync.getDay() + 7) % 7;
    if (daysDiff === 0 && nextSync <= now) {
      nextSync.setDate(nextSync.getDate() + 7);
    } else {
      nextSync.setDate(nextSync.getDate() + daysDiff);
    }
  }
  if (schedule.interval === "monthly" && schedule.dayOfMonth !== void 0) {
    nextSync.setDate(schedule.dayOfMonth);
    if (nextSync <= now) {
      nextSync.setMonth(nextSync.getMonth() + 1);
      nextSync.setDate(schedule.dayOfMonth);
    }
  }
  return nextSync;
}
const createSchedule = (req, res) => {
  try {
    const validatedData = CreateScheduleSchema.parse(req.body);
    if (validatedData.interval === "weekly" && validatedData.dayOfWeek === void 0) {
      return res.status(400).json({
        error: "dayOfWeek is required for weekly schedules"
      });
    }
    if (validatedData.interval === "monthly" && validatedData.dayOfMonth === void 0) {
      return res.status(400).json({
        error: "dayOfMonth is required for monthly schedules"
      });
    }
    const schedule = {
      ...validatedData,
      id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date(),
      nextSync: calculateNextSync(validatedData),
      status: validatedData.isActive ? "pending" : "paused",
      syncHistory: []
    };
    schedules.push(schedule);
    console.log(`Created new schedule: ${schedule.name} (${schedule.id})`);
    res.status(201).json({
      message: "Schedule created successfully",
      schedule
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Validation error",
        details: error.errors
      });
    }
    console.error("Error creating schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const getSchedules = (req, res) => {
  try {
    res.json({
      schedules: schedules.sort(
        (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
      ),
      total: schedules.length
    });
  } catch (error) {
    console.error("Error fetching schedules:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const getSchedule = (req, res) => {
  try {
    const { id } = req.params;
    const schedule = schedules.find((s) => s.id === id);
    if (!schedule) {
      return res.status(404).json({ error: "Schedule not found" });
    }
    res.json({ schedule });
  } catch (error) {
    console.error("Error fetching schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const updateSchedule = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);
    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }
    const validatedData = CreateScheduleSchema.partial().parse(req.body);
    const currentSchedule = schedules[scheduleIndex];
    const updatedSchedule = {
      ...currentSchedule,
      ...validatedData,
      updatedAt: /* @__PURE__ */ new Date()
    };
    if (validatedData.interval || validatedData.time || validatedData.dayOfWeek || validatedData.dayOfMonth) {
      updatedSchedule.nextSync = calculateNextSync(updatedSchedule);
    }
    schedules[scheduleIndex] = updatedSchedule;
    console.log(
      `Updated schedule: ${updatedSchedule.name} (${updatedSchedule.id})`
    );
    res.json({
      message: "Schedule updated successfully",
      schedule: updatedSchedule
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Validation error",
        details: error.errors
      });
    }
    console.error("Error updating schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const deleteSchedule = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);
    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }
    const deletedSchedule = schedules.splice(scheduleIndex, 1)[0];
    console.log(
      `Deleted schedule: ${deletedSchedule.name} (${deletedSchedule.id})`
    );
    res.json({
      message: "Schedule deleted successfully",
      schedule: deletedSchedule
    });
  } catch (error) {
    console.error("Error deleting schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const toggleSchedule = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);
    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }
    const schedule = schedules[scheduleIndex];
    schedule.isActive = !schedule.isActive;
    schedule.status = schedule.isActive ? "pending" : "paused";
    schedule.updatedAt = /* @__PURE__ */ new Date();
    if (schedule.isActive) {
      schedule.nextSync = calculateNextSync(schedule);
    }
    console.log(
      `Toggled schedule: ${schedule.name} (${schedule.id}) - ${schedule.isActive ? "Active" : "Paused"}`
    );
    res.json({
      message: `Schedule ${schedule.isActive ? "resumed" : "paused"} successfully`,
      schedule
    });
  } catch (error) {
    console.error("Error toggling schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
const executeSchedule = (req, res) => {
  try {
    const { id } = req.params;
    const scheduleIndex = schedules.findIndex((s) => s.id === id);
    if (scheduleIndex === -1) {
      return res.status(404).json({ error: "Schedule not found" });
    }
    const schedule = schedules[scheduleIndex];
    if (schedule.status === "syncing") {
      return res.status(400).json({ error: "Schedule is already running" });
    }
    schedule.status = "syncing";
    schedule.progress = 0;
    schedule.updatedAt = /* @__PURE__ */ new Date();
    schedule.lastSync = /* @__PURE__ */ new Date();
    console.log(
      `Started immediate execution of schedule: ${schedule.name} (${schedule.id})`
    );
    setTimeout(() => {
      if (schedules[scheduleIndex]) {
        schedules[scheduleIndex].status = "completed";
        schedules[scheduleIndex].progress = 100;
        schedules[scheduleIndex].nextSync = calculateNextSync(schedule);
        schedules[scheduleIndex].syncHistory.unshift({
          startTime: schedule.lastSync,
          endTime: /* @__PURE__ */ new Date(),
          status: "success",
          filesSynced: Math.floor(Math.random() * 100) + 50,
          filesProcessed: Math.floor(Math.random() * 120) + 80
        });
      }
    }, 5e3);
    res.json({
      message: "Schedule execution started",
      schedule
    });
  } catch (error) {
    console.error("Error executing schedule:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
class WebSocketManager {
  wss;
  clients = /* @__PURE__ */ new Set();
  recentProgress = /* @__PURE__ */ new Map();
  recentEvents = [];
  constructor(server) {
    console.log("🔧 Creating WebSocket manager with server:", !!server);
    try {
      this.wss = new WebSocketServer({
        noServer: true
        // Don't create a separate HTTP server
      });
      console.log("✅ WebSocketServer created successfully");
      server.on("upgrade", (request, socket, head) => {
        console.log("🔄 WebSocket upgrade request for:", request.url);
        if (request.url === "/ws") {
          this.wss.handleUpgrade(request, socket, head, (ws) => {
            this.wss.emit("connection", ws, request);
          });
        }
      });
      console.log("✅ WebSocket upgrade handler registered");
      this.setupWebSocket();
      this.startMinimalDemoData();
      console.log("✅ WebSocket manager fully initialized");
    } catch (error) {
      console.error("💀 Error in WebSocket manager constructor:", error);
      throw error;
    }
  }
  setupWebSocket() {
    this.wss.on("connection", (ws) => {
      console.log("WebSocket client connected");
      this.clients.add(ws);
      this.sendInitialData(ws);
      ws.on("close", () => {
        console.log("WebSocket client disconnected");
        this.clients.delete(ws);
      });
      ws.on("error", (error) => {
        console.error("WebSocket error:", error);
        this.clients.delete(ws);
      });
    });
  }
  sendInitialData(ws) {
    this.recentProgress.forEach((progress) => {
      this.sendMessage(ws, { type: "progress", data: progress });
    });
    this.recentEvents.slice(-20).forEach((event) => {
      this.sendMessage(ws, { type: "event", data: event });
    });
    if (this.recentProgress.size === 0 && this.recentEvents.length === 0) {
      this.sendMessage(ws, {
        type: "event",
        data: {
          id: "welcome",
          title: "WebSocket Connected",
          message: "Ready to receive updates from external applications",
          type: "info",
          category: "system",
          timestamp: /* @__PURE__ */ new Date()
        }
      });
    }
  }
  sendMessage(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }
  broadcast(message) {
    if (message.type === "progress") {
      this.recentProgress.set(message.data.id, message.data);
      if (message.data.status === "completed") {
        setTimeout(
          () => {
            this.recentProgress.delete(message.data.id);
          },
          5 * 60 * 1e3
        );
      }
    } else if (message.type === "event") {
      this.recentEvents.push(message.data);
      if (this.recentEvents.length > 50) {
        this.recentEvents = this.recentEvents.slice(-50);
      }
    }
    this.clients.forEach((ws) => {
      this.sendMessage(ws, message);
    });
  }
  getConnectedClientsCount() {
    return this.clients.size;
  }
  getRecentProgress() {
    return Array.from(this.recentProgress.values());
  }
  getRecentEvents() {
    return this.recentEvents.slice(-20);
  }
  clearProgress(progressId) {
    return this.recentProgress.delete(progressId);
  }
  clearAllProgress() {
    this.recentProgress.clear();
  }
  clearEvents() {
    this.recentEvents = [];
  }
  startMinimalDemoData() {
    let lastExternalDataTime = Date.now();
    const originalBroadcast = this.broadcast.bind(this);
    this.broadcast = (message) => {
      lastExternalDataTime = Date.now();
      originalBroadcast(message);
    };
    const demoInterval = setInterval(() => {
      const timeSinceLastExternal = Date.now() - lastExternalDataTime;
      if (timeSinceLastExternal > 3e4 && this.recentProgress.size === 0) {
        const demoEvent = {
          type: "event",
          data: {
            id: `demo-${Date.now()}`,
            title: "Waiting for external data",
            message: "No external applications are currently sending data. Send POST requests to /api/progress or /api/events to see real-time updates.",
            type: "info",
            category: "system",
            timestamp: /* @__PURE__ */ new Date()
          }
        };
        originalBroadcast(demoEvent);
      }
    }, 6e4);
    process.on("SIGTERM", () => {
      clearInterval(demoInterval);
    });
  }
}
function createServer() {
  const app2 = express();
  const httpServer2 = createServer$1(app2);
  app2.use(cors());
  app2.use(express.json({ limit: "10mb" }));
  app2.use(express.urlencoded({ extended: true }));
  app2.get("/api/ping", (_req, res) => {
    const ping = process.env.PING_MESSAGE ?? "ping";
    res.json({ message: ping });
  });
  app2.get("/api/demo", handleDemo);
  app2.post("/api/schedules", createSchedule);
  app2.get("/api/schedules", getSchedules);
  app2.get("/api/schedules/:id", getSchedule);
  app2.put("/api/schedules/:id", updateSchedule);
  app2.delete("/api/schedules/:id", deleteSchedule);
  app2.patch("/api/schedules/:id/toggle", toggleSchedule);
  app2.post("/api/schedules/:id/execute", executeSchedule);
  app2.post(
    "/api/progress",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleProgressUpdate
  );
  app2.post(
    "/api/events",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleEventUpdate
  );
  app2.post(
    "/api/bulk",
    authenticateApiKey,
    rateLimitByApiKey,
    logApiUsage,
    handleBulkUpdate
  );
  app2.get("/api/ws-health", handleWebSocketHealth);
  try {
    console.log("🚀 Initializing WebSocket manager...");
    const wsManager2 = new WebSocketManager(httpServer2);
    setWebSocketManager(wsManager2);
    console.log("✅ WebSocket manager initialized successfully");
  } catch (error) {
    console.error("💀 FATAL: Failed to initialize WebSocket manager:", error);
    setWebSocketManager(null);
  }
  return httpServer2;
}
const httpServer = createServer();
const port = process.env.PORT || 3e3;
new WebSocketManager(httpServer);
const __dirname = import.meta.dirname;
const distPath = path.join(__dirname, "../spa");
const app = express();
app.use(express.static(distPath));
app.get("*", (req, res) => {
  if (req.path.startsWith("/api/") || req.path.startsWith("/health") || req.path.startsWith("/ws")) {
    return res.status(404).json({ error: "Endpoint not found" });
  }
  res.sendFile(path.join(distPath, "index.html"));
});
httpServer.on("request", (req, res) => {
  const originalListeners = httpServer.listeners("request");
  let handled = false;
  for (const listener of originalListeners) {
    if (listener !== arguments.callee) {
      try {
        listener(req, res);
        if (res.headersSent) {
          handled = true;
          break;
        }
      } catch (e) {
      }
    }
  }
  if (!handled && !res.headersSent) {
    app(req, res);
  }
});
httpServer.listen(port, () => {
  console.log(`🚀 Fusion Starter server running on port ${port}`);
  console.log(`📱 Frontend: http://localhost:${port}`);
  console.log(`🔧 API: http://localhost:${port}/api`);
  console.log(`🔌 WebSocket: ws://localhost:${port}/ws`);
});
process.on("SIGTERM", () => {
  console.log("🛑 Received SIGTERM, shutting down gracefully");
  process.exit(0);
});
process.on("SIGINT", () => {
  console.log("🛑 Received SIGINT, shutting down gracefully");
  process.exit(0);
});
//# sourceMappingURL=node-build.mjs.map
