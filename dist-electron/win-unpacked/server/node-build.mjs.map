{"version": 3, "file": "node-build.mjs", "sources": ["../../server/routes/demo.ts", "../../server/routes/websocket-data.ts", "../../server/middleware/auth.ts", "../../server/routes/schedules.ts", "../../server/websocket.ts", "../../server/index.ts", "../../server/node-build.ts"], "sourcesContent": ["import { RequestHand<PERSON> } from \"express\";\nimport { DemoResponse } from \"@shared/api\";\n\nexport const handleDemo: RequestHandler = (req, res) => {\n  const response: DemoResponse = {\n    message: \"Hello from Express server\",\n  };\n  res.status(200).json(response);\n};\n", "import { Request<PERSON>and<PERSON> } from \"express\";\r\nimport { z } from \"zod\";\r\n\r\n// Validation schemas\r\nconst ProgressUpdateSchema = z.object({\r\n  id: z.string(),\r\n  title: z.string(),\r\n  type: z.enum([\"sync\", \"upload\", \"download\"]),\r\n  progress: z.number().min(0).max(100),\r\n  status: z.enum([\"running\", \"completed\", \"failed\", \"paused\"]),\r\n  startTime: z.string().datetime().optional(),\r\n  estimatedCompletion: z.string().datetime().optional(),\r\n  currentFile: z.string().optional(),\r\n  totalFiles: z.number().optional(),\r\n  processedFiles: z.number().optional(),\r\n});\r\n\r\nconst EventUpdateSchema = z.object({\r\n  id: z.string(),\r\n  title: z.string(),\r\n  message: z.string(),\r\n  type: z.enum([\"success\", \"error\", \"warning\", \"info\"]),\r\n  category: z.enum([\"sync\", \"upload\", \"download\", \"system\", \"auth\"]),\r\n  timestamp: z.string().datetime().optional(),\r\n  clientPath: z.string().optional(),\r\n  details: z.string().optional(),\r\n});\r\n\r\n// Store WebSocket manager reference\r\nlet wsManager: any = null;\r\n\r\nexport function setWebSocketManager(manager: any) {\r\n  console.log(\"🔗 WebSocket manager initialized:\", !!manager);\r\n  wsManager = manager;\r\n}\r\n\r\nexport function getWebSocketManager() {\r\n  return wsManager;\r\n}\r\n\r\nexport const handleProgressUpdate: RequestHandler = (req, res) => {\r\n  try {\r\n    const progressData = ProgressUpdateSchema.parse(req.body);\r\n\r\n    // Convert date strings to Date objects\r\n    const processedData = {\r\n      ...progressData,\r\n      startTime: progressData.startTime\r\n        ? new Date(progressData.startTime)\r\n        : new Date(),\r\n      estimatedCompletion: progressData.estimatedCompletion\r\n        ? new Date(progressData.estimatedCompletion)\r\n        : undefined,\r\n    };\r\n\r\n    // Broadcast to all connected WebSocket clients\r\n    if (wsManager) {\r\n      wsManager.broadcast({\r\n        type: \"progress\",\r\n        data: processedData,\r\n      });\r\n\r\n      console.log(\r\n        `📊 Progress update broadcasted: ${progressData.title} - ${progressData.progress}%`,\r\n      );\r\n      res.json({ success: true, message: \"Progress update broadcasted\" });\r\n    } else {\r\n      console.warn(\"⚠️ WebSocket manager not initialized\");\r\n      res.status(503).json({ error: \"WebSocket service not available\" });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"❌ Invalid progress data:\", error);\r\n    res.status(400).json({\r\n      error: \"Invalid progress data\",\r\n      details: error instanceof z.ZodError ? error.errors : error,\r\n    });\r\n  }\r\n};\r\n\r\nexport const handleEventUpdate: RequestHandler = (req, res) => {\r\n  try {\r\n    const eventData = EventUpdateSchema.parse(req.body);\r\n\r\n    // Convert date string to Date object\r\n    const processedData = {\r\n      ...eventData,\r\n      timestamp: eventData.timestamp\r\n        ? new Date(eventData.timestamp)\r\n        : new Date(),\r\n    };\r\n\r\n    // Broadcast to all connected WebSocket clients\r\n    if (wsManager) {\r\n      wsManager.broadcast({\r\n        type: \"event\",\r\n        data: processedData,\r\n      });\r\n\r\n      console.log(\r\n        `📋 Event broadcasted: ${eventData.title} - ${eventData.type}`,\r\n      );\r\n      res.json({ success: true, message: \"Event broadcasted\" });\r\n    } else {\r\n      console.warn(\"⚠️ WebSocket manager not initialized\");\r\n      res.status(503).json({ error: \"WebSocket service not available\" });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"❌ Invalid event data:\", error);\r\n    res.status(400).json({\r\n      error: \"Invalid event data\",\r\n      details: error instanceof z.ZodError ? error.errors : error,\r\n    });\r\n  }\r\n};\r\n\r\nexport const handleBulkUpdate: RequestHandler = (req, res) => {\r\n  try {\r\n    const { progress = [], events = [] } = req.body;\r\n\r\n    let successCount = 0;\r\n    let errorCount = 0;\r\n    const errors: any[] = [];\r\n\r\n    // Process progress updates\r\n    progress.forEach((item: any, index: number) => {\r\n      try {\r\n        const progressData = ProgressUpdateSchema.parse(item);\r\n        const processedData = {\r\n          ...progressData,\r\n          startTime: progressData.startTime\r\n            ? new Date(progressData.startTime)\r\n            : new Date(),\r\n          estimatedCompletion: progressData.estimatedCompletion\r\n            ? new Date(progressData.estimatedCompletion)\r\n            : undefined,\r\n        };\r\n\r\n        if (wsManager) {\r\n          wsManager.broadcast({\r\n            type: \"progress\",\r\n            data: processedData,\r\n          });\r\n          successCount++;\r\n        }\r\n      } catch (error) {\r\n        errorCount++;\r\n        errors.push({\r\n          type: \"progress\",\r\n          index,\r\n          error: error instanceof z.ZodError ? error.errors : error,\r\n        });\r\n      }\r\n    });\r\n\r\n    // Process event updates\r\n    events.forEach((item: any, index: number) => {\r\n      try {\r\n        const eventData = EventUpdateSchema.parse(item);\r\n        const processedData = {\r\n          ...eventData,\r\n          timestamp: eventData.timestamp\r\n            ? new Date(eventData.timestamp)\r\n            : new Date(),\r\n        };\r\n\r\n        if (wsManager) {\r\n          wsManager.broadcast({\r\n            type: \"event\",\r\n            data: processedData,\r\n          });\r\n          successCount++;\r\n        }\r\n      } catch (error) {\r\n        errorCount++;\r\n        errors.push({\r\n          type: \"event\",\r\n          index,\r\n          error: error instanceof z.ZodError ? error.errors : error,\r\n        });\r\n      }\r\n    });\r\n\r\n    console.log(\r\n      `📦 Bulk update: ${successCount} successful, ${errorCount} errors`,\r\n    );\r\n\r\n    res.json({\r\n      success: errorCount === 0,\r\n      processed: successCount,\r\n      errors: errorCount,\r\n      errorDetails: errors.length > 0 ? errors : undefined,\r\n    });\r\n  } catch (error) {\r\n    console.error(\"❌ Bulk update error:\", error);\r\n    res.status(400).json({ error: \"Invalid bulk update data\" });\r\n  }\r\n};\r\n\r\n// Health check endpoint for external apps\r\nexport const handleWebSocketHealth: RequestHandler = (_req, res) => {\r\n  try {\r\n    const isDevelopment = process.env.NODE_ENV === \"development\";\r\n    const wsManagerExists = !!wsManager;\r\n\r\n    let connectedClients = 0;\r\n    let isHealthy = false;\r\n\r\n    if (wsManagerExists) {\r\n      try {\r\n        connectedClients = wsManager.getConnectedClientsCount();\r\n        isHealthy = true;\r\n        console.log(\r\n          \"✅ WebSocket manager is healthy, connected clients:\",\r\n          connectedClients,\r\n        );\r\n      } catch (error) {\r\n        console.error(\"❌ Error getting connected clients count:\", error);\r\n        isHealthy = false;\r\n      }\r\n    } else {\r\n      console.warn(\"⚠️ WebSocket manager not available for health check\");\r\n    }\r\n\r\n    const response = {\r\n      status: isHealthy ? \"healthy\" : \"unhealthy\",\r\n      environment: isDevelopment ? \"development\" : \"production\",\r\n      connectedClients,\r\n      wsManagerAvailable: wsManagerExists,\r\n      timestamp: new Date().toISOString(),\r\n      details: wsManagerExists\r\n        ? \"WebSocket manager available\"\r\n        : \"WebSocket manager not initialized\",\r\n    };\r\n\r\n    res.json(response);\r\n  } catch (error) {\r\n    console.error(\"💀 Fatal error in WebSocket health check:\", error);\r\n    res.status(500).json({\r\n      status: \"error\",\r\n      environment:\r\n        process.env.NODE_ENV === \"development\" ? \"development\" : \"production\",\r\n      connectedClients: 0,\r\n      wsManagerAvailable: false,\r\n      timestamp: new Date().toISOString(),\r\n      error: error.message,\r\n    });\r\n  }\r\n};\r\n", "import { Request<PERSON><PERSON><PERSON> } from \"express\";\n\n// Simple API key authentication\n// In production, use a more secure system with database storage\nconst VALID_API_KEYS = new Set([\n  process.env.API_KEY || 'demo-api-key-12345',\n  'external-app-key-67890',\n  // Add more keys as needed\n]);\n\nexport const authenticateApiKey: RequestHandler = (req, res, next) => {\n  const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');\n  \n  if (!apiKey) {\n    return res.status(401).json({ \n      error: 'Missing API key. Include X-API-Key header or Authorization: Bearer token' \n    });\n  }\n\n  if (!VALID_API_KEYS.has(apiKey as string)) {\n    return res.status(403).json({ \n      error: 'Invalid API key' \n    });\n  }\n\n  // Store the API key in the request for logging\n  (req as any).apiKey = apiKey;\n  next();\n};\n\n// Rate limiting middleware\nconst requestCounts = new Map<string, { count: number; resetTime: number }>();\nconst RATE_LIMIT = 100; // requests per minute\nconst RATE_WINDOW = 60 * 1000; // 1 minute\n\nexport const rateLimitByApiKey: RequestHandler = (req, res, next) => {\n  const apiKey = (req as any).apiKey || 'unknown';\n  const now = Date.now();\n  \n  const keyData = requestCounts.get(apiKey);\n  \n  if (!keyData || now > keyData.resetTime) {\n    // Reset or initialize counter\n    requestCounts.set(apiKey, { count: 1, resetTime: now + RATE_WINDOW });\n    next();\n  } else if (keyData.count < RATE_LIMIT) {\n    // Increment counter\n    keyData.count++;\n    next();\n  } else {\n    // Rate limit exceeded\n    res.status(429).json({ \n      error: 'Rate limit exceeded',\n      limit: RATE_LIMIT,\n      windowMs: RATE_WINDOW,\n      resetTime: new Date(keyData.resetTime).toISOString()\n    });\n  }\n};\n\n// Optional: Middleware to log API usage\nexport const logApiUsage: RequestHandler = (req, res, next) => {\n  const apiKey = (req as any).apiKey || 'unknown';\n  const timestamp = new Date().toISOString();\n  \n  console.log(`📡 API Request: ${req.method} ${req.path} from ${apiKey} at ${timestamp}`);\n  \n  next();\n};\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"express\";\r\nimport { z } from \"zod\";\r\n\r\n// Validation schema for schedule creation\r\nconst CreateScheduleSchema = z.object({\r\n  name: z.string().min(1, \"Schedule name is required\"),\r\n  sourceFolder: z.string().min(1, \"Source folder is required\"),\r\n  destinationFolder: z.string().min(1, \"Destination folder is required\"),\r\n  clientId: z.string().min(1, \"Client ID is required\"),\r\n  interval: z.enum([\"daily\", \"weekly\", \"monthly\"]),\r\n  time: z\r\n    .string()\r\n    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, \"Invalid time format\"),\r\n  dayOfWeek: z.number().min(0).max(6).optional(),\r\n  dayOfMonth: z.number().min(1).max(28).optional(),\r\n  isActive: z.boolean(),\r\n  exclusions: z.array(z.string()),\r\n  description: z.string().optional(),\r\n});\r\n\r\nexport type CreateScheduleData = z.infer<typeof CreateScheduleSchema>;\r\n\r\ninterface Schedule extends CreateScheduleData {\r\n  id: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  lastSync?: Date;\r\n  nextSync: Date;\r\n  status: \"pending\" | \"syncing\" | \"completed\" | \"failed\" | \"paused\";\r\n  progress?: number;\r\n  error?: string;\r\n  syncHistory: Array<{\r\n    startTime: Date;\r\n    endTime?: Date;\r\n    status: \"success\" | \"error\";\r\n    filesSynced: number;\r\n    filesProcessed: number;\r\n    error?: string;\r\n  }>;\r\n}\r\n\r\n// In-memory storage for schedules (in production, this would be a database)\r\nlet schedules: Schedule[] = [];\r\n\r\n// Helper function to calculate next sync time\r\nfunction calculateNextSync(schedule: CreateScheduleData): Date {\r\n  const now = new Date();\r\n  const [hours, minutes] = schedule.time.split(\":\").map(Number);\r\n\r\n  let nextSync = new Date();\r\n  nextSync.setHours(hours, minutes, 0, 0);\r\n\r\n  // If the time has already passed today, move to next occurrence\r\n  if (nextSync <= now) {\r\n    switch (schedule.interval) {\r\n      case \"daily\":\r\n        nextSync.setDate(nextSync.getDate() + 1);\r\n        break;\r\n      case \"weekly\":\r\n        nextSync.setDate(nextSync.getDate() + 7);\r\n        break;\r\n      case \"monthly\":\r\n        nextSync.setMonth(nextSync.getMonth() + 1);\r\n        break;\r\n    }\r\n  }\r\n\r\n  // Adjust for weekly schedules\r\n  if (schedule.interval === \"weekly\" && schedule.dayOfWeek !== undefined) {\r\n    const daysDiff = (schedule.dayOfWeek - nextSync.getDay() + 7) % 7;\r\n    if (daysDiff === 0 && nextSync <= now) {\r\n      nextSync.setDate(nextSync.getDate() + 7);\r\n    } else {\r\n      nextSync.setDate(nextSync.getDate() + daysDiff);\r\n    }\r\n  }\r\n\r\n  // Adjust for monthly schedules\r\n  if (schedule.interval === \"monthly\" && schedule.dayOfMonth !== undefined) {\r\n    nextSync.setDate(schedule.dayOfMonth);\r\n    if (nextSync <= now) {\r\n      nextSync.setMonth(nextSync.getMonth() + 1);\r\n      nextSync.setDate(schedule.dayOfMonth);\r\n    }\r\n  }\r\n\r\n  return nextSync;\r\n}\r\n\r\n// Create a new schedule\r\nexport const createSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const validatedData = CreateScheduleSchema.parse(req.body);\r\n\r\n    // Validate interval-specific requirements\r\n    if (\r\n      validatedData.interval === \"weekly\" &&\r\n      validatedData.dayOfWeek === undefined\r\n    ) {\r\n      return res.status(400).json({\r\n        error: \"dayOfWeek is required for weekly schedules\",\r\n      });\r\n    }\r\n\r\n    if (\r\n      validatedData.interval === \"monthly\" &&\r\n      validatedData.dayOfMonth === undefined\r\n    ) {\r\n      return res.status(400).json({\r\n        error: \"dayOfMonth is required for monthly schedules\",\r\n      });\r\n    }\r\n\r\n    const schedule: Schedule = {\r\n      ...validatedData,\r\n      id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n      nextSync: calculateNextSync(validatedData),\r\n      status: validatedData.isActive ? \"pending\" : \"paused\",\r\n      syncHistory: [],\r\n    };\r\n\r\n    schedules.push(schedule);\r\n\r\n    console.log(`Created new schedule: ${schedule.name} (${schedule.id})`);\r\n\r\n    res.status(201).json({\r\n      message: \"Schedule created successfully\",\r\n      schedule,\r\n    });\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return res.status(400).json({\r\n        error: \"Validation error\",\r\n        details: error.errors,\r\n      });\r\n    }\r\n\r\n    console.error(\"Error creating schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Get all schedules\r\nexport const getSchedules: RequestHandler = (req, res) => {\r\n  try {\r\n    res.json({\r\n      schedules: schedules.sort(\r\n        (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n      ),\r\n      total: schedules.length,\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching schedules:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Get a specific schedule\r\nexport const getSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const { id } = req.params;\r\n    const schedule = schedules.find((s) => s.id === id);\r\n\r\n    if (!schedule) {\r\n      return res.status(404).json({ error: \"Schedule not found\" });\r\n    }\r\n\r\n    res.json({ schedule });\r\n  } catch (error) {\r\n    console.error(\"Error fetching schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Update a schedule\r\nexport const updateSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const { id } = req.params;\r\n    const scheduleIndex = schedules.findIndex((s) => s.id === id);\r\n\r\n    if (scheduleIndex === -1) {\r\n      return res.status(404).json({ error: \"Schedule not found\" });\r\n    }\r\n\r\n    const validatedData = CreateScheduleSchema.partial().parse(req.body);\r\n    const currentSchedule = schedules[scheduleIndex];\r\n\r\n    const updatedSchedule: Schedule = {\r\n      ...currentSchedule,\r\n      ...validatedData,\r\n      updatedAt: new Date(),\r\n    };\r\n\r\n    // Recalculate next sync if timing data changed\r\n    if (\r\n      validatedData.interval ||\r\n      validatedData.time ||\r\n      validatedData.dayOfWeek ||\r\n      validatedData.dayOfMonth\r\n    ) {\r\n      updatedSchedule.nextSync = calculateNextSync(updatedSchedule);\r\n    }\r\n\r\n    schedules[scheduleIndex] = updatedSchedule;\r\n\r\n    console.log(\r\n      `Updated schedule: ${updatedSchedule.name} (${updatedSchedule.id})`,\r\n    );\r\n\r\n    res.json({\r\n      message: \"Schedule updated successfully\",\r\n      schedule: updatedSchedule,\r\n    });\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return res.status(400).json({\r\n        error: \"Validation error\",\r\n        details: error.errors,\r\n      });\r\n    }\r\n\r\n    console.error(\"Error updating schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Delete a schedule\r\nexport const deleteSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const { id } = req.params;\r\n    const scheduleIndex = schedules.findIndex((s) => s.id === id);\r\n\r\n    if (scheduleIndex === -1) {\r\n      return res.status(404).json({ error: \"Schedule not found\" });\r\n    }\r\n\r\n    const deletedSchedule = schedules.splice(scheduleIndex, 1)[0];\r\n\r\n    console.log(\r\n      `Deleted schedule: ${deletedSchedule.name} (${deletedSchedule.id})`,\r\n    );\r\n\r\n    res.json({\r\n      message: \"Schedule deleted successfully\",\r\n      schedule: deletedSchedule,\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error deleting schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Toggle schedule status (pause/resume)\r\nexport const toggleSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const { id } = req.params;\r\n    const scheduleIndex = schedules.findIndex((s) => s.id === id);\r\n\r\n    if (scheduleIndex === -1) {\r\n      return res.status(404).json({ error: \"Schedule not found\" });\r\n    }\r\n\r\n    const schedule = schedules[scheduleIndex];\r\n    schedule.isActive = !schedule.isActive;\r\n    schedule.status = schedule.isActive ? \"pending\" : \"paused\";\r\n    schedule.updatedAt = new Date();\r\n\r\n    if (schedule.isActive) {\r\n      schedule.nextSync = calculateNextSync(schedule);\r\n    }\r\n\r\n    console.log(\r\n      `Toggled schedule: ${schedule.name} (${schedule.id}) - ${schedule.isActive ? \"Active\" : \"Paused\"}`,\r\n    );\r\n\r\n    res.json({\r\n      message: `Schedule ${schedule.isActive ? \"resumed\" : \"paused\"} successfully`,\r\n      schedule,\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error toggling schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n\r\n// Execute a schedule immediately\r\nexport const executeSchedule: RequestHandler = (req, res) => {\r\n  try {\r\n    const { id } = req.params;\r\n    const scheduleIndex = schedules.findIndex((s) => s.id === id);\r\n\r\n    if (scheduleIndex === -1) {\r\n      return res.status(404).json({ error: \"Schedule not found\" });\r\n    }\r\n\r\n    const schedule = schedules[scheduleIndex];\r\n\r\n    if (schedule.status === \"syncing\") {\r\n      return res.status(400).json({ error: \"Schedule is already running\" });\r\n    }\r\n\r\n    // Update schedule status\r\n    schedule.status = \"syncing\";\r\n    schedule.progress = 0;\r\n    schedule.updatedAt = new Date();\r\n    schedule.lastSync = new Date();\r\n\r\n    console.log(\r\n      `Started immediate execution of schedule: ${schedule.name} (${schedule.id})`,\r\n    );\r\n\r\n    // Simulate sync process (in production, this would trigger actual sync)\r\n    setTimeout(() => {\r\n      if (schedules[scheduleIndex]) {\r\n        schedules[scheduleIndex].status = \"completed\";\r\n        schedules[scheduleIndex].progress = 100;\r\n        schedules[scheduleIndex].nextSync = calculateNextSync(schedule);\r\n        schedules[scheduleIndex].syncHistory.unshift({\r\n          startTime: schedule.lastSync!,\r\n          endTime: new Date(),\r\n          status: \"success\",\r\n          filesSynced: Math.floor(Math.random() * 100) + 50,\r\n          filesProcessed: Math.floor(Math.random() * 120) + 80,\r\n        });\r\n      }\r\n    }, 5000);\r\n\r\n    res.json({\r\n      message: \"Schedule execution started\",\r\n      schedule,\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error executing schedule:\", error);\r\n    res.status(500).json({ error: \"Internal server error\" });\r\n  }\r\n};\r\n", "import { WebSocketServer, WebSocket } from \"ws\";\r\nimport { Server } from \"http\";\r\n\r\ninterface ProgressUpdate {\r\n  type: \"progress\";\r\n  data: {\r\n    id: string;\r\n    title: string;\r\n    type: \"sync\" | \"upload\" | \"download\";\r\n    progress: number;\r\n    status: \"running\" | \"completed\" | \"failed\" | \"paused\";\r\n    startTime: Date;\r\n    estimatedCompletion?: Date;\r\n    currentFile?: string;\r\n    totalFiles?: number;\r\n    processedFiles?: number;\r\n  };\r\n}\r\n\r\ninterface EventUpdate {\r\n  type: \"event\";\r\n  data: {\r\n    id: string;\r\n    title: string;\r\n    message: string;\r\n    type: \"success\" | \"error\" | \"warning\" | \"info\";\r\n    category: \"sync\" | \"upload\" | \"download\" | \"system\" | \"auth\";\r\n    timestamp: Date;\r\n    clientPath?: string;\r\n    details?: string;\r\n  };\r\n}\r\n\r\ntype WSMessage = ProgressUpdate | EventUpdate;\r\n\r\nexport class WebSocketManager {\r\n  private wss: WebSocketServer;\r\n  private clients: Set<WebSocket> = new Set();\r\n  private recentProgress: Map<string, any> = new Map();\r\n  private recentEvents: any[] = [];\r\n\r\n  constructor(server: Server) {\r\n    console.log(\"🔧 Creating WebSocket manager with server:\", !!server);\r\n\r\n    try {\r\n      this.wss = new WebSocketServer({\r\n        noServer: true, // Don't create a separate HTTP server\r\n      });\r\n      console.log(\"✅ WebSocketServer created successfully\");\r\n\r\n      // Handle WebSocket upgrade on the existing server\r\n      server.on(\"upgrade\", (request, socket, head) => {\r\n        console.log(\"🔄 WebSocket upgrade request for:\", request.url);\r\n        if (request.url === \"/ws\") {\r\n          this.wss.handleUpgrade(request, socket, head, (ws) => {\r\n            this.wss.emit(\"connection\", ws, request);\r\n          });\r\n        }\r\n      });\r\n      console.log(\"✅ WebSocket upgrade handler registered\");\r\n\r\n      this.setupWebSocket();\r\n      this.startMinimalDemoData(); // Reduced demo data\r\n      console.log(\"✅ WebSocket manager fully initialized\");\r\n    } catch (error) {\r\n      console.error(\"💀 Error in WebSocket manager constructor:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private setupWebSocket() {\r\n    this.wss.on(\"connection\", (ws: WebSocket) => {\r\n      console.log(\"WebSocket client connected\");\r\n      this.clients.add(ws);\r\n\r\n      // Send initial data when client connects\r\n      this.sendInitialData(ws);\r\n\r\n      ws.on(\"close\", () => {\r\n        console.log(\"WebSocket client disconnected\");\r\n        this.clients.delete(ws);\r\n      });\r\n\r\n      ws.on(\"error\", (error) => {\r\n        console.error(\"WebSocket error:\", error);\r\n        this.clients.delete(ws);\r\n      });\r\n    });\r\n  }\r\n\r\n  private sendInitialData(ws: WebSocket) {\r\n    // Send stored progress items\r\n    this.recentProgress.forEach((progress) => {\r\n      this.sendMessage(ws, { type: \"progress\", data: progress });\r\n    });\r\n\r\n    // Send recent events (last 20)\r\n    this.recentEvents.slice(-20).forEach((event) => {\r\n      this.sendMessage(ws, { type: \"event\", data: event });\r\n    });\r\n\r\n    // Send a welcome message if no data exists\r\n    if (this.recentProgress.size === 0 && this.recentEvents.length === 0) {\r\n      this.sendMessage(ws, {\r\n        type: \"event\",\r\n        data: {\r\n          id: \"welcome\",\r\n          title: \"WebSocket Connected\",\r\n          message: \"Ready to receive updates from external applications\",\r\n          type: \"info\" as const,\r\n          category: \"system\" as const,\r\n          timestamp: new Date(),\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  private sendMessage(ws: WebSocket, message: WSMessage) {\r\n    if (ws.readyState === WebSocket.OPEN) {\r\n      ws.send(JSON.stringify(message));\r\n    }\r\n  }\r\n\r\n  public broadcast(message: WSMessage) {\r\n    // Store the data for new connections\r\n    if (message.type === \"progress\") {\r\n      this.recentProgress.set(message.data.id, message.data);\r\n\r\n      // Clean up completed tasks after 5 minutes\r\n      if (message.data.status === \"completed\") {\r\n        setTimeout(\r\n          () => {\r\n            this.recentProgress.delete(message.data.id);\r\n          },\r\n          5 * 60 * 1000,\r\n        );\r\n      }\r\n    } else if (message.type === \"event\") {\r\n      this.recentEvents.push(message.data);\r\n\r\n      // Keep only last 50 events\r\n      if (this.recentEvents.length > 50) {\r\n        this.recentEvents = this.recentEvents.slice(-50);\r\n      }\r\n    }\r\n\r\n    // Broadcast to all connected clients\r\n    this.clients.forEach((ws) => {\r\n      this.sendMessage(ws, message);\r\n    });\r\n  }\r\n\r\n  public getConnectedClientsCount(): number {\r\n    return this.clients.size;\r\n  }\r\n\r\n  public getRecentProgress(): any[] {\r\n    return Array.from(this.recentProgress.values());\r\n  }\r\n\r\n  public getRecentEvents(): any[] {\r\n    return this.recentEvents.slice(-20);\r\n  }\r\n\r\n  public clearProgress(progressId: string): boolean {\r\n    return this.recentProgress.delete(progressId);\r\n  }\r\n\r\n  public clearAllProgress(): void {\r\n    this.recentProgress.clear();\r\n  }\r\n\r\n  public clearEvents(): void {\r\n    this.recentEvents = [];\r\n  }\r\n\r\n  private startMinimalDemoData() {\r\n    // Only generate minimal demo data if no external data is received\r\n    let lastExternalDataTime = Date.now();\r\n\r\n    // Check for external data activity\r\n    const originalBroadcast = this.broadcast.bind(this);\r\n    this.broadcast = (message: WSMessage) => {\r\n      lastExternalDataTime = Date.now();\r\n      originalBroadcast(message);\r\n    };\r\n\r\n    // Generate minimal demo data only if no external data for 30 seconds\r\n    const demoInterval = setInterval(() => {\r\n      const timeSinceLastExternal = Date.now() - lastExternalDataTime;\r\n\r\n      // Only send demo data if no external data received recently and no active progress\r\n      if (timeSinceLastExternal > 30000 && this.recentProgress.size === 0) {\r\n        const demoEvent: EventUpdate = {\r\n          type: \"event\",\r\n          data: {\r\n            id: `demo-${Date.now()}`,\r\n            title: \"Waiting for external data\",\r\n            message:\r\n              \"No external applications are currently sending data. Send POST requests to /api/progress or /api/events to see real-time updates.\",\r\n            type: \"info\" as const,\r\n            category: \"system\" as const,\r\n            timestamp: new Date(),\r\n          },\r\n        };\r\n\r\n        // Use original broadcast to avoid updating lastExternalDataTime\r\n        originalBroadcast(demoEvent);\r\n      }\r\n    }, 60000); // Check every minute\r\n\r\n    // Clean up interval when needed\r\n    process.on(\"SIGTERM\", () => {\r\n      clearInterval(demoInterval);\r\n    });\r\n  }\r\n}\r\n", "import \"dotenv/config\";\r\nimport express from \"express\";\r\nimport cors from \"cors\";\r\nimport { createServer as createHttpServer } from \"http\";\r\nimport { handleDemo } from \"./routes/demo\";\r\nimport {\r\n  handleProgressUpdate,\r\n  handleEventUpdate,\r\n  handleBulkUpdate,\r\n  handleWebSocketHealth,\r\n  setWebSocketManager as setWSManager,\r\n} from \"./routes/websocket-data\";\r\nimport {\r\n  authenticateApiKey,\r\n  rateLimitByApiKey,\r\n  logApiUsage,\r\n} from \"./middleware/auth\";\r\nimport {\r\n  createSchedule,\r\n  getSchedules,\r\n  getSchedule,\r\n  updateSchedule,\r\n  deleteSchedule,\r\n  toggleSchedule,\r\n  executeSchedule,\r\n} from \"./routes/schedules\";\r\n\r\n// Re-export for external use\r\nexport { setWSManager as setWebSocketManager };\r\nimport { WebSocketManager } from \"./websocket\";\r\n\r\nexport function createServer() {\r\n  const app = express();\r\n  const httpServer = createHttpServer(app);\r\n\r\n  // Middleware\r\n  app.use(cors());\r\n  app.use(express.json({ limit: \"10mb\" })); // Increased limit for bulk updates\r\n  app.use(express.urlencoded({ extended: true }));\r\n\r\n  // Example API routes\r\n  app.get(\"/api/ping\", (_req, res) => {\r\n    const ping = process.env.PING_MESSAGE ?? \"ping\";\r\n    res.json({ message: ping });\r\n  });\r\n\r\n  app.get(\"/api/demo\", handleDemo);\r\n\r\n  // Schedule management routes\r\n  app.post(\"/api/schedules\", createSchedule);\r\n  app.get(\"/api/schedules\", getSchedules);\r\n  app.get(\"/api/schedules/:id\", getSchedule);\r\n  app.put(\"/api/schedules/:id\", updateSchedule);\r\n  app.delete(\"/api/schedules/:id\", deleteSchedule);\r\n  app.patch(\"/api/schedules/:id/toggle\", toggleSchedule);\r\n  app.post(\"/api/schedules/:id/execute\", executeSchedule);\r\n\r\n  // WebSocket data routes for external applications (with authentication)\r\n  app.post(\r\n    \"/api/progress\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleProgressUpdate,\r\n  );\r\n  app.post(\r\n    \"/api/events\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleEventUpdate,\r\n  );\r\n  app.post(\r\n    \"/api/bulk\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleBulkUpdate,\r\n  );\r\n  app.get(\"/api/ws-health\", handleWebSocketHealth); // Public health check\r\n\r\n  // Initialize WebSocket and link it to the routes\r\n  try {\r\n    console.log(\"🚀 Initializing WebSocket manager...\");\r\n    const wsManager = new WebSocketManager(httpServer);\r\n    setWSManager(wsManager);\r\n    console.log(\"✅ WebSocket manager initialized successfully\");\r\n  } catch (error) {\r\n    console.error(\"💀 FATAL: Failed to initialize WebSocket manager:\", error);\r\n    // Still set a null manager to prevent undefined errors\r\n    setWSManager(null);\r\n  }\r\n\r\n  return httpServer;\r\n}\r\n\r\nexport function createExpressApp() {\r\n  const app = express();\r\n\r\n  // Middleware\r\n  app.use(cors());\r\n  app.use(express.json({ limit: \"10mb\" }));\r\n  app.use(express.urlencoded({ extended: true }));\r\n\r\n  // Example API routes\r\n  app.get(\"/ping\", (_req, res) => {\r\n    const ping = process.env.PING_MESSAGE ?? \"ping\";\r\n    res.json({ message: ping });\r\n  });\r\n\r\n  app.get(\"/demo\", handleDemo);\r\n\r\n  // Schedule management routes\r\n  app.post(\"/schedules\", createSchedule);\r\n  app.get(\"/schedules\", getSchedules);\r\n  app.get(\"/schedules/:id\", getSchedule);\r\n  app.put(\"/schedules/:id\", updateSchedule);\r\n  app.delete(\"/schedules/:id\", deleteSchedule);\r\n  app.patch(\"/schedules/:id/toggle\", toggleSchedule);\r\n  app.post(\"/schedules/:id/execute\", executeSchedule);\r\n\r\n  // WebSocket data routes for external applications (with authentication)\r\n  app.post(\r\n    \"/progress\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleProgressUpdate,\r\n  );\r\n  app.post(\r\n    \"/events\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleEventUpdate,\r\n  );\r\n  app.post(\r\n    \"/bulk\",\r\n    authenticateApiKey,\r\n    rateLimitByApiKey,\r\n    logApiUsage,\r\n    handleBulkUpdate,\r\n  );\r\n  app.get(\"/ws-health\", handleWebSocketHealth); // Public health check\r\n\r\n  return app;\r\n}\r\n", "import path from \"path\";\nimport express from \"express\";\nimport { createServer } from \"./index\";\nimport { WebSocketManager } from \"./websocket\";\n\nconst httpServer = createServer();\nconst port = process.env.PORT || 3000;\n\n// Initialize WebSocket\nnew WebSocketManager(httpServer);\n\n// In production, serve the built SPA files\nconst __dirname = import.meta.dirname;\nconst distPath = path.join(__dirname, \"../spa\");\n\n// Create Express app for static file serving\nconst app = express();\n\n// Serve static files\napp.use(express.static(distPath));\n\n// Handle React Router - serve index.html for all non-API routes\napp.get(\"*\", (req, res) => {\n  // Don't serve index.html for API routes or WebSocket\n  if (req.path.startsWith(\"/api/\") || req.path.startsWith(\"/health\") || req.path.startsWith(\"/ws\")) {\n    return res.status(404).json({ error: \"Endpoint not found\" });\n  }\n\n  res.sendFile(path.join(distPath, \"index.html\"));\n});\n\n// Add the static file serving to the HTTP server\nhttpServer.on('request', (req, res) => {\n  // First check if it's handled by the main server (API routes)\n  // If not, pass to the static file server\n  const originalListeners = httpServer.listeners('request');\n  let handled = false;\n\n  // Try the main server first\n  for (const listener of originalListeners) {\n    if (listener !== arguments.callee) {\n      try {\n        (listener as any)(req, res);\n        if (res.headersSent) {\n          handled = true;\n          break;\n        }\n      } catch (e) {\n        // Continue to next listener\n      }\n    }\n  }\n\n  // If not handled by main server, try static files\n  if (!handled && !res.headersSent) {\n    app(req, res);\n  }\n});\n\nhttpServer.listen(port, () => {\n  console.log(`🚀 Fusion Starter server running on port ${port}`);\n  console.log(`📱 Frontend: http://localhost:${port}`);\n  console.log(`🔧 API: http://localhost:${port}/api`);\n  console.log(`🔌 WebSocket: ws://localhost:${port}/ws`);\n});\n\n// Graceful shutdown\nprocess.on(\"SIGTERM\", () => {\n  console.log(\"🛑 Received SIGTERM, shutting down gracefully\");\n  process.exit(0);\n});\n\nprocess.on(\"SIGINT\", () => {\n  console.log(\"🛑 Received SIGINT, shutting down gracefully\");\n  process.exit(0);\n});\n"], "names": ["app", "httpServer", "createHttpServer", "wsManager", "setWSManager"], "mappings": ";;;;;;;AAGa,MAAA,aAA6B,CAAC,KAAK,QAAQ;AACtD,QAAM,WAAyB;AAAA,IAC7B,SAAS;AAAA,EACX;AACA,MAAI,OAAO,GAAG,EAAE,KAAK,QAAQ;AAC/B;ACJA,MAAM,uBAAuB,EAAE,OAAO;AAAA,EACpC,IAAI,EAAE,OAAO;AAAA,EACb,OAAO,EAAE,OAAO;AAAA,EAChB,MAAM,EAAE,KAAK,CAAC,QAAQ,UAAU,UAAU,CAAC;AAAA,EAC3C,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,GAAG;AAAA,EACnC,QAAQ,EAAE,KAAK,CAAC,WAAW,aAAa,UAAU,QAAQ,CAAC;AAAA,EAC3D,WAAW,EAAE,OAAS,EAAA,SAAA,EAAW,SAAS;AAAA,EAC1C,qBAAqB,EAAE,OAAS,EAAA,SAAA,EAAW,SAAS;AAAA,EACpD,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,EAChC,gBAAgB,EAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAED,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACjC,IAAI,EAAE,OAAO;AAAA,EACb,OAAO,EAAE,OAAO;AAAA,EAChB,SAAS,EAAE,OAAO;AAAA,EAClB,MAAM,EAAE,KAAK,CAAC,WAAW,SAAS,WAAW,MAAM,CAAC;AAAA,EACpD,UAAU,EAAE,KAAK,CAAC,QAAQ,UAAU,YAAY,UAAU,MAAM,CAAC;AAAA,EACjE,WAAW,EAAE,OAAS,EAAA,SAAA,EAAW,SAAS;AAAA,EAC1C,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,EAChC,SAAS,EAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAGD,IAAI,YAAiB;AAEd,SAAS,oBAAoB,SAAc;AAChD,UAAQ,IAAI,qCAAqC,CAAC,CAAC,OAAO;AAC9C,cAAA;AACd;AAMa,MAAA,uBAAuC,CAAC,KAAK,QAAQ;AAC5D,MAAA;AACF,UAAM,eAAe,qBAAqB,MAAM,IAAI,IAAI;AAGxD,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,WAAW,aAAa,YACpB,IAAI,KAAK,aAAa,SAAS,IAC/B,oBAAI,KAAK;AAAA,MACb,qBAAqB,aAAa,sBAC9B,IAAI,KAAK,aAAa,mBAAmB,IACzC;AAAA,IACN;AAGA,QAAI,WAAW;AACb,gBAAU,UAAU;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,MAAA,CACP;AAEO,cAAA;AAAA,QACN,mCAAmC,aAAa,KAAK,MAAM,aAAa,QAAQ;AAAA,MAClF;AACA,UAAI,KAAK,EAAE,SAAS,MAAM,SAAS,+BAA+B;AAAA,IAAA,OAC7D;AACL,cAAQ,KAAK,sCAAsC;AACnD,UAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,mCAAmC;AAAA,IAAA;AAAA,WAE5D,OAAO;AACN,YAAA,MAAM,4BAA4B,KAAK;AAC3C,QAAA,OAAO,GAAG,EAAE,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,SAAS,iBAAiB,EAAE,WAAW,MAAM,SAAS;AAAA,IAAA,CACvD;AAAA,EAAA;AAEL;AAEa,MAAA,oBAAoC,CAAC,KAAK,QAAQ;AACzD,MAAA;AACF,UAAM,YAAY,kBAAkB,MAAM,IAAI,IAAI;AAGlD,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,WAAW,UAAU,YACjB,IAAI,KAAK,UAAU,SAAS,IAC5B,oBAAI,KAAK;AAAA,IACf;AAGA,QAAI,WAAW;AACb,gBAAU,UAAU;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,MAAA,CACP;AAEO,cAAA;AAAA,QACN,yBAAyB,UAAU,KAAK,MAAM,UAAU,IAAI;AAAA,MAC9D;AACA,UAAI,KAAK,EAAE,SAAS,MAAM,SAAS,qBAAqB;AAAA,IAAA,OACnD;AACL,cAAQ,KAAK,sCAAsC;AACnD,UAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,mCAAmC;AAAA,IAAA;AAAA,WAE5D,OAAO;AACN,YAAA,MAAM,yBAAyB,KAAK;AACxC,QAAA,OAAO,GAAG,EAAE,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,SAAS,iBAAiB,EAAE,WAAW,MAAM,SAAS;AAAA,IAAA,CACvD;AAAA,EAAA;AAEL;AAEa,MAAA,mBAAmC,CAAC,KAAK,QAAQ;AACxD,MAAA;AACI,UAAA,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC,EAAA,IAAM,IAAI;AAE3C,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,UAAM,SAAgB,CAAC;AAGd,aAAA,QAAQ,CAAC,MAAW,UAAkB;AACzC,UAAA;AACI,cAAA,eAAe,qBAAqB,MAAM,IAAI;AACpD,cAAM,gBAAgB;AAAA,UACpB,GAAG;AAAA,UACH,WAAW,aAAa,YACpB,IAAI,KAAK,aAAa,SAAS,IAC/B,oBAAI,KAAK;AAAA,UACb,qBAAqB,aAAa,sBAC9B,IAAI,KAAK,aAAa,mBAAmB,IACzC;AAAA,QACN;AAEA,YAAI,WAAW;AACb,oBAAU,UAAU;AAAA,YAClB,MAAM;AAAA,YACN,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QAAA;AAAA,eAEK,OAAO;AACd;AACA,eAAO,KAAK;AAAA,UACV,MAAM;AAAA,UACN;AAAA,UACA,OAAO,iBAAiB,EAAE,WAAW,MAAM,SAAS;AAAA,QAAA,CACrD;AAAA,MAAA;AAAA,IACH,CACD;AAGM,WAAA,QAAQ,CAAC,MAAW,UAAkB;AACvC,UAAA;AACI,cAAA,YAAY,kBAAkB,MAAM,IAAI;AAC9C,cAAM,gBAAgB;AAAA,UACpB,GAAG;AAAA,UACH,WAAW,UAAU,YACjB,IAAI,KAAK,UAAU,SAAS,IAC5B,oBAAI,KAAK;AAAA,QACf;AAEA,YAAI,WAAW;AACb,oBAAU,UAAU;AAAA,YAClB,MAAM;AAAA,YACN,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QAAA;AAAA,eAEK,OAAO;AACd;AACA,eAAO,KAAK;AAAA,UACV,MAAM;AAAA,UACN;AAAA,UACA,OAAO,iBAAiB,EAAE,WAAW,MAAM,SAAS;AAAA,QAAA,CACrD;AAAA,MAAA;AAAA,IACH,CACD;AAEO,YAAA;AAAA,MACN,mBAAmB,YAAY,gBAAgB,UAAU;AAAA,IAC3D;AAEA,QAAI,KAAK;AAAA,MACP,SAAS,eAAe;AAAA,MACxB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,cAAc,OAAO,SAAS,IAAI,SAAS;AAAA,IAAA,CAC5C;AAAA,WACM,OAAO;AACN,YAAA,MAAM,wBAAwB,KAAK;AAC3C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,4BAA4B;AAAA,EAAA;AAE9D;AAGa,MAAA,wBAAwC,CAAC,MAAM,QAAQ;AAC9D,MAAA;AACI,UAAA,gBAAgB;AAChB,UAAA,kBAAkB,CAAC,CAAC;AAE1B,QAAI,mBAAmB;AACvB,QAAI,YAAY;AAEhB,QAAI,iBAAiB;AACf,UAAA;AACF,2BAAmB,UAAU,yBAAyB;AAC1C,oBAAA;AACJ,gBAAA;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,eACO,OAAO;AACN,gBAAA,MAAM,4CAA4C,KAAK;AACnD,oBAAA;AAAA,MAAA;AAAA,IACd,OACK;AACL,cAAQ,KAAK,qDAAqD;AAAA,IAAA;AAGpE,UAAM,WAAW;AAAA,MACf,QAAQ,YAAY,YAAY;AAAA,MAChC,aAAa,gBAAgB,gBAAgB;AAAA,MAC7C;AAAA,MACA,oBAAoB;AAAA,MACpB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,SAAS,kBACL,gCACA;AAAA,IACN;AAEA,QAAI,KAAK,QAAQ;AAAA,WACV,OAAO;AACN,YAAA,MAAM,6CAA6C,KAAK;AAC5D,QAAA,OAAO,GAAG,EAAE,KAAK;AAAA,MACnB,QAAQ;AAAA,MACR,aAC2D;AAAA,MAC3D,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,OAAO,MAAM;AAAA,IAAA,CACd;AAAA,EAAA;AAEL;ACnPA,MAAM,qCAAqB,IAAI;AAAA,EAC7B,QAAQ,IAAI,WAAW;AAAA,EACvB;AAAA;AAEF,CAAC;AAEM,MAAM,qBAAqC,CAAC,KAAK,KAAK,SAAS;AAC9D,QAAA,SAAS,IAAI,QAAQ,WAAW,KAAK,IAAI,QAAQ,eAAe,GAAG,QAAQ,WAAW,EAAE;AAE9F,MAAI,CAAC,QAAQ;AACX,WAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,MAC1B,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAGH,MAAI,CAAC,eAAe,IAAI,MAAgB,GAAG;AACzC,WAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,MAC1B,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAIF,MAAY,SAAS;AACjB,OAAA;AACP;AAGA,MAAM,oCAAoB,IAAkD;AAC5E,MAAM,aAAa;AACnB,MAAM,cAAc,KAAK;AAElB,MAAM,oBAAoC,CAAC,KAAK,KAAK,SAAS;AAC7D,QAAA,SAAU,IAAY,UAAU;AAChC,QAAA,MAAM,KAAK,IAAI;AAEf,QAAA,UAAU,cAAc,IAAI,MAAM;AAExC,MAAI,CAAC,WAAW,MAAM,QAAQ,WAAW;AAEzB,kBAAA,IAAI,QAAQ,EAAE,OAAO,GAAG,WAAW,MAAM,aAAa;AAC/D,SAAA;AAAA,EAAA,WACI,QAAQ,QAAQ,YAAY;AAE7B,YAAA;AACH,SAAA;AAAA,EAAA,OACA;AAED,QAAA,OAAO,GAAG,EAAE,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY;AAAA,IAAA,CACpD;AAAA,EAAA;AAEL;AAGO,MAAM,cAA8B,CAAC,KAAK,KAAK,SAAS;AACvD,QAAA,SAAU,IAAY,UAAU;AACtC,QAAM,aAAY,oBAAI,KAAK,GAAE,YAAY;AAEjC,UAAA,IAAI,mBAAmB,IAAI,MAAM,IAAI,IAAI,IAAI,SAAS,MAAM,OAAO,SAAS,EAAE;AAEjF,OAAA;AACP;AChEA,MAAM,uBAAuB,EAAE,OAAO;AAAA,EACpC,MAAM,EAAE,OAAA,EAAS,IAAI,GAAG,2BAA2B;AAAA,EACnD,cAAc,EAAE,OAAA,EAAS,IAAI,GAAG,2BAA2B;AAAA,EAC3D,mBAAmB,EAAE,OAAA,EAAS,IAAI,GAAG,gCAAgC;AAAA,EACrE,UAAU,EAAE,OAAA,EAAS,IAAI,GAAG,uBAAuB;AAAA,EACnD,UAAU,EAAE,KAAK,CAAC,SAAS,UAAU,SAAS,CAAC;AAAA,EAC/C,MAAM,EACH,OAAA,EACA,MAAM,qCAAqC,qBAAqB;AAAA,EACnE,WAAW,EAAE,OAAA,EAAS,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EAC7C,YAAY,EAAE,OAAA,EAAS,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,EAC/C,UAAU,EAAE,QAAQ;AAAA,EACpB,YAAY,EAAE,MAAM,EAAE,QAAQ;AAAA,EAC9B,aAAa,EAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAwBD,IAAI,YAAwB,CAAC;AAG7B,SAAS,kBAAkB,UAAoC;AACvD,QAAA,0BAAU,KAAK;AACf,QAAA,CAAC,OAAO,OAAO,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM;AAExD,MAAA,+BAAe,KAAK;AACxB,WAAS,SAAS,OAAO,SAAS,GAAG,CAAC;AAGtC,MAAI,YAAY,KAAK;AACnB,YAAQ,SAAS,UAAU;AAAA,MACzB,KAAK;AACH,iBAAS,QAAQ,SAAS,QAAQ,IAAI,CAAC;AACvC;AAAA,MACF,KAAK;AACH,iBAAS,QAAQ,SAAS,QAAQ,IAAI,CAAC;AACvC;AAAA,MACF,KAAK;AACH,iBAAS,SAAS,SAAS,SAAS,IAAI,CAAC;AACzC;AAAA,IAAA;AAAA,EACJ;AAIF,MAAI,SAAS,aAAa,YAAY,SAAS,cAAc,QAAW;AACtE,UAAM,YAAY,SAAS,YAAY,SAAS,OAAA,IAAW,KAAK;AAC5D,QAAA,aAAa,KAAK,YAAY,KAAK;AACrC,eAAS,QAAQ,SAAS,QAAQ,IAAI,CAAC;AAAA,IAAA,OAClC;AACL,eAAS,QAAQ,SAAS,QAAQ,IAAI,QAAQ;AAAA,IAAA;AAAA,EAChD;AAIF,MAAI,SAAS,aAAa,aAAa,SAAS,eAAe,QAAW;AAC/D,aAAA,QAAQ,SAAS,UAAU;AACpC,QAAI,YAAY,KAAK;AACnB,eAAS,SAAS,SAAS,SAAS,IAAI,CAAC;AAChC,eAAA,QAAQ,SAAS,UAAU;AAAA,IAAA;AAAA,EACtC;AAGK,SAAA;AACT;AAGa,MAAA,iBAAiC,CAAC,KAAK,QAAQ;AACtD,MAAA;AACF,UAAM,gBAAgB,qBAAqB,MAAM,IAAI,IAAI;AAGzD,QACE,cAAc,aAAa,YAC3B,cAAc,cAAc,QAC5B;AACA,aAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,QAC1B,OAAO;AAAA,MAAA,CACR;AAAA,IAAA;AAGH,QACE,cAAc,aAAa,aAC3B,cAAc,eAAe,QAC7B;AACA,aAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,QAC1B,OAAO;AAAA,MAAA,CACR;AAAA,IAAA;AAGH,UAAM,WAAqB;AAAA,MACzB,GAAG;AAAA,MACH,IAAI,YAAY,KAAK,IAAK,CAAA,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAAA,MACrE,+BAAe,KAAK;AAAA,MACpB,+BAAe,KAAK;AAAA,MACpB,UAAU,kBAAkB,aAAa;AAAA,MACzC,QAAQ,cAAc,WAAW,YAAY;AAAA,MAC7C,aAAa,CAAA;AAAA,IACf;AAEA,cAAU,KAAK,QAAQ;AAEvB,YAAQ,IAAI,yBAAyB,SAAS,IAAI,KAAK,SAAS,EAAE,GAAG;AAEjE,QAAA,OAAO,GAAG,EAAE,KAAK;AAAA,MACnB,SAAS;AAAA,MACT;AAAA,IAAA,CACD;AAAA,WACM,OAAO;AACV,QAAA,iBAAiB,EAAE,UAAU;AAC/B,aAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,QAC1B,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,MAAA,CAChB;AAAA,IAAA;AAGK,YAAA,MAAM,4BAA4B,KAAK;AAC/C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,eAA+B,CAAC,KAAK,QAAQ;AACpD,MAAA;AACF,QAAI,KAAK;AAAA,MACP,WAAW,UAAU;AAAA,QACnB,CAAC,GAAG,MAAM,EAAE,UAAU,YAAY,EAAE,UAAU,QAAQ;AAAA,MACxD;AAAA,MACA,OAAO,UAAU;AAAA,IAAA,CAClB;AAAA,WACM,OAAO;AACN,YAAA,MAAM,6BAA6B,KAAK;AAChD,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,cAA8B,CAAC,KAAK,QAAQ;AACnD,MAAA;AACI,UAAA,EAAE,OAAO,IAAI;AACnB,UAAM,WAAW,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAElD,QAAI,CAAC,UAAU;AACN,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,IAAA;AAGzD,QAAA,KAAK,EAAE,UAAU;AAAA,WACd,OAAO;AACN,YAAA,MAAM,4BAA4B,KAAK;AAC/C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,iBAAiC,CAAC,KAAK,QAAQ;AACtD,MAAA;AACI,UAAA,EAAE,OAAO,IAAI;AACnB,UAAM,gBAAgB,UAAU,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AAE5D,QAAI,kBAAkB,IAAI;AACjB,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,IAAA;AAG7D,UAAM,gBAAgB,qBAAqB,QAAU,EAAA,MAAM,IAAI,IAAI;AAC7D,UAAA,kBAAkB,UAAU,aAAa;AAE/C,UAAM,kBAA4B;AAAA,MAChC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,+BAAe,KAAK;AAAA,IACtB;AAGA,QACE,cAAc,YACd,cAAc,QACd,cAAc,aACd,cAAc,YACd;AACgB,sBAAA,WAAW,kBAAkB,eAAe;AAAA,IAAA;AAG9D,cAAU,aAAa,IAAI;AAEnB,YAAA;AAAA,MACN,qBAAqB,gBAAgB,IAAI,KAAK,gBAAgB,EAAE;AAAA,IAClE;AAEA,QAAI,KAAK;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,IAAA,CACX;AAAA,WACM,OAAO;AACV,QAAA,iBAAiB,EAAE,UAAU;AAC/B,aAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,QAC1B,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,MAAA,CAChB;AAAA,IAAA;AAGK,YAAA,MAAM,4BAA4B,KAAK;AAC/C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,iBAAiC,CAAC,KAAK,QAAQ;AACtD,MAAA;AACI,UAAA,EAAE,OAAO,IAAI;AACnB,UAAM,gBAAgB,UAAU,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AAE5D,QAAI,kBAAkB,IAAI;AACjB,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,IAAA;AAG7D,UAAM,kBAAkB,UAAU,OAAO,eAAe,CAAC,EAAE,CAAC;AAEpD,YAAA;AAAA,MACN,qBAAqB,gBAAgB,IAAI,KAAK,gBAAgB,EAAE;AAAA,IAClE;AAEA,QAAI,KAAK;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,IAAA,CACX;AAAA,WACM,OAAO;AACN,YAAA,MAAM,4BAA4B,KAAK;AAC/C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,iBAAiC,CAAC,KAAK,QAAQ;AACtD,MAAA;AACI,UAAA,EAAE,OAAO,IAAI;AACnB,UAAM,gBAAgB,UAAU,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AAE5D,QAAI,kBAAkB,IAAI;AACjB,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,IAAA;AAGvD,UAAA,WAAW,UAAU,aAAa;AAC/B,aAAA,WAAW,CAAC,SAAS;AACrB,aAAA,SAAS,SAAS,WAAW,YAAY;AACzC,aAAA,gCAAgB,KAAK;AAE9B,QAAI,SAAS,UAAU;AACZ,eAAA,WAAW,kBAAkB,QAAQ;AAAA,IAAA;AAGxC,YAAA;AAAA,MACN,qBAAqB,SAAS,IAAI,KAAK,SAAS,EAAE,OAAO,SAAS,WAAW,WAAW,QAAQ;AAAA,IAClG;AAEA,QAAI,KAAK;AAAA,MACP,SAAS,YAAY,SAAS,WAAW,YAAY,QAAQ;AAAA,MAC7D;AAAA,IAAA,CACD;AAAA,WACM,OAAO;AACN,YAAA,MAAM,4BAA4B,KAAK;AAC/C,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AAGa,MAAA,kBAAkC,CAAC,KAAK,QAAQ;AACvD,MAAA;AACI,UAAA,EAAE,OAAO,IAAI;AACnB,UAAM,gBAAgB,UAAU,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AAE5D,QAAI,kBAAkB,IAAI;AACjB,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,IAAA;AAGvD,UAAA,WAAW,UAAU,aAAa;AAEpC,QAAA,SAAS,WAAW,WAAW;AAC1B,aAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,+BAA+B;AAAA,IAAA;AAItE,aAAS,SAAS;AAClB,aAAS,WAAW;AACX,aAAA,gCAAgB,KAAK;AACrB,aAAA,+BAAe,KAAK;AAErB,YAAA;AAAA,MACN,4CAA4C,SAAS,IAAI,KAAK,SAAS,EAAE;AAAA,IAC3E;AAGA,eAAW,MAAM;AACX,UAAA,UAAU,aAAa,GAAG;AAClB,kBAAA,aAAa,EAAE,SAAS;AACxB,kBAAA,aAAa,EAAE,WAAW;AACpC,kBAAU,aAAa,EAAE,WAAW,kBAAkB,QAAQ;AACpD,kBAAA,aAAa,EAAE,YAAY,QAAQ;AAAA,UAC3C,WAAW,SAAS;AAAA,UACpB,6BAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,UACR,aAAa,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,IAAI;AAAA,UAC/C,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,IAAI;AAAA,QAAA,CACnD;AAAA,MAAA;AAAA,OAEF,GAAI;AAEP,QAAI,KAAK;AAAA,MACP,SAAS;AAAA,MACT;AAAA,IAAA,CACD;AAAA,WACM,OAAO;AACN,YAAA,MAAM,6BAA6B,KAAK;AAChD,QAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,yBAAyB;AAAA,EAAA;AAE3D;AC9SO,MAAM,iBAAiB;AAAA,EACpB;AAAA,EACA,8BAA8B,IAAI;AAAA,EAClC,qCAAuC,IAAI;AAAA,EAC3C,eAAsB,CAAC;AAAA,EAE/B,YAAY,QAAgB;AAC1B,YAAQ,IAAI,8CAA8C,CAAC,CAAC,MAAM;AAE9D,QAAA;AACG,WAAA,MAAM,IAAI,gBAAgB;AAAA,QAC7B,UAAU;AAAA;AAAA,MAAA,CACX;AACD,cAAQ,IAAI,wCAAwC;AAGpD,aAAO,GAAG,WAAW,CAAC,SAAS,QAAQ,SAAS;AACtC,gBAAA,IAAI,qCAAqC,QAAQ,GAAG;AACxD,YAAA,QAAQ,QAAQ,OAAO;AACzB,eAAK,IAAI,cAAc,SAAS,QAAQ,MAAM,CAAC,OAAO;AACpD,iBAAK,IAAI,KAAK,cAAc,IAAI,OAAO;AAAA,UAAA,CACxC;AAAA,QAAA;AAAA,MACH,CACD;AACD,cAAQ,IAAI,wCAAwC;AAEpD,WAAK,eAAe;AACpB,WAAK,qBAAqB;AAC1B,cAAQ,IAAI,uCAAuC;AAAA,aAC5C,OAAO;AACN,cAAA,MAAM,8CAA8C,KAAK;AAC3D,YAAA;AAAA,IAAA;AAAA,EACR;AAAA,EAGM,iBAAiB;AACvB,SAAK,IAAI,GAAG,cAAc,CAAC,OAAkB;AAC3C,cAAQ,IAAI,4BAA4B;AACnC,WAAA,QAAQ,IAAI,EAAE;AAGnB,WAAK,gBAAgB,EAAE;AAEpB,SAAA,GAAG,SAAS,MAAM;AACnB,gBAAQ,IAAI,+BAA+B;AACtC,aAAA,QAAQ,OAAO,EAAE;AAAA,MAAA,CACvB;AAEE,SAAA,GAAG,SAAS,CAAC,UAAU;AAChB,gBAAA,MAAM,oBAAoB,KAAK;AAClC,aAAA,QAAQ,OAAO,EAAE;AAAA,MAAA,CACvB;AAAA,IAAA,CACF;AAAA,EAAA;AAAA,EAGK,gBAAgB,IAAe;AAEhC,SAAA,eAAe,QAAQ,CAAC,aAAa;AACxC,WAAK,YAAY,IAAI,EAAE,MAAM,YAAY,MAAM,UAAU;AAAA,IAAA,CAC1D;AAGD,SAAK,aAAa,MAAM,GAAG,EAAE,QAAQ,CAAC,UAAU;AAC9C,WAAK,YAAY,IAAI,EAAE,MAAM,SAAS,MAAM,OAAO;AAAA,IAAA,CACpD;AAGD,QAAI,KAAK,eAAe,SAAS,KAAK,KAAK,aAAa,WAAW,GAAG;AACpE,WAAK,YAAY,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU;AAAA,UACV,+BAAe,KAAK;AAAA,QAAA;AAAA,MACtB,CACD;AAAA,IAAA;AAAA,EACH;AAAA,EAGM,YAAY,IAAe,SAAoB;AACjD,QAAA,GAAG,eAAe,UAAU,MAAM;AACpC,SAAG,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,IAAA;AAAA,EACjC;AAAA,EAGK,UAAU,SAAoB;AAE/B,QAAA,QAAQ,SAAS,YAAY;AAC/B,WAAK,eAAe,IAAI,QAAQ,KAAK,IAAI,QAAQ,IAAI;AAGjD,UAAA,QAAQ,KAAK,WAAW,aAAa;AACvC;AAAA,UACE,MAAM;AACJ,iBAAK,eAAe,OAAO,QAAQ,KAAK,EAAE;AAAA,UAC5C;AAAA,UACA,IAAI,KAAK;AAAA,QACX;AAAA,MAAA;AAAA,IACF,WACS,QAAQ,SAAS,SAAS;AAC9B,WAAA,aAAa,KAAK,QAAQ,IAAI;AAG/B,UAAA,KAAK,aAAa,SAAS,IAAI;AACjC,aAAK,eAAe,KAAK,aAAa,MAAM,GAAG;AAAA,MAAA;AAAA,IACjD;AAIG,SAAA,QAAQ,QAAQ,CAAC,OAAO;AACtB,WAAA,YAAY,IAAI,OAAO;AAAA,IAAA,CAC7B;AAAA,EAAA;AAAA,EAGI,2BAAmC;AACxC,WAAO,KAAK,QAAQ;AAAA,EAAA;AAAA,EAGf,oBAA2B;AAChC,WAAO,MAAM,KAAK,KAAK,eAAe,QAAQ;AAAA,EAAA;AAAA,EAGzC,kBAAyB;AACvB,WAAA,KAAK,aAAa,MAAM,GAAG;AAAA,EAAA;AAAA,EAG7B,cAAc,YAA6B;AACzC,WAAA,KAAK,eAAe,OAAO,UAAU;AAAA,EAAA;AAAA,EAGvC,mBAAyB;AAC9B,SAAK,eAAe,MAAM;AAAA,EAAA;AAAA,EAGrB,cAAoB;AACzB,SAAK,eAAe,CAAC;AAAA,EAAA;AAAA,EAGf,uBAAuB;AAEzB,QAAA,uBAAuB,KAAK,IAAI;AAGpC,UAAM,oBAAoB,KAAK,UAAU,KAAK,IAAI;AAC7C,SAAA,YAAY,CAAC,YAAuB;AACvC,6BAAuB,KAAK,IAAI;AAChC,wBAAkB,OAAO;AAAA,IAC3B;AAGM,UAAA,eAAe,YAAY,MAAM;AAC/B,YAAA,wBAAwB,KAAK,IAAA,IAAQ;AAG3C,UAAI,wBAAwB,OAAS,KAAK,eAAe,SAAS,GAAG;AACnE,cAAM,YAAyB;AAAA,UAC7B,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,IAAI,QAAQ,KAAK,IAAK,CAAA;AAAA,YACtB,OAAO;AAAA,YACP,SACE;AAAA,YACF,MAAM;AAAA,YACN,UAAU;AAAA,YACV,+BAAe,KAAK;AAAA,UAAA;AAAA,QAExB;AAGA,0BAAkB,SAAS;AAAA,MAAA;AAAA,OAE5B,GAAK;AAGA,YAAA,GAAG,WAAW,MAAM;AAC1B,oBAAc,YAAY;AAAA,IAAA,CAC3B;AAAA,EAAA;AAEL;ACzLO,SAAS,eAAe;AAC7B,QAAMA,OAAM,QAAQ;AACd,QAAAC,cAAaC,eAAiBF,IAAG;AAGnC,EAAAA,KAAA,IAAI,MAAM;AACd,EAAAA,KAAI,IAAI,QAAQ,KAAK,EAAE,OAAO,OAAA,CAAQ,CAAC;AACvC,EAAAA,KAAI,IAAI,QAAQ,WAAW,EAAE,UAAU,KAAA,CAAM,CAAC;AAG9C,EAAAA,KAAI,IAAI,aAAa,CAAC,MAAM,QAAQ;AAC5B,UAAA,OAAO,QAAQ,IAAI,gBAAgB;AACzC,QAAI,KAAK,EAAE,SAAS,KAAA,CAAM;AAAA,EAAA,CAC3B;AAEG,EAAAA,KAAA,IAAI,aAAa,UAAU;AAG3B,EAAAA,KAAA,KAAK,kBAAkB,cAAc;AACrC,EAAAA,KAAA,IAAI,kBAAkB,YAAY;AAClC,EAAAA,KAAA,IAAI,sBAAsB,WAAW;AACrC,EAAAA,KAAA,IAAI,sBAAsB,cAAc;AACxC,EAAAA,KAAA,OAAO,sBAAsB,cAAc;AAC3C,EAAAA,KAAA,MAAM,6BAA6B,cAAc;AACjD,EAAAA,KAAA,KAAK,8BAA8B,eAAe;AAGlD,EAAAA,KAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACI,EAAAA,KAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACI,EAAAA,KAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACI,EAAAA,KAAA,IAAI,kBAAkB,qBAAqB;AAG3C,MAAA;AACF,YAAQ,IAAI,sCAAsC;AAC5C,UAAAG,aAAY,IAAI,iBAAiBF,WAAU;AACjDG,wBAAaD,UAAS;AACtB,YAAQ,IAAI,8CAA8C;AAAA,WACnD,OAAO;AACN,YAAA,MAAM,qDAAqD,KAAK;AAExEC,wBAAa,IAAI;AAAA,EAAA;AAGZ,SAAAH;AACT;ACzFA,MAAM,aAAa,aAAa;AAChC,MAAM,OAAO,QAAQ,IAAI,QAAQ;AAGjC,IAAI,iBAAiB,UAAU;AAG/B,MAAM,YAAY,YAAY;AAC9B,MAAM,WAAW,KAAK,KAAK,WAAW,QAAQ;AAG9C,MAAM,MAAM,QAAQ;AAGpB,IAAI,IAAI,QAAQ,OAAO,QAAQ,CAAC;AAGhC,IAAI,IAAI,KAAK,CAAC,KAAK,QAAQ;AAEzB,MAAI,IAAI,KAAK,WAAW,OAAO,KAAK,IAAI,KAAK,WAAW,SAAS,KAAK,IAAI,KAAK,WAAW,KAAK,GAAG;AACzF,WAAA,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,sBAAsB;AAAA,EAAA;AAG7D,MAAI,SAAS,KAAK,KAAK,UAAU,YAAY,CAAC;AAChD,CAAC;AAGD,WAAW,GAAG,WAAW,CAAC,KAAK,QAAQ;AAG/B,QAAA,oBAAoB,WAAW,UAAU,SAAS;AACxD,MAAI,UAAU;AAGd,aAAW,YAAY,mBAAmB;AACpC,QAAA,aAAa,UAAU,QAAQ;AAC7B,UAAA;AACD,iBAAiB,KAAK,GAAG;AAC1B,YAAI,IAAI,aAAa;AACT,oBAAA;AACV;AAAA,QAAA;AAAA,eAEK,GAAG;AAAA,MAAA;AAAA,IAEZ;AAAA,EACF;AAIF,MAAI,CAAC,WAAW,CAAC,IAAI,aAAa;AAChC,QAAI,KAAK,GAAG;AAAA,EAAA;AAEhB,CAAC;AAED,WAAW,OAAO,MAAM,MAAM;AACpB,UAAA,IAAI,4CAA4C,IAAI,EAAE;AACtD,UAAA,IAAI,iCAAiC,IAAI,EAAE;AAC3C,UAAA,IAAI,4BAA4B,IAAI,MAAM;AAC1C,UAAA,IAAI,gCAAgC,IAAI,KAAK;AACvD,CAAC;AAGD,QAAQ,GAAG,WAAW,MAAM;AAC1B,UAAQ,IAAI,+CAA+C;AAC3D,UAAQ,KAAK,CAAC;AAChB,CAAC;AAED,QAAQ,GAAG,UAAU,MAAM;AACzB,UAAQ,IAAI,8CAA8C;AAC1D,UAAQ,KAAK,CAAC;AAChB,CAAC;"}