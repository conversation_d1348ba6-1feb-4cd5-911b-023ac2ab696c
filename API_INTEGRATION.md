# External Application API Integration

This document describes how external applications can send real-time progress and event data to the W8 File Importer dashboard.

## Authentication

All API endpoints require authentication using an API key. Include the API key in one of these ways:

- **Header**: `X-API-Key: your-api-key`
- **Authorization**: `Authorization: Bearer your-api-key`

### Default API Keys (Development)
- `demo-api-key-12345`
- `external-app-key-67890`

## API Endpoints

### Base URL
- **Development**: `http://localhost:8080/api`
- **Production**: `http://localhost:3001/api` (when running as Electron app)

### 1. Send Progress Update

**POST** `/progress`

Update the progress of a file operation (upload, download, sync).

```json
{
  "id": "unique-task-id",
  "title": "Documents Sync",
  "type": "sync",
  "progress": 75,
  "status": "running",
  "startTime": "2025-01-14T12:00:00Z",
  "estimatedCompletion": "2025-01-14T12:30:00Z",
  "currentFile": "document.pdf",
  "totalFiles": 100,
  "processedFiles": 75
}
```

**Fields:**
- `id` (string, required): Unique identifier for the task
- `title` (string, required): Display name for the operation
- `type` (enum, required): `"sync"`, `"upload"`, or `"download"`
- `progress` (number, required): Progress percentage (0-100)
- `status` (enum, required): `"running"`, `"completed"`, `"failed"`, or `"paused"`
- `startTime` (ISO string, optional): When the operation started
- `estimatedCompletion` (ISO string, optional): Estimated completion time
- `currentFile` (string, optional): Currently processing file
- `totalFiles` (number, optional): Total number of files
- `processedFiles` (number, optional): Number of files processed

### 2. Send Event

**POST** `/events`

Send a log event (success, error, warning, info).

```json
{
  "id": "unique-event-id",
  "title": "File uploaded successfully",
  "message": "document.pdf uploaded to ClientA/folder",
  "type": "success",
  "category": "upload",
  "timestamp": "2025-01-14T12:15:00Z",
  "clientPath": "ClientA/Documents/2025",
  "details": "File size: 2.5MB, Upload time: 3.2s"
}
```

**Fields:**
- `id` (string, required): Unique identifier for the event
- `title` (string, required): Event title
- `message` (string, required): Event description
- `type` (enum, required): `"success"`, `"error"`, `"warning"`, or `"info"`
- `category` (enum, required): `"sync"`, `"upload"`, `"download"`, `"system"`, or `"auth"`
- `timestamp` (ISO string, optional): When the event occurred (defaults to now)
- `clientPath` (string, optional): Related file path
- `details` (string, optional): Additional details

### 3. Bulk Update

**POST** `/bulk`

Send multiple progress updates and events in a single request.

```json
{
  "progress": [
    {
      "id": "task-1",
      "title": "Sync A",
      "type": "sync",
      "progress": 50,
      "status": "running"
    }
  ],
  "events": [
    {
      "id": "event-1",
      "title": "Started sync",
      "message": "Sync operation began",
      "type": "info",
      "category": "sync"
    }
  ]
}
```

### 4. Health Check

**GET** `/ws-health`

Check WebSocket service status (no authentication required).

```json
{
  "status": "healthy",
  "connectedClients": 2,
  "timestamp": "2025-01-14T12:00:00Z"
}
```

## Rate Limiting

- **Limit**: 100 requests per minute per API key
- **Response**: HTTP 429 when exceeded

## WebSocket Connection

The dashboard connects to WebSocket at `/ws` to receive real-time updates. External applications don't need to connect directly - use the HTTP API endpoints above.

## Example Usage

### Python Example

```python
import requests
import json
from datetime import datetime, timezone

API_KEY = "demo-api-key-12345"
BASE_URL = "http://localhost:8080/api"

headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# Send progress update
progress_data = {
    "id": "python-sync-001",
    "title": "Python File Sync",
    "type": "sync",
    "progress": 25,
    "status": "running",
    "startTime": datetime.now(timezone.utc).isoformat(),
    "totalFiles": 50,
    "processedFiles": 12
}

response = requests.post(f"{BASE_URL}/progress", 
                        json=progress_data, 
                        headers=headers)
print(f"Progress update: {response.status_code}")

# Send event
event_data = {
    "id": f"python-event-{int(datetime.now().timestamp())}",
    "title": "Sync started",
    "message": "Started syncing Python project files",
    "type": "info",
    "category": "sync",
    "clientPath": "ClientA/Projects/Python"
}

response = requests.post(f"{BASE_URL}/events", 
                        json=event_data, 
                        headers=headers)
print(f"Event sent: {response.status_code}")
```

### cURL Examples

```bash
# Send progress update
curl -X POST http://localhost:8080/api/progress \
  -H "X-API-Key: demo-api-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "curl-test-001",
    "title": "Test Upload",
    "type": "upload",
    "progress": 50,
    "status": "running"
  }'

# Send event
curl -X POST http://localhost:8080/api/events \
  -H "X-API-Key: demo-api-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "curl-event-001",
    "title": "Test completed",
    "message": "cURL test completed successfully",
    "type": "success",
    "category": "system"
  }'

# Check health
curl http://localhost:8080/api/ws-health
```

## Error Responses

### 400 Bad Request
Invalid data format or missing required fields.

### 401 Unauthorized
Missing API key.

### 403 Forbidden
Invalid API key.

### 429 Too Many Requests
Rate limit exceeded.

### 503 Service Unavailable
WebSocket service not available.

## Data Persistence

- **Progress**: Stored temporarily, automatically cleaned up when completed (after 5 minutes)
- **Events**: Last 50 events are kept in memory
- **New Connections**: Receive recent progress and events when connecting

## Security Notes

- API keys are transmitted in headers (use HTTPS in production)
- Rate limiting prevents abuse
- Input validation prevents malformed data
- WebSocket connections are read-only for external clients
