import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: {
        main: resolve(__dirname, 'electron/main.ts'),
        preload: resolve(__dirname, 'electron/preload.ts'),
      },
      formats: ['cjs'],
      fileName: (format, entryName) => `${entryName}.cjs`,
    },
    rollupOptions: {
      external: [
        'electron',
        'path',
        'fs',
        'http',
        'https',
        'ws',
        'express',
        'cors',
        'zod',
        'dotenv',
        'electron-squirrel-startup'
      ],
      output: {
        dir: 'dist/electron',
        format: 'cjs',
        entryFileNames: '[name].cjs',
      },
    },
    outDir: 'dist/electron',
    sourcemap: true,
    minify: false, // Keep readable for debugging
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './client'),
      '@shared': resolve(__dirname, './shared'),
    },
  },
});
