# External API Test Commands

## Setup

First, start the production server:
```bash
npm start
```

The server should start on port 3000 with WebSocket support.

## Test Commands

### 1. Health Check (No Authentication)
```bash
curl http://localhost:3000/api/ws-health
```

### 2. Send Progress Update
```bash
curl -X POST http://localhost:3000/api/progress \
  -H "X-API-Key: demo-api-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-progress-001",
    "title": "Test File Sync",
    "type": "sync",
    "progress": 45,
    "status": "running",
    "startTime": "2025-01-14T12:00:00Z",
    "totalFiles": 100,
    "processedFiles": 45,
    "currentFile": "test-document.pdf"
  }'
```

### 3. Send Event
```bash
curl -X POST http://localhost:3000/api/events \
  -H "X-API-Key: demo-api-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-event-001",
    "title": "Test Event",
    "message": "This is a test event from curl",
    "type": "success",
    "category": "system",
    "details": "Testing external API integration"
  }'
```

### 4. Send Bulk Update
```bash
curl -X POST http://localhost:3000/api/bulk \
  -H "X-API-Key: demo-api-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "progress": [{
      "id": "bulk-progress-001",
      "title": "Bulk Test Progress",
      "type": "upload",
      "progress": 75,
      "status": "running"
    }],
    "events": [{
      "id": "bulk-event-001",
      "title": "Bulk Test Event",
      "message": "Bulk update test completed",
      "type": "info",
      "category": "system"
    }]
  }'
```

### 5. Test Authentication Error
```bash
curl -X POST http://localhost:3000/api/progress \
  -H "Content-Type: application/json" \
  -d '{"id": "test", "title": "No Auth", "type": "sync", "progress": 50, "status": "running"}'
```

## Expected Results

1. **Health Check**: Should return status and connected clients count
2. **Progress Update**: Should return `{"success": true, "message": "Progress update broadcasted"}`
3. **Event**: Should return `{"success": true, "message": "Event broadcasted"}`
4. **Bulk Update**: Should return processing summary
5. **No Auth**: Should return `401 Unauthorized`

## Frontend Testing

Open the frontend at `http://localhost:3000` and you should see the real-time updates in the Progress Tracker and Event Log components.

## Electron App Testing

Build and run the Electron app:
```bash
npm run start:electron
```

The Electron app will run the server on port 3001 and display the dashboard.
