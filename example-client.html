<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MedVest Capital File Service WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
        }
        .panel h3 {
            margin-top: 0;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .example-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 10px 0;
        }
        .example-buttons button {
            font-size: 12px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <h1>MedVest Capital File Service WebSocket Client</h1>
    
    <div class="container">
        <div class="panel">
            <h3>Connection</h3>
            <div class="input-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="ws://W8FileService.local:8081">
            </div>
            <div class="input-group">
                <label for="bearerToken">Bearer Token:</label>
                <input type="text" id="bearerToken" placeholder="Enter your bearer token">
            </div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <div id="status" class="status disconnected">Disconnected</div>
        </div>

        <div class="panel">
            <h3>Quick Actions</h3>
            <div class="example-buttons">
                <button onclick="sendPing()">Ping</button>
                <button onclick="sendGetChannels()">Get Channels</button>
                <button onclick="sendSearchFiles()">Search Files</button>
                <button onclick="sendGetProviders()">Get Providers</button>
            </div>
            
            <h4>Custom Message</h4>
            <div class="input-group">
                <label for="action">Action:</label>
                <select id="action">
                    <option value="ping">ping</option>
                    <option value="get_file_register_channel">get_file_register_channel</option>
                    <option value="search_file_register_channels">search_file_register_channels</option>
                    <option value="get_file">get_file</option>
                    <option value="search_files">search_files</option>
                    <option value="get_storage_provider">get_storage_provider</option>
                    <option value="search_storage_providers">search_storage_providers</option>
                </select>
            </div>
            <textarea id="requestData" placeholder='Enter request data as JSON, e.g.:
{
  "id": "some-guid",
  "includeFiles": true
}'></textarea>
            <button onclick="sendCustomMessage()">Send Message</button>
        </div>
    </div>

    <div class="panel">
        <h3>Messages</h3>
        <button onclick="clearMessages()">Clear Messages</button>
        <textarea id="messages" readonly></textarea>
    </div>

    <script>
        let ws = null;
        let messageId = 1;

        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    updateStatus('Connected', true);
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    addMessage('Connected to ' + url);
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    addMessage('Received: ' + JSON.stringify(message, null, 2));
                };
                
                ws.onclose = function(event) {
                    updateStatus('Disconnected', false);
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    addMessage('Connection closed');
                };
                
                ws.onerror = function(error) {
                    addMessage('Error: ' + error);
                };
                
            } catch (error) {
                addMessage('Connection error: ' + error);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function updateStatus(text, connected) {
            const status = document.getElementById('status');
            status.textContent = text;
            status.className = 'status ' + (connected ? 'connected' : 'disconnected');
        }

        function addMessage(message) {
            const messages = document.getElementById('messages');
            const timestamp = new Date().toLocaleTimeString();
            messages.value += `[${timestamp}] ${message}\n\n`;
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').value = '';
        }

        function sendMessage(action, data = null) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('Error: Not connected to WebSocket');
                return;
            }

            const bearerToken = document.getElementById('bearerToken').value;
            const message = {
                id: `msg-${messageId++}`,
                type: 'request',
                action: action,
                data: data,
                bearerToken: bearerToken || null,
                timestamp: new Date().toISOString()
            };

            ws.send(JSON.stringify(message));
            addMessage('Sent: ' + JSON.stringify(message, null, 2));
        }

        function sendPing() {
            sendMessage('ping');
        }

        function sendGetChannels() {
            const data = {
                pageIndex: 0,
                pageSize: 10,
                searchCriteria: {
                    sortKey: 'name',
                    ascendingOrder: true,
                    filterJoin: 'And',
                    filters: []
                }
            };
            sendMessage('search_file_register_channels', data);
        }

        function sendSearchFiles() {
            const data = {
                pageIndex: 0,
                pageSize: 10,
                searchCriteria: {
                    sortKey: 'fileName',
                    ascendingOrder: true,
                    filterJoin: 'And',
                    filters: []
                }
            };
            sendMessage('search_files', data);
        }

        function sendGetProviders() {
            const data = {
                pageIndex: 0,
                pageSize: 10,
                searchCriteria: {
                    sortKey: 'name',
                    ascendingOrder: true,
                    filterJoin: 'And',
                    filters: []
                }
            };
            sendMessage('search_storage_providers', data);
        }

        function sendCustomMessage() {
            const action = document.getElementById('action').value;
            const dataText = document.getElementById('requestData').value.trim();
            
            let data = null;
            if (dataText) {
                try {
                    data = JSON.parse(dataText);
                } catch (error) {
                    addMessage('Error: Invalid JSON in request data - ' + error.message);
                    return;
                }
            }
            
            sendMessage(action, data);
        }

        // Auto-connect on page load for convenience
        window.onload = function() {
            // Uncomment the line below to auto-connect
            // connect();
        };
    </script>
</body>
</html>
