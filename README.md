# MedVest Capital File Importer Service

A comprehensive WebSocket-based service that interfaces with the MedVest Capital W8Files API, featuring advanced operations management, scheduling, settings, and monitoring capabilities.

## Features

### Core Services
- **WebSocket Interface**: All interactions happen through WebSocket connections
- **Complete API Coverage**: Supports all MedVest Capital W8Files API endpoints
- **File Management**: Upload, download, search, and manage files
- **Channel Management**: Create and manage file register channels
- **Storage Provider Management**: Configure and manage storage providers
- **Authentication**: Bearer token authentication support

### Advanced Services
- **Operations Service**: Queue-based operation management with retry logic and progress tracking
- **Scheduling Service**: Cron-based task scheduling with execution history and monitoring
- **Settings Service**: Hierarchical configuration management with encryption and audit trails
- **Monitoring Service**: Comprehensive system monitoring with metrics, health checks, and alerting

### Infrastructure
- **In-Memory Queuing**: Concurrent operation processing with priority and retry mechanisms
- **LiteDB Database**: Embedded database for data persistence and reporting
- **Real-time Monitoring**: Live system metrics and performance counters
- **Comprehensive Logging**: Detailed audit trails and error tracking

## Configuration

The service can be configured through `appsettings.json`:

```json
{
  "MedVestApi": {
    "BaseUrl": "https://api.medvestcapital.w8file.com",
    "ApiVersion": "1",
    "TimeoutSeconds": 30,
    "MaxRetryAttempts": 3,
    "RetryDelayMilliseconds": 1000
  },
  "WebSocket": {
    "Port": 8080,
    "Host": "localhost",
    "BufferSize": 4096,
    "KeepAliveIntervalSeconds": 30,
    "MaxConnections": 100
  }
}
```

## 🚀 Quick Start

### First-Time Setup (Run as Administrator)
```powershell
# Automated setup and start
.\start-service.ps1 -OpenBrowser

# Or manual setup
.\setup.ps1
dotnet build
dotnet run
```

### Service Endpoints
- **📖 API Documentation**: `http://W8FileService.local:8081/docs`
- **🔌 WebSocket API**: `ws://W8FileService.local:8081`
- **📊 Service Status**: `http://W8FileService.local:8081/status`
- **❤️ Health Check**: `http://W8FileService.local:8081/health`
- **🎯 Actions List**: `http://W8FileService.local:8081/api/actions`

### What the Setup Does
1. **Adds hosts entry**: `W8FileService.local` → `127.0.0.1`
2. **Creates directories**: `Data/`, `Logs/`, `Backups/`
3. **Configures firewall**: Opens port 8081
4. **Updates configuration**: Sets proper host/port settings

## WebSocket API Usage

### Message Format

All WebSocket messages follow this format:

**Request:**
```json
{
  "id": "unique-request-id",
  "type": "request",
  "action": "action_name",
  "data": { /* action-specific data */ },
  "bearerToken": "your-bearer-token",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

**Response:**
```json
{
  "id": "response-id",
  "type": "response",
  "success": true,
  "data": { /* response data */ },
  "error": null,
  "requestId": "original-request-id",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## 🎯 Complete API Coverage

**ALL 31 MedVest Capital W8Files API endpoints are fully implemented** with additional enhanced services:

### 📊 Coverage Summary
- ✅ **31 Original API Methods**: Complete WebSocket implementation
- ✅ **25 Enhanced Service Actions**: Operations, Scheduling, Settings, Monitoring
- ✅ **5 HTTP Documentation Endpoints**: /docs, /status, /health, etc.
- ✅ **Total: 61 Available Actions**

See [API_COVERAGE_VERIFICATION.md](API_COVERAGE_VERIFICATION.md) for detailed verification.

### Available Actions

#### File Register Channels
- `get_file_register_channel` - Get a specific channel
- `create_file_register_channel` - Create a new channel
- `update_file_register_channel` - Update an existing channel
- `delete_file_register_channel` - Delete a channel
- `search_file_register_channels` - Search channels
- `get_accessible_channels` - Get channels accessible to a user
- `get_channel_files` - Get files in a channel
- `activate_file_register_channel` - Activate a channel
- `deactivate_file_register_channel` - Deactivate a channel

#### File Registers
- `get_file_register` - Get a specific register
- `create_file_register` - Create a new register
- `update_file_register` - Update an existing register
- `delete_file_register` - Delete a register
- `search_file_registers` - Search registers
- `activate_file_register` - Activate a register
- `deactivate_file_register` - Deactivate a register

#### Files
- `get_file` - Get file information
- `upload_file` - Upload a new file
- `update_file` - Update an existing file
- `delete_file` - Delete a file
- `download_files` - Download files (returns base64 content)
- `search_files` - Search files

#### File Revisions
- `get_file_revision` - Get a specific file revision

#### Storage Providers
- `get_storage_provider` - Get a specific provider
- `create_storage_provider` - Create a new provider
- `update_storage_provider` - Update an existing provider
- `delete_storage_provider` - Delete a provider
- `search_storage_providers` - Search providers
- `activate_storage_provider` - Activate a provider
- `deactivate_storage_provider` - Deactivate a provider

#### Utility
- `ping` - Test connection (returns "pong")

### Example Usage

#### Upload a File
```json
{
  "id": "upload-123",
  "type": "request",
  "action": "upload_file",
  "bearerToken": "your-token-here",
  "data": {
    "fileData": {
      "channelId": "channel-guid-here",
      "createdByUserId": "user-guid-here",
      "description": "My uploaded file",
      "tags": ["tag1", "tag2"],
      "files": [
        {
          "fileName": "document.pdf",
          "content": "base64-encoded-file-content",
          "contentType": "application/pdf"
        }
      ]
    }
  }
}
```

#### Search Files
```json
{
  "id": "search-456",
  "type": "request",
  "action": "search_files",
  "bearerToken": "your-token-here",
  "data": {
    "pageIndex": 0,
    "pageSize": 10,
    "searchCriteria": {
      "sortKey": "fileName",
      "ascendingOrder": true,
      "filterJoin": "And",
      "filters": [
        {
          "propertyName": "fileName",
          "operation": "Contains",
          "value": "document"
        }
      ]
    }
  }
}
```

#### Get Channel Information
```json
{
  "id": "get-channel-789",
  "type": "request",
  "action": "get_file_register_channel",
  "bearerToken": "your-token-here",
  "data": {
    "id": "channel-guid-here",
    "includeFiles": true,
    "includeHistory": false,
    "includeChildren": true
  }
}
```

## Error Handling

The service provides detailed error messages in the response:

```json
{
  "id": "error-response-id",
  "type": "error",
  "success": false,
  "error": "Detailed error message",
  "requestId": "original-request-id",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## Authentication

All API calls require a valid Bearer token. Include the token in the `bearerToken` field of your WebSocket request messages.

## Development

The service is built with:
- .NET 9.0
- System.Net.WebSockets for WebSocket handling
- Newtonsoft.Json for JSON serialization
- HttpClient for API communication

## Architecture

- **Models/**: Data models for API and WebSocket communication
- **Services/**: Core service implementations
- **Configuration/**: Configuration options
- **Worker.cs**: Background service that starts the WebSocket server
- **Program.cs**: Application entry point and dependency injection setup
