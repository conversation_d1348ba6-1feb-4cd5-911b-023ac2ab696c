# 🚀 W8 File Importer Solution

A comprehensive full-stack application suite for interfacing with the MedVest Capital W8Files API, featuring both a modern React-based dashboard and a powerful WebSocket service backend.

## 📚 Documentation Navigation

### 📖 Core Documentation
- **[🏗️ Architecture Overview](./docs/ARCHITECTURE.md)** - System architecture and design patterns
- **[🚀 Development Setup](./docs/DEVELOPMENT.md)** - Complete development environment setup
- **[🧪 Testing Guide](./docs/TESTING.md)** - Testing strategies and guidelines
- **[📡 API Integration](./API_INTEGRATION.md)** - External application integration guide

### 🛠️ Development Resources
- **[⚡ Development Scripts](./scripts/README.md)** - PowerShell build and development scripts
- **[🎨 Typography & Theming](./docs/TYPOGRAPHY.md)** - Design system and styling guide
- **[🔧 Troubleshooting](./docs/TROUBLESHOOTING.md)** - Common issues and solutions
- **[🚢 Deployment Guide](./docs/DEPLOYMENT.md)** - Production deployment instructions

### 🏢 Project Components
- **[📊 Dashboard Application](#-dashboard-application)** - React-based frontend interface
- **[🔌 WebSocket Service](#-websocket-service)** - Backend API service
- **[🖥️ Electron Desktop App](#-electron-desktop-app)** - Cross-platform desktop application

---

## 🚀 Quick Start

### 🎯 Interactive Development Menu
Run the interactive development menu for guided setup and operations:

```powershell
# Launch the interactive development menu
.\devscripts.ps1
```

### ⚡ Direct Script Access
Use individual PowerShell scripts for specific tasks:

```powershell
# Electron development and building
.\.scripts\electron.ps1 dev          # Start development
.\.scripts\electron.ps1 build        # Build for current platform
.\.scripts\electron.ps1 build -All   # Build for all platforms

# Host configuration
.\.scripts\setup-hosts.ps1            # Setup development hosts

# Service management (FileImporterService)
.\FileImporterService\start-service.ps1 -OpenBrowser
```

### 🌐 Standard Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

---

## 📊 Dashboard Application

Modern React-based dashboard with real-time WebSocket communication.

### ✨ Key Features
- **Real-Time Communication**: WebSocket-based live updates
- **Modern UI**: React 18 + TypeScript + TailwindCSS
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Theme**: Automatic theme switching
- **File Operations**: Upload, download, and manage files
- **Progress Tracking**: Real-time operation progress
- **Service Control**: Start/stop backend services

### 🏗️ Tech Stack
- **Frontend**: React 18 + React Router 6 + TypeScript + Vite
- **UI Framework**: TailwindCSS 3 + Radix UI + Lucide React
- **State Management**: Zustand + React Query
- **Testing**: Vitest + Testing Library
- **Build Tool**: Vite with hot reload

### 📁 Structure
```
client/                   # React SPA frontend
├── components/           # UI components
│   ├── ui/              # Base UI component library
│   ├── SplashScreen.tsx # Loading screen
│   └── ServiceControl.tsx # Service management
├── pages/               # Route components
├── hooks/               # Custom React hooks
├── services/            # Business logic services
└── global.css           # Typography & theme system
```

---

## 🔌 WebSocket Service

Comprehensive WebSocket-based service that interfaces with the MedVest Capital W8Files API.

### 🌟 Core Services
- **WebSocket Interface**: All interactions through WebSocket connections
- **Complete API Coverage**: Supports all MedVest Capital W8Files API endpoints
- **File Management**: Upload, download, search, and manage files
- **Channel Management**: Create and manage file register channels
- **Storage Provider Management**: Configure and manage storage providers
- **Authentication**: Bearer token authentication support

### 🔧 Advanced Services
- **Operations Service**: Queue-based operation management with retry logic
- **Scheduling Service**: Cron-based task scheduling with execution history
- **Settings Service**: Hierarchical configuration management with encryption
- **Monitoring Service**: System monitoring with metrics and health checks

### 🏗️ Infrastructure
- **In-Memory Queuing**: Concurrent operation processing with priority
- **LiteDB Database**: Embedded database for data persistence
- **Real-time Monitoring**: Live system metrics and performance counters
- **Comprehensive Logging**: Detailed audit trails and error tracking

### 📁 Structure
```
FileImporterService/      # .NET WebSocket service
├── Services/            # Core service implementations
├── Models/              # Data models
├── Configuration/       # Configuration options
├── Worker.cs           # Background service
└── Program.cs          # Application entry point
```

---

## 🖥️ Electron Desktop App

Cross-platform desktop application built with Electron.

### ✨ Features
- **Native Desktop Experience**: Windows, macOS, and Linux support
- **Offline Capability**: Works without internet connection
- **System Integration**: Native file dialogs and notifications
- **Auto-Updates**: Automatic application updates
- **Secure**: Sandboxed execution environment

### 🏗️ Build Targets
- **Windows**: NSIS installer (.exe) + unpacked app
- **macOS**: DMG installer (.dmg) + app bundle (.app)
- **Linux**: AppImage (.AppImage) + other formats

### 📁 Structure
```
electron/                # Electron main process
├── main.ts             # Main process entry point
└── preload.ts          # Preload script for security

dist-electron/          # Built distributables
├── win-unpacked/       # Windows unpacked app
├── mac/               # macOS app bundle
└── linux-unpacked/    # Linux unpacked app
```

---

## 🛠️ Development Commands

### 📱 Web Development
```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run typecheck        # TypeScript validation
npm test                # Run test suite
```

### 🖥️ Electron Development
```bash
npm run dev:electron     # Start Electron development
npm run electron:build   # Build Electron app
npm run electron:dist    # Create distributables
```

### 🔧 PowerShell Scripts
```powershell
# Interactive menu
.\devscripts.ps1

# Electron operations
.\.scripts\electron.ps1 [dev|build|run|clean|help]

# Host setup
.\.scripts\setup-hosts.ps1

# Service management
.\FileImporterService\start-service.ps1
```

---

## 🌐 Service Endpoints

### 📊 Dashboard
- **Frontend**: http://localhost:8080
- **API**: http://localhost:8080/api/*
- **WebSocket**: ws://localhost:8080/ws

### 🔌 WebSocket Service
- **API Documentation**: http://W8FileService.local:8081/docs
- **WebSocket API**: ws://W8FileService.local:8081
- **Service Status**: http://W8FileService.local:8081/status
- **Health Check**: http://W8FileService.local:8081/health

---

## 🤝 Contributing

1. **Read the Documentation**: Start with [Development Setup](./docs/DEVELOPMENT.md)
2. **Follow the Workflow**: Use the [Testing Guide](./docs/TESTING.md)
3. **Use the Scripts**: Leverage `.\devscripts.ps1` for development tasks
4. **Check Architecture**: Understand the [Architecture Overview](./docs/ARCHITECTURE.md)

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

---

**🚀 Ready to get started?** Run `.\devscripts.ps1` for the interactive development menu or explore the [Development Setup Guide](./docs/DEVELOPMENT.md).
