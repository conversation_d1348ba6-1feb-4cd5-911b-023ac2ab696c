# 🚀 W8 File Importer Solution

A comprehensive full-stack application suite for interfacing with the MedVest Capital W8Files API, featuring both a modern React-based dashboard and a powerful WebSocket service backend.

## 📚 Documentation Navigation

### 📖 Core Documentation
- **[🏗️ Architecture Overview](./docs/ARCHITECTURE.md)** - System architecture and design patterns
- **[🚀 Development Setup](./docs/DEVELOPMENT.md)** - Complete development environment setup
- **[📖 User Manual](./docs/USER_MANUAL.md)** - Complete user guide for end users and administrators
- **[🧪 Testing Guide](./docs/TESTING.md)** - Testing strategies and guidelines
- **[📡 API Integration](./docs/API_INTEGRATION.md)** - External application integration guide

### 🛠️ Development Resources
- **[⚡ Development Scripts](#-development-scripts)** - PowerShell build and development scripts
- **[🎨 Typography & Theming](./docs/TYPOGRAPHY.md)** - Design system and styling guide
- **[🔧 Troubleshooting](./docs/TROUBLESHOOTING.md)** - Common issues and solutions
- **[🚢 Deployment Guide](./docs/DEPLOYMENT.md)** - Production deployment instructions
- **[📊 Build Status](./docs/BUILD_STATUS.md)** - Build and deployment status information

### 🏢 Project Components
- **[📊 Dashboard Application](#-dashboard-application)** - React-based frontend interface
- **[🔌 WebSocket Service](#-websocket-service)** - Backend API service
- **[🖥️ Electron Desktop App](#-electron-desktop-app)** - Cross-platform desktop application

---

## 🚀 Quick Start

### 🎯 Interactive Development Menu
Run the interactive development menu for guided setup and operations:

```powershell
# Launch the interactive development menu
.\devscripts.ps1
```

### ⚡ Direct Script Access
Use individual PowerShell scripts for specific tasks:

```powershell
# Dashboard development (React + Electron)
cd W8File.Importer
npm run dev              # Start web development
npm run dev:electron     # Start Electron development

# WebSocket Service (.NET)
cd FileImporterService
.\start-service.ps1 -OpenBrowser    # Setup and start service
dotnet run                          # Run service directly
```

### 🌐 Standard Development
```bash
# Dashboard Application
cd W8File.Importer
npm install && npm run dev

# WebSocket Service
cd FileImporterService
dotnet restore && dotnet run
```

---

## 📊 Dashboard Application

**Location**: `./W8File.Importer/`

Modern React-based dashboard with real-time WebSocket communication.

### ✨ Key Features
- **Real-Time Communication**: WebSocket-based live updates
- **Modern UI**: React 18 + TypeScript + TailwindCSS
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Theme**: Automatic theme switching
- **File Operations**: Upload, download, and manage files
- **Progress Tracking**: Real-time operation progress
- **Service Control**: Start/stop backend services

### 🏗️ Tech Stack
- **Frontend**: React 18 + React Router 6 + TypeScript + Vite
- **UI Framework**: TailwindCSS 3 + Radix UI + Lucide React
- **State Management**: Zustand + React Query
- **Testing**: Vitest + Testing Library
- **Build Tool**: Vite with hot reload

### 📁 Structure
```
W8File.Importer/
├── client/                 # React SPA frontend
│   ├── components/         # UI components
│   │   ├── ui/            # Base UI component library
│   │   ├── SplashScreen.tsx
│   │   └── ServiceControl.tsx
│   ├── pages/             # Route components
│   ├── hooks/             # Custom React hooks
│   ├── services/          # Business logic services
│   └── global.css         # Typography & theme system
├── server/                # Express API backend
│   ├── routes/            # API endpoints
│   ├── middleware/        # Custom middleware
│   ├── websocket.ts       # WebSocket manager
│   └── index.ts           # Server configuration
├── electron/              # Electron main process
├── shared/                # Shared types and utilities
└── scripts/               # Build and development scripts
```

### 🛠️ Development Commands
```bash
cd W8File.Importer

# Development
npm run dev              # Start development server
npm run dev:electron     # Start Electron development
npm run build            # Build for production
npm run start            # Start production server

# Code Quality
npm run typecheck        # TypeScript type checking
npm test                # Run test suite

# Electron Distribution
npm run electron:build   # Build Electron app
npm run electron:dist    # Create distributables
```

---

## 🔌 WebSocket Service

**Location**: `./FileImporterService/`

Comprehensive .NET WebSocket-based service that interfaces with the MedVest Capital W8Files API.

### 🌟 Core Services
- **WebSocket Interface**: All interactions through WebSocket connections
- **Complete API Coverage**: Supports all MedVest Capital W8Files API endpoints
- **File Management**: Upload, download, search, and manage files
- **Channel Management**: Create and manage file register channels
- **Storage Provider Management**: Configure and manage storage providers
- **Authentication**: Bearer token authentication support

### 🔧 Advanced Services
- **Operations Service**: Queue-based operation management with retry logic
- **Scheduling Service**: Cron-based task scheduling with execution history
- **Settings Service**: Hierarchical configuration management with encryption
- **Monitoring Service**: System monitoring with metrics and health checks

### 🏗️ Infrastructure
- **In-Memory Queuing**: Concurrent operation processing with priority
- **LiteDB Database**: Embedded database for data persistence
- **Real-time Monitoring**: Live system metrics and performance counters
- **Comprehensive Logging**: Detailed audit trails and error tracking

### 📁 Structure
```
FileImporterService/
├── Services/              # Core service implementations
│   ├── WebSocketService.cs
│   ├── OperationsService.cs
│   ├── SchedulingService.cs
│   ├── SettingsService.cs
│   └── MonitoringService.cs
├── Models/                # Data models
├── Configuration/         # Configuration options
├── Worker.cs             # Background service
├── Program.cs            # Application entry point
├── setup.ps1             # First-time setup script
└── start-service.ps1     # Service startup script
```

### 🛠️ Development Commands
```bash
cd FileImporterService

# First-time setup (run as Administrator)
.\start-service.ps1 -OpenBrowser

# Manual setup and run
.\setup.ps1
dotnet restore
dotnet build
dotnet run

# Development
dotnet watch run          # Hot reload development
dotnet test              # Run tests
```

### 🌐 Service Endpoints
- **📖 API Documentation**: `http://W8FileService.local:8081/docs`
- **🔌 WebSocket API**: `ws://W8FileService.local:8081`
- **📊 Service Status**: `http://W8FileService.local:8081/status`
- **❤️ Health Check**: `http://W8FileService.local:8081/health`
- **🎯 Actions List**: `http://W8FileService.local:8081/api/actions`

---

## 🖥️ Electron Desktop App

**Location**: `./W8File.Importer/electron/`

Cross-platform desktop application built with Electron, providing native desktop experience.

### ✨ Features
- **Native Desktop Experience**: Windows, macOS, and Linux support
- **Offline Capability**: Works without internet connection
- **System Integration**: Native file dialogs and notifications
- **Auto-Updates**: Automatic application updates
- **Secure**: Sandboxed execution environment

### 🏗️ Build Targets
- **Windows**: NSIS installer (.exe) + unpacked app
- **macOS**: DMG installer (.dmg) + app bundle (.app)
- **Linux**: AppImage (.AppImage) + other formats

### 📁 Structure
```
W8File.Importer/
├── electron/              # Electron main process
│   ├── main.ts           # Main process entry point
│   └── preload.ts        # Preload script for security
└── dist-electron/        # Built distributables
    ├── win-unpacked/     # Windows unpacked app
    ├── mac/             # macOS app bundle
    └── linux-unpacked/  # Linux unpacked app
```

---

## ⚡ Development Scripts

Interactive PowerShell development menu and individual scripts for all development tasks.

### 🎯 Interactive Menu
```powershell
# Launch the comprehensive development menu
.\devscripts.ps1
```

**Menu Options:**
1. 🚀 **Start Development with Proxy** - Full stack with reverse proxy
2. 🔧 **Start Development (Standard)** - Vite dev server + Electron
3. 🌐 **Start Vite Dev Server Only** - Web development server
4. ⚡ **Start Electron App Only** - Desktop application
5. 🏗️ **Build Application** - Production build
6. 🧹 **Clean Build Artifacts** - Remove build folders
7. 🔧 **Setup Hosts File** - Configure development domains
8. 🔗 **Create Reverse Proxy Mapping** - Configure proxy routes
9. ❓ **Show Help** - Detailed command help

### 📜 Individual Scripts

**Dashboard Scripts** (in `W8File.Importer/`):
```powershell
# Main development scripts are npm-based
cd W8File.Importer
npm run dev              # Start development server
npm run dev:electron     # Start Electron development
npm run build            # Build for production
```

**Service Scripts** (in `FileImporterService/`):
- `setup.ps1` - First-time environment setup
- `start-service.ps1` - Service startup with options

---

## 🛠️ Development Commands

### 📱 Web Development
```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run typecheck        # TypeScript validation
npm test                # Run test suite
```

### 🖥️ Electron Development
```bash
npm run dev:electron     # Start Electron development
npm run electron:build   # Build Electron app
npm run electron:dist    # Create distributables
```

### 🔧 PowerShell Scripts
```powershell
# Interactive menu
.\devscripts.ps1

# Service management
cd FileImporterService
.\setup.ps1                    # First-time setup
.\start-service.ps1           # Start service with setup
```

---

## 🌐 Service Endpoints

### 📊 Dashboard
- **Frontend**: http://localhost:8080
- **API**: http://localhost:8080/api/*
- **WebSocket**: ws://localhost:8080/ws

### 🔌 WebSocket Service
- **API Documentation**: http://W8FileService.local:8081/docs
- **WebSocket API**: ws://W8FileService.local:8081
- **Service Status**: http://W8FileService.local:8081/status
- **Health Check**: http://W8FileService.local:8081/health

---

## 🤝 Contributing

1. **Read the Documentation**: Start with [Development Setup](./docs/DEVELOPMENT.md)
2. **Follow the Workflow**: Use the [Testing Guide](./docs/TESTING.md)
3. **Use the Scripts**: Leverage `.\devscripts.ps1` for development tasks
4. **Check Architecture**: Understand the [Architecture Overview](./docs/ARCHITECTURE.md)

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

---

**🚀 Ready to get started?** Run `.\devscripts.ps1` for the interactive development menu or explore the [Development Setup Guide](./docs/DEVELOPMENT.md).
