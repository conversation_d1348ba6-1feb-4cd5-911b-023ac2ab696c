#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Run Built Electron Application Script
# Runs the production-built Electron application

param(
    [switch]$Build
)

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║  ▶️  " -NoNewline -ForegroundColor Magenta
Write-Host "RUN ELECTRON APPLICATION" -NoNewline -ForegroundColor Green
Write-Host "                                     ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""

# Check if Node.js and npm are available
Write-Host "🔍 " -NoNewline -ForegroundColor Blue
Write-Host "Checking prerequisites..." -ForegroundColor White

if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Gray
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Node.js and npm found" -ForegroundColor Green

# Build if requested or if dist doesn't exist
if ($Build -or -not (Test-Path "dist")) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
    Write-Host "│  🔨 BUILDING APPLICATION...           │" -ForegroundColor Blue
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
    Write-Host ""
    
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Build failed" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Application built successfully!" -ForegroundColor Green
}

# Check if built files exist
if (-not (Test-Path "dist/server")) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Built application not found." -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Run with " -NoNewline -ForegroundColor Gray
    Write-Host "-Build" -NoNewline -ForegroundColor Green
    Write-Host " flag or build manually first:" -ForegroundColor Gray
    Write-Host "   " -NoNewline
    Write-Host ".\\scripts\\run-electron.ps1 -Build" -ForegroundColor Green
    Write-Host "   " -NoNewline
    Write-Host "npm run build" -ForegroundColor Green
    Write-Host ""
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Built application found" -ForegroundColor Green

# Run the Electron application
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Magenta
Write-Host "│  ▶️  STARTING ELECTRON APPLICATION... │" -ForegroundColor Magenta
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Magenta
Write-Host ""

Write-Host "🚀 " -NoNewline -ForegroundColor Yellow
Write-Host "Launching application..." -ForegroundColor White
Write-Host "🛑 " -NoNewline -ForegroundColor Red
Write-Host "Close the Electron window to stop" -ForegroundColor Gray
Write-Host ""

npm run start:electron

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Failed to start Electron application" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 " -NoNewline -ForegroundColor Green
Write-Host "Electron application finished successfully!" -ForegroundColor Green
Write-Host ""
