#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Setup Hosts File Entry Script
# Adds W8File.Importer.Local to the hosts file for development

$Remove = $args -contains '-Remove'

$hostEntry = "127.0.0.1`tW8File.Importer.Local"
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Add-HostsEntry {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Green
    Write-Host "│  🌐 SETTING UP HOSTS FILE ENTRY       │" -ForegroundColor Green
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Green
    Write-Host ""

    if (-not (Test-AdminRights)) {
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "This script requires administrator privileges to modify the hosts file." -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 " -NoNewline -ForegroundColor Yellow
        Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
        Write-Host ""
        exit 1
    }

    try {
        # Read current hosts file
        $hostsContent = Get-Content $hostsPath -ErrorAction Stop
        
        # Check if entry already exists
        $entryExists = $hostsContent | Where-Object { $_ -match "W8File\.Importer\.Local" }
        
        if ($entryExists) {
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "Hosts entry for " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -NoNewline -ForegroundColor Cyan
            Write-Host " already exists" -ForegroundColor Gray
        } else {
            # Add the entry
            Add-Content -Path $hostsPath -Value $hostEntry -ErrorAction Stop
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "Added hosts entry: " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -NoNewline -ForegroundColor Cyan
            Write-Host " -> " -NoNewline -ForegroundColor Gray
            Write-Host "127.0.0.1" -ForegroundColor Green
        }
        
        # Flush DNS cache
        Write-Host ""
        Write-Host "🔄 " -NoNewline -ForegroundColor Blue
        Write-Host "Flushing DNS cache..." -ForegroundColor Gray
        ipconfig /flushdns | Out-Null
        Write-Host "✅ " -NoNewline -ForegroundColor Green
        Write-Host "DNS cache flushed successfully" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Failed to modify hosts file: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Remove-HostsEntry {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Red
    Write-Host "│  🗑️  REMOVING HOSTS FILE ENTRY        │" -ForegroundColor Red
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Red
    Write-Host ""

    if (-not (Test-AdminRights)) {
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "This script requires administrator privileges to modify the hosts file." -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 " -NoNewline -ForegroundColor Yellow
        Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
        Write-Host ""
        exit 1
    }

    try {
        # Read current hosts file
        $hostsContent = Get-Content $hostsPath -ErrorAction Stop
        
        # Filter out the W8File entry
        $filteredContent = $hostsContent | Where-Object { $_ -notmatch "W8File\.Importer\.Local" }
        
        # Check if anything was removed
        if ($hostsContent.Count -eq $filteredContent.Count) {
            Write-Host "ℹ️  " -NoNewline -ForegroundColor Blue
            Write-Host "No hosts entry found for " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -ForegroundColor Cyan
        } else {
            # Write back the filtered content
            Set-Content -Path $hostsPath -Value $filteredContent -ErrorAction Stop
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "Removed hosts entry for " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -ForegroundColor Cyan
            
            # Flush DNS cache
            Write-Host ""
            Write-Host "🔄 " -NoNewline -ForegroundColor Blue
            Write-Host "Flushing DNS cache..." -ForegroundColor Gray
            ipconfig /flushdns | Out-Null
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "DNS cache flushed successfully" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Failed to modify hosts file: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Main execution
if ($Remove) {
    Remove-HostsEntry
} else {
    Add-HostsEntry
}

Write-Host ""
Write-Host "🎉 " -NoNewline -ForegroundColor Green
Write-Host "Hosts file operation completed successfully!" -ForegroundColor Green
Write-Host ""
