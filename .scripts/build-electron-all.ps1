#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Cross-Platform Electron Build Script
# Builds Electron distributables for all platforms

param(
    [switch]$Clean,
    [switch]$SkipTests
)

Write-Host ""
Write-Host "╔═════════════════��════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
Write-Host "║  🌍 " -NoNewline -ForegroundColor Blue
Write-Host "CROSS-PLATFORM ELECTRON BUILD" -NoNewline -ForegroundColor Magenta
Write-Host "                              ║" -ForegroundColor Magenta
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
Write-Host ""

Write-Host "🎯 " -NoNewline -ForegroundColor Yellow
Write-Host "Target Platforms: " -NoNewline -ForegroundColor White
Write-Host "Windows 🖥️  macOS 🍎 Linux 🐧" -ForegroundColor Gray
Write-Host ""

# Check if Node.js and npm are available
Write-Host "🔍 " -NoNewline -ForegroundColor Blue
Write-Host "Checking prerequisites..." -ForegroundColor White

if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Gray
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Node.js and npm found" -ForegroundColor Green

# Clean previous builds if requested
if ($Clean) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Red
    Write-Host "│  🧹 CLEANING PREVIOUS BUILDS...       │" -ForegroundColor Red
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Red
    Write-Host ""
    
    $foldersToClean = @("dist", "dist-electron")
    foreach ($folder in $foldersToClean) {
        if (Test-Path $folder) {
            Write-Host "  🗂️  " -NoNewline -ForegroundColor Red
            Write-Host "Removing " -NoNewline -ForegroundColor Gray
            Write-Host "$folder" -NoNewline -ForegroundColor White
            Write-Host "..." -ForegroundColor Gray
            Remove-Item -Recurse -Force $folder
            Write-Host "    ✅ Deleted successfully" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "🎉 " -NoNewline -ForegroundColor Green
    Write-Host "Clean completed!" -ForegroundColor Green
}

# Install dependencies
Write-Host ""
Write-Host "┌───────────────��────────────────────────┐" -ForegroundColor Yellow
Write-Host "│  📦 INSTALLING DEPENDENCIES...        │" -ForegroundColor Yellow
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
Write-Host ""

npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Failed to install dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Dependencies installed successfully!" -ForegroundColor Green

# Run tests if not skipped
if (-not $SkipTests) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
    Write-Host "│  🧪 RUNNING TESTS...                  │" -ForegroundColor Blue
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
    Write-Host ""
    
    npm test
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Tests failed" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "All tests passed!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⏭️  " -NoNewline -ForegroundColor Yellow
    Write-Host "Skipping tests as requested" -ForegroundColor Yellow
}

# Run type checking
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Cyan
Write-Host "│  🔍 RUNNING TYPE CHECK...             │" -ForegroundColor Cyan
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Cyan
Write-Host ""

npm run typecheck
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Type check failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Type check passed!" -ForegroundColor Green

# Build the application
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
Write-Host "│  🔨 BUILDING APPLICATION...           │" -ForegroundColor Blue
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
Write-Host ""

npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Application built successfully!" -ForegroundColor Green

# Install electron-builder globally if not available
$electronBuilder = Get-Command "electron-builder" -ErrorAction SilentlyContinue
if (-not $electronBuilder) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  📦 INSTALLING ELECTRON-BUILDER...    │" -ForegroundColor Yellow
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
    Write-Host ""
    
    npm install -g electron-builder
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "electron-builder installed!" -ForegroundColor Green
}

# Build for each platform
$platforms = @(
    @{ Name = "Windows"; Emoji = "🖥️ "; Flag = "--win"; Color = "Blue" },
    @{ Name = "macOS"; Emoji = "🍎 "; Flag = "--mac"; Color = "White" },
    @{ Name = "Linux"; Emoji = "🐧 "; Flag = "--linux"; Color = "Green" }
)

$successCount = 0
$totalPlatforms = $platforms.Count

foreach ($platform in $platforms) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor $platform.Color
    Write-Host "│  $($platform.Emoji)BUILDING FOR $($platform.Name.ToUpper())..." -NoNewline -ForegroundColor $platform.Color
    $spaces = " " * (25 - $platform.Name.Length)
    Write-Host "$spaces│" -ForegroundColor $platform.Color
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor $platform.Color
    Write-Host ""
    
    npx electron-builder $platform.Flag
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ " -NoNewline -ForegroundColor Green
        Write-Host "$($platform.Name) build completed successfully!" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
        Write-Host "$($platform.Name) build failed, continuing..." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
Write-Host "║  🎉 " -NoNewline -ForegroundColor Green
Write-Host "BUILD SUMMARY" -NoNewline -ForegroundColor Magenta
Write-Host "                                                      ║" -ForegroundColor Magenta
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
Write-Host ""

Write-Host "📊 " -NoNewline -ForegroundColor Cyan
Write-Host "Successful builds: " -NoNewline -ForegroundColor White
Write-Host "$successCount" -NoNewline -ForegroundColor Green
Write-Host "/" -NoNewline -ForegroundColor Gray
Write-Host "$totalPlatforms" -ForegroundColor Green

Write-Host "📂 " -NoNewline -ForegroundColor Cyan
Write-Host "Check the " -NoNewline -ForegroundColor Gray
Write-Host "'dist-electron'" -NoNewline -ForegroundColor White
Write-Host " folder for your distributables" -ForegroundColor Gray

# List created files
if (Test-Path "dist-electron") {
    Write-Host ""
    Write-Host "📋 " -NoNewline -ForegroundColor Cyan
    Write-Host "Created distributables:" -ForegroundColor White
    Get-ChildItem "dist-electron" -Recurse -File | ForEach-Object {
        $size = [math]::Round($_.Length / 1MB, 2)
        $icon = if ($_.Extension -eq ".exe") { "🖥️ " } elseif ($_.Extension -eq ".dmg") { "🍎 " } elseif ($_.Extension -eq ".AppImage") { "🐧 " } else { "📄 " }
        Write-Host "  $icon" -NoNewline -ForegroundColor Blue
        Write-Host "$($_.Name) " -NoNewline -ForegroundColor White
        Write-Host "(${size}MB)" -ForegroundColor Gray
    }
}

Write-Host ""
