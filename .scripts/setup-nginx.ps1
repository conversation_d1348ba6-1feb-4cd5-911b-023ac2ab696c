#!/usr/bin/env pwsh
# Reverse Proxy Setup Script for W8File

Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

# ConvertTo-Ini function
Function ConvertTo-Ini {
    Param (
        [Parameter(Mandatory)] [array]$Config
    )

    $IniContent = ""
    foreach ($section in $Config) {
        $sectionName = $section.Section
        $IniContent += "[$sectionName]`n"
        foreach ($key in $section.Keys.GetEnumerator()) {
            $IniContent += "$($key.Key) = $($key.Value)`n"
        }
        $IniContent += "`n"
    }
    return $IniContent.TrimEnd()
}

# Nginx Configuration Content
$NginxConfig = @'
server {
    listen 80;
    server_name W8File.Importer.Local;
    
    location / {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
'@

# Path to configuration files
$nginxConfPath = "/usr/local/etc/nginx/servers/W8File.conf"

# Function to setup Nginx and add W8File configuration
function Setup-ReverseProxy {
    if (-not (Get-Command nginx -ErrorAction SilentlyContinue)) {
        Write-Host "Nginx is not installed. Please install Nginx first." -ForegroundColor Red
        return
    }

    # Write Nginx Configuration
    Write-Host "Writing Nginx configuration for W8File..." -ForegroundColor Green
    Set-Content -Path $nginxConfPath -Value $NginxConfig

    # Restart Nginx to apply configuration
    Write-Host "Restarting Nginx to apply new configuration..." -ForegroundColor Cyan
    nginx -s reload
    Write-Host "Reverse proxy setup completed! Access the app at http://W8File.Importer.Local" -ForegroundColor Green
}

# Check for admin rights
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "You need to run this script as an Administrator to modify the hosts file and setup Nginx." -ForegroundColor Red
    exit 1
}

Setup-ReverseProxy

