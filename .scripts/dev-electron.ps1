#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Electron Development Script
# Starts the development server and Electron app

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║  🚀 " -NoNewline -ForegroundColor Yellow
Write-Host "ELECTRON DEVELOPMENT ENVIRONMENT" -NoNewline -ForegroundColor Cyan
Write-Host "                             ║" -ForegroundColor Cyan
Write-Host "╚═════════════════════════════════���════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js and npm are available
Write-Host "🔍 " -NoNewline -ForegroundColor Blue
Write-Host "Checking prerequisites..." -ForegroundColor White

if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Gray
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Node.js and npm found" -ForegroundColor Green

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  📦 INSTALLING DEPENDENCIES...        │" -ForegroundColor Yellow
    Write-Host "└──────────────────────────────��─────────┘" -ForegroundColor Yellow
    Write-Host ""
    
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} else {
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Dependencies already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
Write-Host "│  🔧 STARTING DEVELOPMENT ENVIRONMENT  │" -ForegroundColor Blue
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
Write-Host ""

Write-Host "⚡ " -NoNewline -ForegroundColor Yellow
Write-Host "Launching Electron with hot reload..." -ForegroundColor White
Write-Host "🌐 " -NoNewline -ForegroundColor Cyan
Write-Host "Web server will start automatically" -ForegroundColor Gray
Write-Host "💻 " -NoNewline -ForegroundColor Magenta
Write-Host "Electron window will open when ready" -ForegroundColor Gray
Write-Host ""
Write-Host "🛑 " -NoNewline -ForegroundColor Red
Write-Host "Press Ctrl+C to stop the development environment" -ForegroundColor Gray
Write-Host ""

# Start Electron development
npm run dev:electron

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Failed to start Electron development environment" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 " -NoNewline -ForegroundColor Green
Write-Host "Electron development environment finished!" -ForegroundColor Green
Write-Host ""
