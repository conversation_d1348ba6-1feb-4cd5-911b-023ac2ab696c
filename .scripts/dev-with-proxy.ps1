#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Electron Development Script with Reverse Proxy
# Starts the development server, reverse proxy, and Electron app

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║  🚀 " -NoNewline -ForegroundColor Yellow
Write-Host "ELECTRON DEVELOPMENT WITH REVERSE PROXY" -NoNewline -ForegroundColor Cyan
Write-Host "                       ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js and npm are available
Write-Host "🔍 " -NoNewline -ForegroundColor Blue
Write-Host "Checking prerequisites..." -ForegroundColor White

if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Gray
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Node.js and npm found" -ForegroundColor Green

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Check admin rights for port 80
if (-not (Test-Administrator)) {
    Write-Host ""
    Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
    Write-Host "Administrator privileges required for reverse proxy on port 80" -ForegroundColor Yellow
    Write-Host "   " -NoNewline
    Write-Host "Please run PowerShell as Administrator or the proxy will use port 8080" -ForegroundColor Gray
    Write-Host ""
}

# Setup hosts file entry
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Green
Write-Host "│  🌐 SETTING UP HOSTS FILE ENTRY       │" -ForegroundColor Green
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Green
Write-Host ""

$hostEntry = "127.0.0.1`tW8File.Importer.Local"
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

try {
    if (Test-Administrator) {
        # Read current hosts file
        $hostsContent = Get-Content $hostsPath -ErrorAction Stop
        
        # Check if entry already exists
        $entryExists = $hostsContent | Where-Object { $_ -match "W8File\.Importer\.Local" }
        
        if ($entryExists) {
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "Hosts entry for " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -NoNewline -ForegroundColor Cyan
            Write-Host " already exists" -ForegroundColor Gray
        } else {
            # Add the entry
            Add-Content -Path $hostsPath -Value $hostEntry -ErrorAction Stop
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "Added hosts entry: " -NoNewline -ForegroundColor Gray
            Write-Host "W8File.Importer.Local" -NoNewline -ForegroundColor Cyan
            Write-Host " -> " -NoNewline -ForegroundColor Gray
            Write-Host "127.0.0.1" -ForegroundColor Green
            
            # Flush DNS cache
            Write-Host "🔄 " -NoNewline -ForegroundColor Blue
            Write-Host "Flushing DNS cache..." -ForegroundColor Gray
            ipconfig /flushdns | Out-Null
            Write-Host "✅ " -NoNewline -ForegroundColor Green
            Write-Host "DNS cache flushed" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
        Write-Host "Skipping hosts file modification (requires admin privileges)" -ForegroundColor Yellow
        Write-Host "   💡 " -NoNewline -ForegroundColor Blue
        Write-Host "You may need to manually add: " -NoNewline -ForegroundColor Gray
        Write-Host "127.0.0.1 W8File.Importer.Local" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️  " -NoNewline -ForegroundColor Yellow
    Write-Host "Could not modify hosts file: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  📦 INSTALLING DEPENDENCIES...        │" -ForegroundColor Yellow
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
    Write-Host ""
    
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} else {
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Dependencies already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
Write-Host "│  🔧 STARTING DEVELOPMENT STACK        │" -ForegroundColor Blue
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
Write-Host ""

Write-Host "⚡ " -NoNewline -ForegroundColor Yellow
Write-Host "Launching development stack..." -ForegroundColor White
Write-Host "📡 " -NoNewline -ForegroundColor Cyan
Write-Host "Vite dev server on localhost:5173" -ForegroundColor Gray
Write-Host "🔄 " -NoNewline -ForegroundColor Magenta
Write-Host "Reverse proxy on W8File.Importer.Local" -ForegroundColor Gray
Write-Host "💻 " -NoNewline -ForegroundColor Green
Write-Host "Electron app will open when ready" -ForegroundColor Gray
Write-Host ""
Write-Host "🛑 " -NoNewline -ForegroundColor Red
Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Gray
Write-Host ""

# Start the development environment with proxy
npm run dev:electron:proxy

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Failed to start development environment" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 " -NoNewline -ForegroundColor Green
Write-Host "Development environment finished!" -ForegroundColor Green
Write-Host ""
