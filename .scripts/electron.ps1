#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Main Electron Build Orchestrator Script
# Provides a unified interface for all Electron operations

$Command = $args[0]
$Clean = $args -contains '-Clean'
$All = $args -contains '-All'
$PackageOnly = $args -contains '-PackageOnly'
$SkipTests = $args -contains '-SkipTests'

# Default command to help if none is provided
if (-not $Command) {
    $Command = 'help'
}

# Validate command
$validCommands = @("dev", "dev-proxy", "build", "run", "clean", "help")

function Show-Help {
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━───━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
    Write-Host "  ⚡ " -NoNewline -ForegroundColor Yellow
    Write-Host "ELECTRON BUILD ORCHESTRATOR" -NoNewline -ForegroundColor Cyan
    Write-Host " ⚡" -ForegroundColor Yellow
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "  🚀 " -NoNewline -ForegroundColor Blue
    Write-Host "USAGE: " -NoNewline -ForegroundColor White
    Write-Host ".\\scripts\\electron.ps1 [command] [options]" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  📋 " -NoNewline -ForegroundColor Green
    Write-Host "COMMANDS:" -ForegroundColor White
    Write-Host "    🔧 " -NoNewline -ForegroundColor Yellow
    Write-Host "dev        " -NoNewline -ForegroundColor Green
    Write-Host "Start development environment with hot reload" -ForegroundColor Gray
    Write-Host "    🌐 " -NoNewline -ForegroundColor Cyan
    Write-Host "dev-proxy  " -NoNewline -ForegroundColor Green
    Write-Host "Start development with reverse proxy (no port needed)" -ForegroundColor Gray
    Write-Host "    🏗️  " -NoNewline -ForegroundColor Blue
    Write-Host "build      " -NoNewline -ForegroundColor Green
    Write-Host "Build production Electron application" -ForegroundColor Gray
    Write-Host "    ▶️  " -NoNewline -ForegroundColor Magenta
    Write-Host "run        " -NoNewline -ForegroundColor Green
    Write-Host "Run the built Electron application" -ForegroundColor Gray
    Write-Host "    🧹 " -NoNewline -ForegroundColor Red
    Write-Host "clean      " -NoNewline -ForegroundColor Green
    Write-Host "Clean all build artifacts" -ForegroundColor Gray
    Write-Host "    ❓ " -NoNewline -ForegroundColor Cyan
    Write-Host "help       " -NoNewline -ForegroundColor Green
    Write-Host "Show this help message" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  ⚙️  " -NoNewline -ForegroundColor Yellow
    Write-Host "OPTIONS:" -ForegroundColor White
    Write-Host "    🧹 " -NoNewline -ForegroundColor Red
    Write-Host "-Clean        " -NoNewline -ForegroundColor Green
    Write-Host "Clean previous builds before building" -ForegroundColor Gray
    Write-Host "    🌍 " -NoNewline -ForegroundColor Blue
    Write-Host "-All          " -NoNewline -ForegroundColor Green
    Write-Host "Build for all platforms (Windows, macOS, Linux)" -ForegroundColor Gray
    Write-Host "    📦 " -NoNewline -ForegroundColor Cyan
    Write-Host "-PackageOnly  " -NoNewline -ForegroundColor Green
    Write-Host "Only package, don't rebuild (assumes dist exists)" -ForegroundColor Gray
    Write-Host "    ⏭️  " -NoNewline -ForegroundColor Yellow
    Write-Host "-SkipTests    " -NoNewline -ForegroundColor Green
    Write-Host "Skip running tests during build" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "EXAMPLES:" -ForegroundColor White
    Write-Host "    " -NoNewline
    Write-Host ".\\scripts\\electron.ps1 dev" -NoNewline -ForegroundColor Green
    Write-Host "                    # Start development" -ForegroundColor Gray
    Write-Host "    " -NoNewline
    Write-Host ".\\scripts\\electron.ps1 build -Clean" -NoNewline -ForegroundColor Green
    Write-Host "          # Clean build for current platform" -ForegroundColor Gray
    Write-Host "    " -NoNewline
    Write-Host ".\\scripts\\electron.ps1 build -All" -NoNewline -ForegroundColor Green
    Write-Host "            # Build for all platforms" -ForegroundColor Gray
    Write-Host "    " -NoNewline
    Write-Host ".\\scripts\\electron.ps1 run" -NoNewline -ForegroundColor Green
    Write-Host "                   # Run built application" -ForegroundColor Gray
    Write-Host "    " -NoNewline
    Write-Host ".\\scripts\\electron.ps1 clean" -NoNewline -ForegroundColor Green
    Write-Host "                 # Clean build artifacts" -ForegroundColor Gray
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
    Write-Host ""
}

function Clean-BuildArtifacts {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
    Write-Host "│  🧹 CLEANING BUILD ARTIFACTS...       │" -ForegroundColor Yellow
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
    Write-Host ""

    $foldersToClean = @("dist", "dist-electron", "node_modules/.cache")

    foreach ($folder in $foldersToClean) {
        if (Test-Path $folder) {
            Write-Host "  🗂️  " -NoNewline -ForegroundColor Red
            Write-Host "Removing " -NoNewline -ForegroundColor Gray
            Write-Host "$folder" -NoNewline -ForegroundColor White
            Write-Host "..." -ForegroundColor Gray
            Remove-Item -Recurse -Force $folder
            Write-Host "    ✅ Deleted successfully" -ForegroundColor Green
        } else {
            Write-Host "  📂 " -NoNewline -ForegroundColor Gray
            Write-Host "$folder" -NoNewline -ForegroundColor White
            Write-Host " (not found, skipping)" -ForegroundColor Gray
        }
    }

    Write-Host ""
    Write-Host "🎉 " -NoNewline -ForegroundColor Green
    Write-Host "Clean completed successfully!" -ForegroundColor Green
    Write-Host ""
}

# Ensure we're in the project root
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptDir

if (-not (Test-Path (Join-Path $projectRoot "package.json"))) {
    Write-Host "❌ Must run from project root directory" -ForegroundColor Red
    exit 1
}

Set-Location $projectRoot

# Validate command
$validCommands = @("dev", "build", "run", "clean", "help")

switch ($Command) {
    "dev" {
        & "$scriptDir\dev-electron.ps1"
    }
    
    "dev-proxy" {
        & "$scriptDir\dev-with-proxy.ps1"
    }
    
    "build" {
        if ($All) {
            $params = @()
            if ($Clean) { $params += "-Clean" }
            if ($SkipTests) { $params += "-SkipTests" }
            & "$scriptDir\build-electron-all.ps1" @params
        } else {
            $params = @()
            if ($Clean) { $params += "-Clean" }
            if ($PackageOnly) { $params += "-PackageOnly" }
            & "$scriptDir\build-electron.ps1" @params
        }
    }
    
    "run" {
        & "$scriptDir\run-electron.ps1"
    }
    
    "clean" {
        Clean-BuildArtifacts
    }
    
    "help" {
        Show-Help
    }
    
    default {
        Write-Host "❌ Unknown command: $Command" -ForegroundColor Red
        Show-Help
        exit 1
    }
}
