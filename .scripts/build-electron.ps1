#!/usr/bin/env pwsh
# Set UTF-8 encoding for proper emoji support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Electron Production Build Script
# Builds the complete Electron application for production

param(
    [switch]$Clean,
    [switch]$PackageOnly,
    [string]$Platform = "current"
)

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║  🏗️  " -NoNewline -ForegroundColor Blue
Write-Host "ELECTRON PRODUCTION BUILD" -NoNewline -ForegroundColor Green
Write-Host "                                     ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""

# Check if Node.js and npm are available
Write-Host "🔍 " -NoNewline -ForegroundColor Blue
Write-Host "Checking prerequisites..." -ForegroundColor White

if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   💡 " -NoNewline -ForegroundColor Yellow
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Gray
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Node.js and npm found" -ForegroundColor Green

# Clean previous builds if requested
if ($Clean) {
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Red
    Write-Host "│  🧹 CLEANING PREVIOUS BUILDS...       │" -ForegroundColor Red
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Red
    Write-Host ""
    
    $foldersToClean = @("dist", "dist-electron")
    foreach ($folder in $foldersToClean) {
        if (Test-Path $folder) {
            Write-Host "  🗂️  " -NoNewline -ForegroundColor Red
            Write-Host "Removing " -NoNewline -ForegroundColor Gray
            Write-Host "$folder" -NoNewline -ForegroundColor White
            Write-Host "..." -ForegroundColor Gray
            Remove-Item -Recurse -Force $folder
            Write-Host "    ✅ Deleted successfully" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "🎉 " -NoNewline -ForegroundColor Green
    Write-Host "Clean completed!" -ForegroundColor Green
}

# Install dependencies
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Yellow
Write-Host "│  📦 INSTALLING DEPENDENCIES...        │" -ForegroundColor Yellow
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Yellow
Write-Host ""

npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Failed to install dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Dependencies installed successfully!" -ForegroundColor Green

# Run type checking
Write-Host ""
Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Cyan
Write-Host "│  🔍 RUNNING TYPE CHECK...             │" -ForegroundColor Cyan
Write-Host "└────────────────────────────────────────┘" -ForegroundColor Cyan
Write-Host ""

npm run typecheck
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Type check failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ " -NoNewline -ForegroundColor Green
Write-Host "Type check passed!" -ForegroundColor Green

if ($PackageOnly) {
    # Only package without building (assumes dist exists)
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Magenta
    Write-Host "│  📦 PACKAGING ELECTRON APP...         │" -ForegroundColor Magenta
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Magenta
    Write-Host ""
    
    npm run electron:pack
} else {
    # Full build and package
    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Blue
    Write-Host "│  🔨 BUILDING APPLICATION...           │" -ForegroundColor Blue
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Blue
    Write-Host ""
    
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "❌ " -NoNewline -ForegroundColor Red
        Write-Host "Build failed" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ " -NoNewline -ForegroundColor Green
    Write-Host "Application built successfully!" -ForegroundColor Green

    Write-Host ""
    Write-Host "┌────────────────────────────────────────┐" -ForegroundColor Magenta
    Write-Host "│  📦 CREATING ELECTRON DISTRIBUTABLES  │" -ForegroundColor Magenta
    Write-Host "└────────────────────────────────────────┘" -ForegroundColor Magenta
    Write-Host ""
    
    npm run electron:build
}

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ " -NoNewline -ForegroundColor Red
    Write-Host "Electron packaging failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 " -NoNewline -ForegroundColor Green
Write-Host "Electron application built successfully!" -ForegroundColor Green
Write-Host "📂 " -NoNewline -ForegroundColor Cyan
Write-Host "Check the " -NoNewline -ForegroundColor Gray
Write-Host "'dist-electron'" -NoNewline -ForegroundColor White
Write-Host " folder for your distributables" -ForegroundColor Gray

# List created files if the directory exists
if (Test-Path "dist-electron") {
    Write-Host ""
    Write-Host "📋 " -NoNewline -ForegroundColor Cyan
    Write-Host "Created files:" -ForegroundColor White
    Get-ChildItem "dist-electron" -Recurse -File | ForEach-Object {
        $size = [math]::Round($_.Length / 1MB, 2)
        Write-Host "  📄 " -NoNewline -ForegroundColor Blue
        Write-Host "$($_.Name) " -NoNewline -ForegroundColor White
        Write-Host "(${size}MB)" -ForegroundColor Gray
    }
}

Write-Host ""
