// Test script for external API endpoints
const API_KEY = "demo-api-key-12345";
const BASE_URL = "http://localhost:3000/api";

async function testAPI() {
  console.log("🧪 Testing External API endpoints...\n");

  const headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
  };

  try {
    // Test health check (no auth required)
    console.log("1. Testing health check...");
    const healthResponse = await fetch(`${BASE_URL}/ws-health`);
    const healthData = await healthResponse.json();
    console.log(`   Status: ${healthResponse.status}`);
    console.log(`   Data:`, healthData);
    console.log("");

    // Test progress update
    console.log("2. Testing progress update...");
    const progressData = {
      id: "test-progress-001",
      title: "Test File Sync",
      type: "sync",
      progress: 45,
      status: "running",
      startTime: new Date().toISOString(),
      totalFiles: 100,
      processedFiles: 45,
      currentFile: "test-document.pdf"
    };

    const progressResponse = await fetch(`${BASE_URL}/progress`, {
      method: "POST",
      headers,
      body: JSON.stringify(progressData)
    });
    const progressResult = await progressResponse.json();
    console.log(`   Status: ${progressResponse.status}`);
    console.log(`   Response:`, progressResult);
    console.log("");

    // Test event
    console.log("3. Testing event...");
    const eventData = {
      id: `test-event-${Date.now()}`,
      title: "Test Event",
      message: "This is a test event from the API test script",
      type: "success",
      category: "system",
      timestamp: new Date().toISOString(),
      details: "Testing external API integration"
    };

    const eventResponse = await fetch(`${BASE_URL}/events`, {
      method: "POST",
      headers,
      body: JSON.stringify(eventData)
    });
    const eventResult = await eventResponse.json();
    console.log(`   Status: ${eventResponse.status}`);
    console.log(`   Response:`, eventResult);
    console.log("");

    // Test bulk update
    console.log("4. Testing bulk update...");
    const bulkData = {
      progress: [{
        id: "bulk-progress-001",
        title: "Bulk Test Progress",
        type: "upload",
        progress: 75,
        status: "running"
      }],
      events: [{
        id: `bulk-event-${Date.now()}`,
        title: "Bulk Test Event",
        message: "Bulk update test completed",
        type: "info",
        category: "system"
      }]
    };

    const bulkResponse = await fetch(`${BASE_URL}/bulk`, {
      method: "POST",
      headers,
      body: JSON.stringify(bulkData)
    });
    const bulkResult = await bulkResponse.json();
    console.log(`   Status: ${bulkResponse.status}`);
    console.log(`   Response:`, bulkResult);
    console.log("");

    console.log("✅ All tests completed!");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.message.includes("fetch")) {
      console.log("💡 Make sure the server is running: npm start");
    }
  }
}

// Run the test
testAPI();
