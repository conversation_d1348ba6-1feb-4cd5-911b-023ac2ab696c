# 🚀 Development Setup

Complete guide to setting up and developing the W8 File Importer application.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🏗️ Architecture](./ARCHITECTURE.md)
- [🎨 Typography & Theming](./TYPOGRAPHY.md)
- [📡 API Documentation](./API.md)

---

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Project Structure](#project-structure)
- [Environment Configuration](#environment-configuration)
- [Development Commands](#development-commands)
- [Debugging](#debugging)
- [Testing](#testing)
- [Contributing](#contributing)

## Prerequisites

### Required Software

| Tool        | Version     | Purpose                     |
| ----------- | ----------- | --------------------------- |
| **Node.js** | ≥18.0.0     | JavaScript runtime          |
| **npm**     | ≥9.0.0      | Package manager             |
| **Git**     | Latest      | Version control             |
| **VS Code** | Recommended | Code editor with extensions |

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    "yoavbls.pretty-ts-errors"
  ]
}
```

## Getting Started

### 1. Clone the Repository

```bash
git clone <repository-url>
cd w8-file-importer
```

### 2. Install Dependencies

```bash
# Install all dependencies
npm install

# Verify installation
npm run typecheck
```

### 3. Start Development Server

```bash
# Start the development server
npm run dev
```

The application will be available at:

- **Frontend**: http://localhost:8080
- **API**: http://localhost:8080/api/\*
- **WebSocket**: ws://localhost:8080/ws

### 4. Verify Setup

1. Open http://localhost:8080 in your browser
2. You should see the animated splash screen
3. The app should connect within 30 seconds
4. Check the browser console for connection logs

## Development Workflow

### Daily Development Process

```bash
# 1. Pull latest changes
git pull origin main

# 2. Install any new dependencies
npm install

# 3. Start development server
npm run dev

# 4. Make your changes
# ... edit files ...

# 5. Test your changes
npm run typecheck
npm test

# 6. Commit changes
git add .
git commit -m "feat: your feature description"
git push origin your-branch
```

### Hot Reload Features

- **React Components**: Instant updates with state preservation
- **API Routes**: Server automatically restarts on changes
- **WebSocket**: Connection maintained during server restarts
- **Styles**: TailwindCSS updates without page refresh

## Project Structure

### Frontend Structure

```
client/
├── components/           # React components
│   ├── ui/              # Base UI components (Radix + custom)
│   │   ├── button.tsx   # Enhanced button with gradients
│   │   ├── card.tsx     # Glass-effect cards
│   │   └── ...
│   ├── SplashScreen.tsx # Connection splash screen
│   ├── ServiceControl.tsx # WebSocket control panel
│   └── ...
├── hooks/               # Custom React hooks
│   ├── useWebSocket.ts  # WebSocket connection management
│   └── ...
├── pages/               # Route components
│   ├── Index.tsx        # Main dashboard
│   └── NotFound.tsx     # 404 page
├── services/            # Business logic
├── lib/                 # Utilities
└── global.css           # Typography & theme system
```

### Backend Structure

```
server/
├── index.ts             # Server factory and configuration
├── websocket.ts         # WebSocket manager implementation
├── routes/              # API route handlers
│   ├── demo.ts          # Demo endpoints
│   ├── schedules.ts     # Schedule CRUD operations
│   ├── operations.ts    # File operation handlers
│   ├── settings.ts      # Configuration endpoints
│   └── websocket-data.ts # WebSocket data handlers
└── middleware/          # Custom middleware
    └── auth.ts          # API key authentication
```

### Shared Code

```
shared/
├── api.ts               # Shared type definitions
└── ...                  # Other shared utilities
```

## Environment Configuration

### Environment Variables

Create a `.env` file in the root directory:

```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# API Configuration
PING_MESSAGE="Development Server"

# Security (development only)
ALLOWED_ORIGINS="http://localhost:8080,http://127.0.0.1:8080"

# Optional: Custom configuration
API_KEY_VALIDATION=false
DEBUG_WEBSOCKET=true
```

### Configuration Files

#### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["DOM", "DOM.Iterable", "ES2020"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./client/*"],
      "@shared/*": ["./shared/*"]
    }
  }
}
```

## Development Commands

### Core Commands

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server

# Code Quality
npm run typecheck        # TypeScript type checking
npm run format.fix       # Format code with Prettier
npm test                 # Run test suite

# Electron (Desktop App)
npm run dev:electron     # Start Electron development
npm run electron:build   # Build Electron app
```

### Advanced Commands

```bash
# Build Variants
npm run build:client     # Build only frontend
npm run build:server     # Build only backend
npm run build:electron   # Build Electron components

# Electron Packaging
npm run electron:pack    # Package without installer
npm run electron:build   # Full build with installer

# Maintenance
npm run clean            # Clean build artifacts
npm run reinstall        # Fresh dependency install
```

## Debugging

### Browser DevTools

#### WebSocket Debugging

1. Open DevTools (F12)
2. Go to **Network** tab
3. Filter by **WS** (WebSocket)
4. Monitor connection status and messages

#### Console Logging

The application provides detailed console logs:

```javascript
// Connection logs
🚀 Initializing WebSocket manager...
✅ WebSocket connected successfully
🔄 Attempting reconnect 1 in 5s
⚠️ Connection lost - starting 5-minute kill timer
```

### VS Code Debugging

#### Launch Configuration

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/server/index.ts",
      "env": {
        "NODE_ENV": "development"
      },
      "runtimeArgs": ["--loader", "tsx"]
    }
  ]
}
```

### Common Debug Scenarios

#### WebSocket Connection Issues

```bash
# Check server logs
npm run dev
# Look for:
# ✅ WebSocket manager initialized successfully
# 🔗 WebSocket client connected

# Check browser console for:
# ✅ WebSocket connected successfully
# 🏥 WebSocket health check response: {status: "healthy"}
```

#### API Request Issues

```bash
# Test health endpoint
curl http://localhost:8080/api/ws-health

# Expected response:
{
  "status": "healthy",
  "environment": "development",
  "connectedClients": 0,
  "wsManagerAvailable": true,
  "timestamp": "2024-01-XX..."
}
```

## Testing

### Running Tests

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- SplashScreen.test.tsx

# Watch mode
npm run test:watch
```

### Test Structure

```
tests/
├── components/          # Component tests
├── hooks/              # Hook tests
├── services/           # Service tests
├── utils/              # Utility tests
└── integration/        # Integration tests
```

### Writing Tests

```typescript
// Example component test
import { render, screen } from '@testing-library/react';
import { SplashScreen } from '@/components/SplashScreen';

describe('SplashScreen', () => {
  it('shows connection status', () => {
    render(
      <SplashScreen
        isConnected={false}
        docsLoaded={false}
        onLoadComplete={() => {}}
      />
    );

    expect(screen.getByText(/connecting/i)).toBeInTheDocument();
  });
});
```

## Contributing

### Code Style Guidelines

1. **TypeScript**: Use strict mode, avoid `any`
2. **React**: Functional components with hooks
3. **Naming**: Descriptive names, PascalCase for components
4. **File Organization**: Co-locate related files

### Commit Message Convention

```bash
# Format: type(scope): description

feat(websocket): add connection retry logic
fix(ui): resolve button gradient rendering
docs(api): update WebSocket documentation
style(components): format SplashScreen component
```

### Pull Request Process

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/your-feature`
3. **Make** your changes with tests
4. **Ensure** all checks pass: `npm run typecheck && npm test`
5. **Submit** a pull request with description

### Development Standards

- **Type Safety**: All new code must be typed
- **Testing**: New features require tests
- **Documentation**: Update docs for API changes
- **Performance**: Consider WebSocket message frequency

---

## 🔗 Next Steps

- **[Typography System](./TYPOGRAPHY.md)** - Learn about the design system
- **[API Documentation](./API.md)** - Explore the WebSocket API
- **[Architecture Guide](./ARCHITECTURE.md)** - Understand system design
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Common development issues

---

_Having trouble with setup? Check the [Troubleshooting Guide](./TROUBLESHOOTING.md) or review the [Architecture Documentation](./ARCHITECTURE.md)._
