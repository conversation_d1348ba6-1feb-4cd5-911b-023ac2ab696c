# 📡 API Documentation

Complete reference for the W8 File Importer REST API and WebSocket communication system.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🏗️ Architecture](./ARCHITECTURE.md)
- [🚀 Development Setup](./DEVELOPMENT.md)
- [🎨 Typography & Theming](./TYPOGRAPHY.md)

---

## 📋 Table of Contents

- [API Overview](#api-overview)
- [Authentication](#authentication)
- [HTTP Endpoints](#http-endpoints)
- [WebSocket API](#websocket-api)
- [Data Models](#data-models)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Examples](#examples)
- [Monitoring](#monitoring)

## API Overview

The W8 File Importer API is built on a **WebSocket-first architecture** where HTTP endpoints trigger operations and WebSocket provides real-time updates.

### Base URLs

| Environment     | HTTP Base URL                 | WebSocket URL              |
| --------------- | ----------------------------- | -------------------------- |
| **Development** | `http://localhost:8080/api`   | `ws://localhost:8080/ws`   |
| **Production**  | `https://your-domain.com/api` | `wss://your-domain.com/ws` |

### API Characteristics

- **Real-Time**: All operations provide live progress via WebSocket
- **Type-Safe**: Full TypeScript definitions available
- **Authenticated**: API key required for write operations
- **RESTful**: Standard HTTP methods and status codes
- **JSON**: All requests and responses use JSON format

## Authentication

### API Key Authentication

External applications require API keys for write operations. Read operations (health checks) are public.

#### Header Format

```http
X-API-Key: your-api-key-here
Content-Type: application/json
```

#### Example Request

```bash
curl -X POST "http://localhost:8080/api/progress" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "upload_123",
    "title": "File Upload",
    "progress": 50,
    "status": "running"
  }'
```

### Rate Limiting

- **Per API Key**: 100 requests per minute
- **Global**: 1000 requests per minute
- **WebSocket**: 10 messages per second per connection

## HTTP Endpoints

### Health & Monitoring

#### GET `/api/ws-health`

Get WebSocket service health status.

**Authentication**: None required

**Response**:

```json
{
  "status": "healthy",
  "environment": "development",
  "connectedClients": 2,
  "wsManagerAvailable": true,
  "timestamp": "2024-01-XX...",
  "details": "WebSocket manager available"
}
```

#### GET `/api/ping`

Simple ping endpoint for basic connectivity testing.

**Authentication**: None required

**Response**:

```json
{
  "message": "ping"
}
```

### Progress Updates

#### POST `/api/progress`

Update or create a progress item.

**Authentication**: Required

**Request Body**:

```json
{
  "id": "string",
  "title": "string",
  "type": "sync" | "upload" | "download",
  "progress": 0-100,
  "status": "running" | "completed" | "failed" | "paused",
  "startTime": "ISO8601",
  "estimatedCompletion": "ISO8601",
  "currentFile": "string",
  "totalFiles": 100,
  "processedFiles": 45
}
```

**Response**:

```json
{
  "success": true,
  "message": "Progress updated successfully"
}
```

**WebSocket Broadcast**: Sends `progress` message to all connected clients.

### Event Logging

#### POST `/api/events`

Create a new event log entry.

**Authentication**: Required

**Request Body**:

```json
{
  "id": "string",
  "title": "string",
  "message": "string",
  "type": "success" | "error" | "warning" | "info",
  "category": "sync" | "upload" | "download" | "system" | "auth",
  "timestamp": "ISO8601",
  "clientPath": "string",
  "details": "string"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Event logged successfully"
}
```

**WebSocket Broadcast**: Sends `event` message to all connected clients.

### Bulk Operations

#### POST `/api/bulk`

Submit multiple updates in a single request for efficiency.

**Authentication**: Required

**Request Body**:

```json
{
  "progress": [
    {
      "id": "upload_1",
      "title": "File 1",
      "progress": 100,
      "status": "completed"
    }
  ],
  "events": [
    {
      "id": "event_1",
      "title": "Upload Complete",
      "message": "File uploaded successfully",
      "type": "success",
      "category": "upload"
    }
  ]
}
```

**Response**:

```json
{
  "success": true,
  "processed": {
    "progress": 1,
    "events": 1
  }
}
```

### Schedule Management

#### GET `/api/schedules`

Retrieve all schedules with optional pagination.

**Authentication**: None required

**Query Parameters**:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status

**Response**:

```json
{
  "schedules": [
    {
      "id": "schedule_123",
      "name": "Daily Sync",
      "folder": "/data/source",
      "destination": "/data/backup",
      "frequency": "daily",
      "nextRun": "2024-01-XX...",
      "lastRun": "2024-01-XX...",
      "status": "active",
      "filesCount": 1250,
      "isRunning": false
    }
  ],
  "total": 5,
  "page": 1,
  "totalPages": 1
}
```

#### POST `/api/schedules`

Create a new schedule.

**Authentication**: Required

**Request Body**:

```json
{
  "name": "Weekly Backup",
  "folder": "/data/source",
  "destination": "/data/backup",
  "frequency": "weekly",
  "isActive": true,
  "options": {
    "preserveTimestamps": true,
    "excludePatterns": ["*.tmp", "*.log"]
  }
}
```

**Response**:

```json
{
  "message": "Schedule created successfully",
  "schedule": {
    "id": "schedule_456",
    "name": "Weekly Backup"
    // ... full schedule object
  }
}
```

#### GET `/api/schedules/:id`

Retrieve a specific schedule by ID.

#### PUT `/api/schedules/:id`

Update an existing schedule.

#### DELETE `/api/schedules/:id`

Delete a schedule.

#### PATCH `/api/schedules/:id/toggle`

Toggle schedule active/inactive status.

#### POST `/api/schedules/:id/execute`

Manually execute a schedule immediately.

## WebSocket API

### Connection

#### Endpoint

```
ws://localhost:8080/ws
```

#### Connection Lifecycle

1. **Health Check**: Client checks `/api/ws-health`
2. **WebSocket Upgrade**: Client initiates WebSocket connection
3. **Initial Data**: Server sends current progress and events
4. **Real-Time Updates**: Bidirectional message exchange

#### Connection Management

```javascript
const ws = new WebSocket("ws://localhost:8080/ws");

ws.onopen = () => {
  console.log("Connected to WebSocket");
  // Request initial data
  ws.send(JSON.stringify({ type: "request-initial-data" }));
};

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  handleMessage(message);
};

ws.onclose = (event) => {
  console.log("Connection closed:", event.code, event.reason);
  // Implement reconnection logic
};
```

### Message Protocol

All WebSocket messages follow this structure:

```typescript
interface WebSocketMessage {
  type:
    | "progress"
    | "event"
    | "notification"
    | "schedule"
    | "service-status"
    | "system-metrics";
  data?: any;
  timestamp?: string;
}
```

### Incoming Messages (Server → Client)

#### Progress Updates

```json
{
  "type": "progress",
  "data": {
    "id": "upload_123",
    "title": "File Upload",
    "type": "upload",
    "progress": 75,
    "status": "running",
    "startTime": "2024-01-XX...",
    "currentFile": "document.pdf",
    "totalFiles": 10,
    "processedFiles": 7
  },
  "timestamp": "2024-01-XX..."
}
```

#### Event Notifications

```json
{
  "type": "event",
  "data": {
    "id": "event_456",
    "title": "Sync Complete",
    "message": "Successfully synchronized 150 files",
    "type": "success",
    "category": "sync",
    "timestamp": "2024-01-XX...",
    "clientPath": "/data/documents"
  }
}
```

#### System Metrics

```json
{
  "type": "system-metrics",
  "data": {
    "cpu": 45,
    "memory": 60,
    "storage": 80,
    "uptime": "2h 35m"
  }
}
```

#### Service Status

```json
{
  "type": "service-status",
  "data": {
    "status": "running"
  }
}
```

#### System Ready

```json
{
  "type": "system-ready"
}
```

### Outgoing Messages (Client → Server)

#### Service Commands

```json
{
  "type": "service-command",
  "command": "start" | "stop" | "pause" | "resume" | "restart" | "shutdown"
}
```

#### Request Initial Data

```json
{
  "type": "request-initial-data"
}
```

#### Create Schedule

```json
{
  "type": "create-schedule",
  "data": {
    "name": "Daily Backup",
    "folder": "/data/source",
    "destination": "/data/backup"
  }
}
```

#### Mark Notification Read

```json
{
  "type": "mark-notification-read",
  "data": {
    "id": "notification_123"
  }
}
```

## Data Models

### Progress Item

```typescript
interface ProgressItem {
  id: string;
  title: string;
  type: "sync" | "upload" | "download";
  progress: number; // 0-100
  status: "running" | "completed" | "failed" | "paused";
  startTime: Date;
  estimatedCompletion?: Date;
  currentFile?: string;
  totalFiles?: number;
  processedFiles?: number;
}
```

### Event Log Item

```typescript
interface EventLogItem {
  id: string;
  title: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  category: "sync" | "upload" | "download" | "system" | "auth";
  timestamp: Date;
  clientPath?: string;
  details?: string;
}
```

### Schedule

```typescript
interface Schedule {
  id: string;
  name: string;
  folder: string;
  destination: string;
  frequency: string;
  nextRun: Date;
  lastRun: Date;
  status: "active" | "paused" | "error";
  filesCount: number;
  isRunning: boolean;
}
```

### System Metrics

```typescript
interface SystemMetrics {
  cpu: number; // 0-100
  memory: number; // 0-100
  storage: number; // 0-100
  uptime: string; // "2h 35m"
}
```

## Error Handling

### HTTP Error Responses

```json
{
  "error": "Invalid API key",
  "code": "AUTH_INVALID",
  "timestamp": "2024-01-XX...",
  "details": {
    "field": "x-api-key",
    "expected": "valid API key"
  }
}
```

### Common Error Codes

| HTTP Status | Code                  | Description                     |
| ----------- | --------------------- | ------------------------------- |
| `400`       | `VALIDATION_ERROR`    | Invalid request data            |
| `401`       | `AUTH_INVALID`        | Invalid or missing API key      |
| `429`       | `RATE_LIMIT`          | Rate limit exceeded             |
| `500`       | `INTERNAL_ERROR`      | Server error                    |
| `503`       | `SERVICE_UNAVAILABLE` | WebSocket manager not available |

### WebSocket Errors

WebSocket errors are handled through connection events:

```javascript
ws.onerror = (error) => {
  console.error("WebSocket error:", error);
  // Implement error handling
};

ws.onclose = (event) => {
  if (event.code !== 1000) {
    console.error("Unexpected close:", event.code, event.reason);
    // Implement reconnection logic
  }
};
```

## Rate Limiting

### Limits

- **HTTP API**: 100 requests per minute per API key
- **WebSocket**: 10 messages per second per connection
- **Global HTTP**: 1000 requests per minute

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

### Rate Limit Response

```json
{
  "error": "Rate limit exceeded",
  "code": "RATE_LIMIT",
  "retryAfter": 60
}
```

## Examples

### Complete Integration Example

```javascript
class W8FileImporterClient {
  constructor(apiKey, baseUrl = "http://localhost:8080") {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.ws = null;
    this.isConnected = false;
  }

  async connect() {
    // Check health first
    const health = await fetch(`${this.baseUrl}/api/ws-health`);
    const healthData = await health.json();

    if (healthData.status !== "healthy") {
      throw new Error("Service not healthy");
    }

    // Connect WebSocket
    const wsUrl = this.baseUrl.replace("http", "ws") + "/ws";
    this.ws = new WebSocket(wsUrl);

    return new Promise((resolve, reject) => {
      this.ws.onopen = () => {
        this.isConnected = true;
        this.ws.send(JSON.stringify({ type: "request-initial-data" }));
        resolve();
      };

      this.ws.onerror = reject;

      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      };
    });
  }

  async updateProgress(progressData) {
    const response = await fetch(`${this.baseUrl}/api/progress`, {
      method: "POST",
      headers: {
        "X-API-Key": this.apiKey,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(progressData),
    });

    return response.json();
  }

  handleMessage(message) {
    switch (message.type) {
      case "progress":
        console.log("Progress update:", message.data);
        break;
      case "event":
        console.log("New event:", message.data);
        break;
      case "system-metrics":
        console.log("System metrics:", message.data);
        break;
    }
  }
}

// Usage
const client = new W8FileImporterClient("your-api-key");
await client.connect();

// Update progress
await client.updateProgress({
  id: "upload_123",
  title: "Uploading Files",
  progress: 50,
  status: "running",
  type: "upload",
});
```

### File Upload with Progress

```javascript
async function uploadFileWithProgress(file, client) {
  const uploadId = `upload_${Date.now()}`;

  // Start progress tracking
  await client.updateProgress({
    id: uploadId,
    title: `Uploading ${file.name}`,
    progress: 0,
    status: "running",
    type: "upload",
    totalFiles: 1,
    processedFiles: 0,
    currentFile: file.name,
  });

  // Simulate upload progress
  for (let progress = 0; progress <= 100; progress += 10) {
    await new Promise((resolve) => setTimeout(resolve, 200));

    await client.updateProgress({
      id: uploadId,
      progress,
      status: progress === 100 ? "completed" : "running",
      processedFiles: progress === 100 ? 1 : 0,
    });
  }

  // Log completion event
  await fetch(`${client.baseUrl}/api/events`, {
    method: "POST",
    headers: {
      "X-API-Key": client.apiKey,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id: `event_${Date.now()}`,
      title: "Upload Complete",
      message: `Successfully uploaded ${file.name}`,
      type: "success",
      category: "upload",
      timestamp: new Date().toISOString(),
    }),
  });
}
```

## Monitoring

### Health Monitoring

Monitor service health with regular checks:

```bash
# Check service health
curl http://localhost:8080/api/ws-health

# Monitor WebSocket connections
curl http://localhost:8080/api/ws-health | jq '.connectedClients'
```

### Metrics Collection

The API provides system metrics via WebSocket:

```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);

  if (message.type === "system-metrics") {
    const { cpu, memory, storage, uptime } = message.data;

    // Send to monitoring system
    sendMetrics({
      "system.cpu": cpu,
      "system.memory": memory,
      "system.storage": storage,
      "system.uptime": uptime,
    });
  }
};
```

### Performance Monitoring

```javascript
// Track API response times
const startTime = performance.now();
const response = await fetch("/api/progress", options);
const responseTime = performance.now() - startTime;

console.log(`API response time: ${responseTime}ms`);
```

---

## 🔗 Related Documentation

- **[Architecture Guide](./ARCHITECTURE.md)** - System design and WebSocket implementation
- **[Development Setup](./DEVELOPMENT.md)** - API development environment
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Common API issues and solutions

---

_Looking for more examples? Check the [Development Guide](./DEVELOPMENT.md) for local testing setup._
