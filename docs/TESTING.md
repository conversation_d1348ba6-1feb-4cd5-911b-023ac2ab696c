# 🧪 Testing Guide

Comprehensive testing strategies and procedures for the W8 File Importer application.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🏗️ Architecture](./ARCHITECTURE.md)
- [🚀 Development Setup](./DEVELOPMENT.md)
- [📡 API Documentation](./API.md)
- [🔧 Troubleshooting](./TROUBLESHOOTING.md)
- [📖 User Manual](./USER_MANUAL.md)
- [🚀 Deployment](./DEPLOYMENT.md)

---

## 📋 Table of Contents

- [Testing Overview](#testing-overview)
- [Test Environment Setup](#test-environment-setup)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [API Testing](#api-testing)
- [WebSocket Testing](#websocket-testing)
- [Performance Testing](#performance-testing)
- [Security Testing](#security-testing)
- [Manual Testing Procedures](#manual-testing-procedures)
- [Continuous Integration](#continuous-integration)
- [Test Data Management](#test-data-management)

## Testing Overview

### Testing Strategy

The W8 File Importer employs a multi-layered testing approach:

1. **Unit Tests**: Component and utility function testing
2. **Integration Tests**: API endpoint and service integration
3. **End-to-End Tests**: Complete user workflow testing
4. **Performance Tests**: Load and stress testing
5. **Security Tests**: Vulnerability and penetration testing

### Testing Stack

**Frontend Testing:**

- **Vitest**: Unit test runner
- **React Testing Library**: Component testing
- **MSW**: API mocking
- **Playwright**: End-to-end testing

**Backend Testing:**

- **Jest**: Test framework
- **Supertest**: HTTP assertion library
- **ws**: WebSocket testing utilities
- **Artillery**: Load testing

### Test Structure

```
tests/
├── unit/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   └── services/
├── integration/
│   ├── api/
│   ├── websocket/
│   └── database/
├── e2e/
│   ├── user-flows/
│   ├── regression/
│   └── accessibility/
├── performance/
│   ├── load/
│   ├── stress/
│   └── endurance/
└── fixtures/
    ├── data/
    ├── files/
    └── mocks/
```

## Test Environment Setup

### Development Environment

```bash
# Install test dependencies
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom
npm install --save-dev playwright msw jest supertest

# Initialize test configuration
npm run test:setup
```

### Test Configuration Files

**Vitest Configuration (`vitest.config.ts`):**

```typescript
import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./tests/setup.ts"],
    include: ["tests/unit/**/*.{test,spec}.{ts,tsx}"],
    coverage: {
      reporter: ["text", "json", "html"],
      exclude: ["node_modules/", "tests/", "**/*.d.ts", "**/*.config.{ts,js}"],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./client"),
      "@shared": path.resolve(__dirname, "./shared"),
    },
  },
});
```

**Test Setup (`tests/setup.ts`):**

```typescript
import "@testing-library/jest-dom";
import { setupServer } from "msw/node";
import { rest } from "msw";

// Mock API server
export const server = setupServer(
  rest.get("/api/ws-health", (req, res, ctx) => {
    return res(
      ctx.json({
        status: "healthy",
        wsManagerAvailable: true,
        connectedClients: 0,
      }),
    );
  }),
);

// Establish API mocking before all tests
beforeAll(() => server.listen());

// Reset handlers after each test
afterEach(() => server.resetHandlers());

// Clean up after tests
afterAll(() => server.close());

// Mock WebSocket
global.WebSocket = class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  url: string;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      this.onopen?.(new Event("open"));
    }, 100);
  }

  send(data: string) {
    console.log("Mock WebSocket send:", data);
  }

  close() {
    this.readyState = MockWebSocket.CLOSED;
    this.onclose?.(new CloseEvent("close"));
  }
};
```

## Unit Testing

### Component Testing

**Example: SplashScreen Component Test**

```typescript
// tests/unit/components/SplashScreen.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { SplashScreen } from '@/components/SplashScreen';
import { useWebSocket } from '@/hooks/useWebSocket';

// Mock the useWebSocket hook
vi.mock('@/hooks/useWebSocket');

describe('SplashScreen', () => {
  const mockUseWebSocket = vi.mocked(useWebSocket);

  beforeEach(() => {
    mockUseWebSocket.mockReturnValue({
      isConnected: false,
      isConnecting: true,
      error: null,
      connect: vi.fn(),
      disconnect: vi.fn(),
      send: vi.fn(),
    });
  });

  it('should display connecting state', () => {
    render(<SplashScreen />);

    expect(screen.getByText('Connecting...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('should display connection error', () => {
    mockUseWebSocket.mockReturnValue({
      isConnected: false,
      isConnecting: false,
      error: 'Connection failed',
      connect: vi.fn(),
      disconnect: vi.fn(),
      send: vi.fn(),
    });

    render(<SplashScreen />);

    expect(screen.getByText('CONNECTION REQUIRED')).toBeInTheDocument();
    expect(screen.getByText(/Connection failed/)).toBeInTheDocument();
  });

  it('should show timeout countdown', async () => {
    render(<SplashScreen />);

    await waitFor(() => {
      expect(screen.getByText(/Time remaining:/)).toBeInTheDocument();
    });
  });

  it('should display fatal error after timeout', async () => {
    vi.useFakeTimers();

    render(<SplashScreen />);

    // Fast-forward 5 minutes
    vi.advanceTimersByTime(5 * 60 * 1000);

    await waitFor(() => {
      expect(screen.getByText('FATAL ERROR')).toBeInTheDocument();
    });

    vi.useRealTimers();
  });
});
```

### Hook Testing

**Example: useWebSocket Hook Test**

```typescript
// tests/unit/hooks/useWebSocket.test.ts
import { renderHook, act } from "@testing-library/react";
import { useWebSocket } from "@/hooks/useWebSocket";

describe("useWebSocket", () => {
  beforeEach(() => {
    vi.clearAllTimers();
  });

  it("should initialize with disconnected state", () => {
    const { result } = renderHook(() => useWebSocket());

    expect(result.current.isConnected).toBe(false);
    expect(result.current.isConnecting).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it("should connect to WebSocket", async () => {
    const { result } = renderHook(() => useWebSocket());

    act(() => {
      result.current.connect();
    });

    expect(result.current.isConnecting).toBe(true);

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });
  });

  it("should handle connection timeout", async () => {
    vi.useFakeTimers();

    const { result } = renderHook(() => useWebSocket());

    act(() => {
      result.current.connect();
    });

    // Advance timer beyond connection timeout
    act(() => {
      vi.advanceTimersByTime(31000); // 31 seconds
    });

    await waitFor(() => {
      expect(result.current.error).toContain("timeout");
    });

    vi.useRealTimers();
  });
});
```

### Utility Function Testing

**Example: File Upload Service Test**

```typescript
// tests/unit/services/fileUploadService.test.ts
import { uploadFile, validateFileType } from "@/services/fileUploadService";

describe("fileUploadService", () => {
  describe("validateFileType", () => {
    it("should accept valid file types", () => {
      const validFiles = [
        new File(["content"], "test.pdf", { type: "application/pdf" }),
        new File(["content"], "test.txt", { type: "text/plain" }),
        new File(["content"], "test.jpg", { type: "image/jpeg" }),
      ];

      validFiles.forEach((file) => {
        expect(validateFileType(file)).toBe(true);
      });
    });

    it("should reject invalid file types", () => {
      const invalidFile = new File(["content"], "test.exe", {
        type: "application/x-msdownload",
      });

      expect(validateFileType(invalidFile)).toBe(false);
    });
  });

  describe("uploadFile", () => {
    it("should upload file successfully", async () => {
      const file = new File(["content"], "test.txt", { type: "text/plain" });
      const onProgress = vi.fn();

      const result = await uploadFile(file, onProgress);

      expect(result.success).toBe(true);
      expect(onProgress).toHaveBeenCalled();
    });

    it("should handle upload errors", async () => {
      // Mock fetch to return error
      global.fetch = vi.fn().mockRejectedValue(new Error("Upload failed"));

      const file = new File(["content"], "test.txt", { type: "text/plain" });

      const result = await uploadFile(file);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Upload failed");
    });
  });
});
```

### Running Unit Tests

```bash
# Run all unit tests
npm run test:unit

# Run tests in watch mode
npm run test:unit:watch

# Run tests with coverage
npm run test:unit:coverage

# Run specific test file
npm run test:unit -- SplashScreen.test.tsx

# Run tests matching pattern
npm run test:unit -- --grep "WebSocket"
```

## Integration Testing

### API Integration Tests

**Example: WebSocket Health Endpoint Test**

```typescript
// tests/integration/api/websocket-health.test.ts
import request from "supertest";
import { createServer } from "../../../server";

describe("WebSocket Health API", () => {
  let app: Express.Application;

  beforeAll(() => {
    app = createServer();
  });

  it("should return healthy status", async () => {
    const response = await request(app).get("/api/ws-health").expect(200);

    expect(response.body).toEqual({
      status: "healthy",
      wsManagerAvailable: expect.any(Boolean),
      connectedClients: expect.any(Number),
      environment: "test",
    });
  });

  it("should handle server errors gracefully", async () => {
    // Mock WebSocket manager to throw error
    vi.mock("../../../server/websocket", () => ({
      getWebSocketManager: () => {
        throw new Error("WebSocket manager unavailable");
      },
    }));

    const response = await request(app).get("/api/ws-health").expect(500);

    expect(response.body.status).toBe("unhealthy");
  });
});
```

### Database Integration Tests

```typescript
// tests/integration/database/operations.test.ts
import { Operation } from "@shared/api";
import { OperationStore } from "../../../server/stores/operationStore";

describe("Operation Store Integration", () => {
  let store: OperationStore;

  beforeEach(async () => {
    store = new OperationStore();
    await store.initialize();
  });

  afterEach(async () => {
    await store.cleanup();
  });

  it("should create and retrieve operation", async () => {
    const operation: Operation = {
      id: "test-op-1",
      title: "Test Operation",
      progress: 0,
      status: "pending",
      type: "upload",
    };

    await store.create(operation);
    const retrieved = await store.getById("test-op-1");

    expect(retrieved).toEqual(operation);
  });

  it("should update operation progress", async () => {
    const operation: Operation = {
      id: "test-op-2",
      title: "Test Operation",
      progress: 0,
      status: "pending",
      type: "upload",
    };

    await store.create(operation);
    await store.updateProgress("test-op-2", 50);

    const updated = await store.getById("test-op-2");
    expect(updated?.progress).toBe(50);
  });
});
```

## End-to-End Testing

### Playwright Configuration

**Playwright Config (`playwright.config.ts`):**

```typescript
import { defineConfig, devices } from "@playwright/test";

export default defineConfig({
  testDir: "./tests/e2e",
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [["html", { outputFolder: "test-results/playwright-report" }]],
  use: {
    baseURL: "http://localhost:8080",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
  },

  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
    {
      name: "firefox",
      use: { ...devices["Desktop Firefox"] },
    },
    {
      name: "webkit",
      use: { ...devices["Desktop Safari"] },
    },
    {
      name: "Mobile Chrome",
      use: { ...devices["Pixel 5"] },
    },
  ],

  webServer: {
    command: "npm run dev",
    url: "http://localhost:8080",
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples

**Connection Flow Test:**

```typescript
// tests/e2e/connection-flow.spec.ts
import { test, expect } from "@playwright/test";

test.describe("Connection Flow", () => {
  test("should establish connection and load dashboard", async ({ page }) => {
    await page.goto("/");

    // Should show splash screen initially
    await expect(page.getByText("Connecting...")).toBeVisible();

    // Should connect within timeout
    await expect(page.getByText("Dashboard")).toBeVisible({ timeout: 10000 });

    // Should show connection status
    const status = page.locator('[data-testid="connection-status"]');
    await expect(status).toHaveClass(/connected/);
  });

  test("should handle connection failure gracefully", async ({ page }) => {
    // Block WebSocket connections
    await page.route("**/ws", (route) => route.abort());

    await page.goto("/");

    // Should show connection error
    await expect(page.getByText("CONNECTION REQUIRED")).toBeVisible({
      timeout: 15000,
    });

    // Should show retry option
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
  });

  test("should timeout after 5 minutes", async ({ page }) => {
    // Mock slow connection
    await page.route("**/ws", (route) => {
      return new Promise(() => {}); // Never resolve
    });

    await page.goto("/");

    // Should show timeout error after 5 minutes
    await expect(page.getByText("FATAL ERROR")).toBeVisible({
      timeout: 5 * 60 * 1000 + 1000,
    });
  });
});
```

**File Upload Flow Test:**

```typescript
// tests/e2e/file-upload-flow.spec.ts
import { test, expect } from "@playwright/test";
import path from "path";

test.describe("File Upload Flow", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
    await expect(page.getByText("Dashboard")).toBeVisible({ timeout: 10000 });
  });

  test("should upload file successfully", async ({ page }) => {
    // Navigate to folder
    await page.getByText("Folders").click();
    await expect(page.getByText("Folder Management")).toBeVisible();

    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(
      path.join(__dirname, "../fixtures/test-file.txt"),
    );

    // Verify upload progress
    await expect(page.getByText("Uploading...")).toBeVisible();
    await expect(page.getByText("Upload complete")).toBeVisible({
      timeout: 30000,
    });

    // Verify file appears in list
    await expect(page.getByText("test-file.txt")).toBeVisible();
  });

  test("should handle upload errors", async ({ page }) => {
    // Mock upload failure
    await page.route("**/api/upload", (route) => {
      route.fulfill({ status: 500, body: "Upload failed" });
    });

    await page.getByText("Folders").click();

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(
      path.join(__dirname, "../fixtures/test-file.txt"),
    );

    // Should show error message
    await expect(page.getByText("Upload failed")).toBeVisible();

    // Should offer retry option
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
  });
});
```

## API Testing

### REST API Testing

```typescript
// tests/integration/api/operations.test.ts
import request from "supertest";
import { createServer } from "../../../server";

describe("Operations API", () => {
  let app: Express.Application;

  beforeAll(() => {
    app = createServer();
  });

  describe("POST /api/progress", () => {
    it("should create new operation", async () => {
      const operation = {
        id: "test-op",
        title: "Test Operation",
        progress: 0,
        status: "running",
        type: "upload",
      };

      const response = await request(app)
        .post("/api/progress")
        .send(operation)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it("should validate required fields", async () => {
      const invalidOperation = {
        title: "Test Operation",
        // Missing required fields
      };

      await request(app)
        .post("/api/progress")
        .send(invalidOperation)
        .expect(400);
    });

    it("should require authentication", async () => {
      const operation = {
        id: "test-op",
        title: "Test Operation",
        progress: 0,
        status: "running",
        type: "upload",
      };

      await request(app).post("/api/progress").send(operation).expect(401);
    });
  });

  describe("GET /api/operations", () => {
    it("should return operations list", async () => {
      const response = await request(app)
        .get("/api/operations")
        .set("X-API-Key", "test-key")
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it("should support pagination", async () => {
      const response = await request(app)
        .get("/api/operations?page=1&limit=10")
        .set("X-API-Key", "test-key")
        .expect(200);

      expect(response.body).toHaveProperty("data");
      expect(response.body).toHaveProperty("pagination");
    });
  });
});
```

## WebSocket Testing

```typescript
// tests/integration/websocket/connection.test.ts
import WebSocket from "ws";
import { createServer } from "../../../server";

describe("WebSocket Connection", () => {
  let server: any;
  let ws: WebSocket;

  beforeAll((done) => {
    server = createServer().listen(0, done);
  });

  afterAll((done) => {
    server.close(done);
  });

  beforeEach(() => {
    const port = server.address().port;
    ws = new WebSocket(`ws://localhost:${port}/ws`);
  });

  afterEach(() => {
    ws.close();
  });

  it("should establish WebSocket connection", (done) => {
    ws.on("open", () => {
      expect(ws.readyState).toBe(WebSocket.OPEN);
      done();
    });
  });

  it("should receive initial data", (done) => {
    ws.on("open", () => {
      ws.send(JSON.stringify({ type: "request-initial-data" }));
    });

    ws.on("message", (data) => {
      const message = JSON.parse(data.toString());
      expect(message.type).toBe("initial-data");
      done();
    });
  });

  it("should handle operation updates", (done) => {
    const operation = {
      id: "test-op",
      title: "Test Operation",
      progress: 50,
      status: "running",
      type: "upload",
    };

    ws.on("open", () => {
      ws.send(
        JSON.stringify({
          type: "operation-update",
          data: operation,
        }),
      );
    });

    ws.on("message", (data) => {
      const message = JSON.parse(data.toString());
      if (message.type === "operation-updated") {
        expect(message.data).toEqual(operation);
        done();
      }
    });
  });
});
```

## Performance Testing

### Load Testing with Artillery

**Artillery Configuration (`artillery.yml`):**

```yaml
config:
  target: "http://localhost:8080"
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Stress test"
  processor: "./tests/performance/processor.js"

scenarios:
  - name: "API Health Check"
    weight: 30
    flow:
      - get:
          url: "/api/ws-health"
          capture:
            - json: "$.status"
              as: "healthStatus"

  - name: "WebSocket Connection"
    weight: 40
    engine: ws
    flow:
      - connect:
          target: "ws://localhost:8080/ws"
      - send: '{"type": "request-initial-data"}'
      - think: 5
      - send: '{"type": "ping"}'

  - name: "File Upload"
    weight: 30
    flow:
      - post:
          url: "/api/upload"
          formData:
            file: "@./tests/fixtures/test-file.txt"
          headers:
            X-API-Key: "test-key"
```

### Performance Test Scripts

```bash
# Run load tests
npm run test:performance

# Run specific test scenario
artillery run tests/performance/load-test.yml

# Generate performance report
artillery run --output performance-report.json tests/performance/load-test.yml
artillery report performance-report.json
```

## Security Testing

### Vulnerability Scanning

```typescript
// tests/security/vulnerability.test.ts
import request from "supertest";
import { createServer } from "../../server";

describe("Security Tests", () => {
  let app: Express.Application;

  beforeAll(() => {
    app = createServer();
  });

  it("should prevent SQL injection", async () => {
    const maliciousInput = "'; DROP TABLE operations; --";

    await request(app)
      .get(`/api/operations?search=${encodeURIComponent(maliciousInput)}`)
      .set("X-API-Key", "test-key")
      .expect(200);

    // Verify database is still intact
    const response = await request(app)
      .get("/api/operations")
      .set("X-API-Key", "test-key")
      .expect(200);

    expect(Array.isArray(response.body)).toBe(true);
  });

  it("should prevent XSS attacks", async () => {
    const xssPayload = '<script>alert("XSS")</script>';

    const response = await request(app)
      .post("/api/progress")
      .send({
        id: "test-xss",
        title: xssPayload,
        progress: 0,
        status: "running",
        type: "upload",
      })
      .set("X-API-Key", "test-key")
      .expect(200);

    // Verify content is escaped
    expect(response.body.title).not.toContain("<script>");
  });

  it("should enforce rate limiting", async () => {
    const requests = Array(101)
      .fill(null)
      .map(() =>
        request(app).get("/api/ws-health").set("X-API-Key", "test-key"),
      );

    const responses = await Promise.all(requests);
    const rateLimitedResponses = responses.filter((r) => r.status === 429);

    expect(rateLimitedResponses.length).toBeGreaterThan(0);
  });
});
```

## Manual Testing Procedures

### User Acceptance Testing Checklist

**Connection Testing:**

- [ ] Application loads within 10 seconds
- [ ] WebSocket connection establishes successfully
- [ ] Connection status indicators work correctly
- [ ] Timeout behavior functions as expected
- [ ] Reconnection attempts work properly

**File Upload Testing:**

- [ ] Single file upload works
- [ ] Multiple file upload works
- [ ] Large file upload (>10MB) works
- [ ] Upload progress is accurate
- [ ] Error handling works for failed uploads
- [ ] File type validation works

**Folder Management Testing:**

- [ ] Folder creation works
- [ ] Folder navigation works
- [ ] Folder synchronization works
- [ ] Permission management works
- [ ] Folder deletion works

**Theme and UI Testing:**

- [ ] All 10 themes apply correctly
- [ ] Typography scales properly
- [ ] Responsive design works on mobile
- [ ] Dark mode accessibility is maintained
- [ ] Theme persistence works across sessions

### Browser Compatibility Testing

**Supported Browsers:**

- Chrome 90+ ✓
- Firefox 88+ ✓
- Safari 14+ ✓
- Edge 90+ ✓

**Test Procedures:**

1. Open application in each browser
2. Test core functionality
3. Verify WebSocket connections
4. Test file upload/download
5. Verify theme switching
6. Check responsive behavior

### Accessibility Testing

```typescript
// tests/accessibility/a11y.test.ts
import { test, expect } from "@playwright/test";
import AxeBuilder from "@axe-core/playwright";

test.describe("Accessibility Tests", () => {
  test("should not have accessibility violations", async ({ page }) => {
    await page.goto("/");
    await expect(page.getByText("Dashboard")).toBeVisible({ timeout: 10000 });

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test("should support keyboard navigation", async ({ page }) => {
    await page.goto("/");
    await expect(page.getByText("Dashboard")).toBeVisible({ timeout: 10000 });

    // Test tab navigation
    await page.keyboard.press("Tab");
    await expect(page.locator(":focus")).toBeVisible();

    // Test Enter key activation
    await page.keyboard.press("Enter");
    // Verify appropriate action occurred
  });
});
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:unit:coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run integration tests
        run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run E2E tests
        run: npm run test:e2e

      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: test-results/playwright-report/
```

## Test Data Management

### Test Fixtures

```typescript
// tests/fixtures/operations.ts
export const mockOperations = [
  {
    id: "op-1",
    title: "Sample Upload",
    progress: 75,
    status: "running",
    type: "upload",
    startTime: new Date("2024-01-01T10:00:00Z"),
    files: ["file1.txt", "file2.pdf"],
  },
  {
    id: "op-2",
    title: "Folder Sync",
    progress: 100,
    status: "completed",
    type: "sync",
    startTime: new Date("2024-01-01T09:00:00Z"),
    endTime: new Date("2024-01-01T09:30:00Z"),
  },
];
```

### Database Seeding

```typescript
// tests/helpers/seedDatabase.ts
export async function seedTestData() {
  const operations = mockOperations;

  for (const operation of operations) {
    await db.operations.create(operation);
  }
}

export async function cleanupTestData() {
  await db.operations.deleteMany({
    id: { in: mockOperations.map((op) => op.id) },
  });
}
```

### Test Commands

```json
{
  "scripts": {
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "vitest run",
    "test:unit:watch": "vitest",
    "test:unit:coverage": "vitest run --coverage",
    "test:integration": "jest tests/integration",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:performance": "artillery run tests/performance/load-test.yml",
    "test:security": "npm audit && npm run test:security:custom",
    "test:security:custom": "jest tests/security",
    "test:all": "npm run test && npm run test:e2e && npm run test:performance"
  }
}
```

---

## 🔗 Related Documentation

- **[Development Setup](./DEVELOPMENT.md)** - Environment setup for testing
- **[API Documentation](./API.md)** - API endpoints for integration testing
- **[Architecture Guide](./ARCHITECTURE.md)** - System components for testing
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Common testing issues

---

_For advanced testing strategies or custom test requirements, consult the development team or refer to the framework-specific documentation._
