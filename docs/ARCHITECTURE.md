# 🏗️ System Architecture

Comprehensive guide to the W8 File Importer architecture, design patterns, and technical decisions.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🚀 Development Setup](./DEVELOPMENT.md)
- [🎨 Typography & Theming](./TYPOGRAPHY.md)
- [📡 API Documentation](./API.md)

---

## 📋 Table of Contents

- [System Overview](#system-overview)
- [Frontend Architecture](#frontend-architecture)
- [Backend Architecture](#backend-architecture)
- [WebSocket Communication](#websocket-communication)
- [Data Flow](#data-flow)
- [Security Model](#security-model)
- [Performance Considerations](#performance-considerations)
- [Deployment Architecture](#deployment-architecture)

## System Overview

The W8 File Importer follows a **WebSocket-first architecture** with real-time communication at its core. The system is designed as a professional desktop tool with web-based interface.

### Core Principles

1. **Real-Time First**: Every operation provides live updates via WebSocket
2. **Connection Required**: Application requires active WebSocket connection
3. **Type Safety**: End-to-end TypeScript for reliability
4. **Professional UI**: Advanced typography and theming system

### High-Level Architecture

```mermaid
graph TB
    Client[React SPA Client]
    Server[Express Server]
    WS[WebSocket Manager]

    Client -.->|HTTP Health Check| Server
    Client <-->|WebSocket Connection| WS
    Server -->|Manages| WS

    subgraph "Client Features"
        UI[UI Components]
        Themes[Theme System]
        Typography[Typography System]
        Hooks[Custom Hooks]
    end

    subgraph "Server Features"
        API[REST API]
        Auth[Authentication]
        Routes[Route Handlers]
        Middleware[Custom Middleware]
    end

    Client --> UI
    Client --> Themes
    Client --> Typography
    Client --> Hooks

    Server --> API
    Server --> Auth
    Server --> Routes
    Server --> Middleware
```

## Frontend Architecture

### Component Hierarchy

```
App.tsx (Root)
├── SplashScreen.tsx (Connection Gate)
├── Index.tsx (Main Dashboard)
│   ├── ServiceControl.tsx (WebSocket Control)
│   ├── ProgressTracker.tsx (Real-time Progress)
│   ├── EventLog.tsx (Live Events)
│   └── UpcomingSchedules.tsx (Schedule Management)
└── UI Components
    ├── Button (Enhanced with gradients)
    ├── Card (Glass effects)
    ├── Badge (Status indicators)
    └── Typography Components
```

### State Management Pattern

```typescript
// Custom Hook Pattern for WebSocket State
export function useWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [serviceStatus, setServiceStatus] = useState("stopped");
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({});

  // Connection management with 5-minute timeout
  const connect = useCallback(() => {
    // Health check -> WebSocket connection -> Event handling
  }, []);

  return {
    // State
    isConnected,
    serviceStatus,
    systemMetrics,
    // Actions
    connect,
    sendMessage,
    sendServiceCommand,
  };
}
```

### Routing System

- **React Router 6 SPA Mode**: Client-side routing
- **Route Protection**: Connection-gated routes
- **Dynamic Loading**: Components loaded based on connection state

## Backend Architecture

### Express Server Structure

```
server/
├── index.ts              # Server factory and configuration
├── websocket.ts           # WebSocket manager class
├── routes/               # API endpoint handlers
│   ├── demo.ts           # Demo endpoints
│   ├── schedules.ts      # Schedule management
│   ├── operations.ts     # File operations
│   ├── settings.ts       # Configuration
│   └── websocket-data.ts # WebSocket data handlers
└── middleware/           # Custom middleware
    └── auth.ts           # API key authentication
```

### WebSocket Manager

```typescript
export class WebSocketManager {
  private wss: WebSocketServer;
  private clients: Set<WebSocket> = new Set();
  private recentProgress: Map<string, any> = new Map();
  private recentEvents: any[] = [];

  constructor(server: Server) {
    // WebSocket server with upgrade handling
    this.wss = new WebSocketServer({ noServer: true });

    // Handle HTTP -> WebSocket upgrades
    server.on("upgrade", (request, socket, head) => {
      if (request.url === "/ws") {
        this.wss.handleUpgrade(request, socket, head, (ws) => {
          this.wss.emit("connection", ws, request);
        });
      }
    });
  }
}
```

### API Design Pattern

```typescript
// Standard API Handler Pattern
export const handleOperation: RequestHandler = async (req, res) => {
  try {
    // 1. Validate input with Zod schemas
    const data = OperationSchema.parse(req.body);

    // 2. Process operation
    const result = await processOperation(data);

    // 3. Broadcast real-time updates
    wsManager.broadcast({
      type: "progress",
      data: { ...result, timestamp: new Date() },
    });

    // 4. Return HTTP response
    res.json({ success: true, result });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
```

## WebSocket Communication

### Connection Lifecycle

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant WS as WebSocket Manager

    C->>S: GET /api/ws-health
    S-->>C: Health status

    alt Health OK
        C->>WS: WebSocket Upgrade /ws
        WS-->>C: Connection Established
        WS->>C: Initial Data

        loop Real-time Updates
            S->>WS: Broadcast Event
            WS->>C: Live Update
        end
    else Health Failed
        C->>C: Retry with backoff
    end
```

### Message Protocol

```typescript
interface WebSocketMessage {
  type: 'progress' | 'event' | 'notification' | 'schedule' | 'service-status' | 'system-metrics';
  data?: any;
  timestamp?: string;
}

// Example Messages
{
  type: 'progress',
  data: {
    id: 'upload_123',
    title: 'File Upload',
    progress: 75,
    status: 'running',
    currentFile: 'document.pdf'
  }
}

{
  type: 'system-metrics',
  data: {
    cpu: 45,
    memory: 60,
    storage: 80,
    uptime: '2h 35m'
  }
}
```

### Connection Resilience

1. **Health Check**: 30-second timeout HTTP request
2. **Connection Timeout**: 30-second WebSocket connection timeout
3. **Retry Logic**: Progressive delays (5s → 60s max)
4. **5-Minute Rule**: Fatal timeout after 5 minutes without connection

## Data Flow

### Real-Time Progress Updates

```mermaid
graph LR
    API[API Endpoint] --> Process[Process Operation]
    Process --> WS[WebSocket Broadcast]
    WS --> Client1[Client 1]
    WS --> Client2[Client 2]
    WS --> ClientN[Client N]

    Client1 --> UI1[Update UI]
    Client2 --> UI2[Update UI]
    ClientN --> UIN[Update UI]
```

### Event Aggregation

- **Progress Storage**: Recent progress items (auto-cleanup after 5 minutes)
- **Event History**: Last 50 events (rolling buffer)
- **New Client Sync**: Immediate data sync for new connections

## Security Model

### API Authentication

```typescript
// API Key Middleware
export const authenticateApiKey: RequestHandler = (req, res, next) => {
  const apiKey = req.headers["x-api-key"];

  if (!apiKey || !isValidApiKey(apiKey)) {
    return res.status(401).json({ error: "Invalid API key" });
  }

  next();
};
```

### Rate Limiting

- **Per-API-Key Limits**: Prevent abuse from external applications
- **Request Logging**: Track usage for monitoring
- **Graceful Degradation**: Informative error messages

### CORS Configuration

```typescript
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || "*",
    credentials: true,
  }),
);
```

## Performance Considerations

### Frontend Optimizations

1. **Component Memoization**: React.memo for expensive components
2. **Virtual Scrolling**: For large lists (event logs)
3. **Progressive Enhancement**: Load features based on connection state
4. **Efficient Re-renders**: Optimized state updates

### Backend Optimizations

1. **Event Debouncing**: Prevent excessive WebSocket broadcasts
2. **Memory Management**: Automatic cleanup of old data
3. **Connection Pooling**: Efficient WebSocket client management
4. **Compression**: Gzip compression for HTTP responses

### WebSocket Optimizations

```typescript
// Efficient Broadcasting
public broadcast(message: WSMessage) {
  const serialized = JSON.stringify(message);

  this.clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(serialized);
    }
  });
}
```

## Deployment Architecture

### Development Environment

```
Vite Dev Server (Port 8080)
├── React SPA with HMR
├── Integrated Express Server
└── Real WebSocket Manager
```

### Production Environment

```
Express Server (Port 3000)
├── Serves Static SPA Files
├── API Routes (/api/*)
├── WebSocket Endpoint (/ws)
└── Health Monitoring
```

### Netlify Deployment

```
Netlify Functions
├── serverless.ts (Express wrapper)
├── Static File Serving
└── Edge Functions for WebSocket proxy
```

---

## 🔗 Related Documentation

- **[Development Setup](./DEVELOPMENT.md)** - Local development environment
- **[API Reference](./API.md)** - Complete API documentation
- **[Typography System](./TYPOGRAPHY.md)** - Design system architecture
- **[Deployment Guide](./DEPLOYMENT.md)** - Production deployment

---

_Need help with architecture decisions? Check the [Troubleshooting Guide](./TROUBLESHOOTING.md) or review the [API Documentation](./API.md)._
