# 🔧 Troubleshooting Guide

Solutions to common issues and debugging strategies for the W8 File Importer.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🏗️ Architecture](./ARCHITECTURE.md)
- [🚀 Development Setup](./DEVELOPMENT.md)
- [📡 API Documentation](./API.md)

---

## 📋 Table of Contents

- [WebSocket Connection Issues](#websocket-connection-issues)
- [Development Server Problems](#development-server-problems)
- [Build and Deployment Issues](#build-and-deployment-issues)
- [Typography and Theming Issues](#typography-and-theming-issues)
- [Performance Problems](#performance-problems)
- [Common Error Messages](#common-error-messages)
- [Debugging Tools](#debugging-tools)
- [Getting Help](#getting-help)

## WebSocket Connection Issues

### 🚨 "Connection Required" Error Screen

**Symptoms**: Black screen with "CONNECTION REQUIRED" message

**Causes**:

- WebSocket health check failing
- Server not running or misconfigured
- Network connectivity issues
- CORS configuration problems

**Solutions**:

1. **Check Server Status**

```bash
# Verify server is running
curl http://localhost:8080/api/ws-health

# Expected response:
{
  "status": "healthy",
  "wsManagerAvailable": true,
  "connectedClients": 0
}
```

2. **Restart Development Server**

```bash
# Stop current server (Ctrl+C)
# Clear cache and restart
rm -rf node_modules/.vite
npm run dev
```

3. **Check Network Configuration**

```bash
# Test basic connectivity
curl http://localhost:8080/api/ping

# Test WebSocket upgrade (should show 101 status)
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
  http://localhost:8080/ws
```

### ⚠️ "Failed to fetch" Errors

**Symptoms**: Console shows "TypeError: Failed to fetch"

**Causes**:

- API endpoints not responding
- CORS restrictions
- Development server not fully initialized

**Solutions**:

1. **Verify API Endpoints**

```bash
# Test health endpoint
curl -v http://localhost:8080/api/ws-health

# Check for 200 status code and proper JSON response
```

2. **Check Vite Configuration**

```typescript
// vite.config.ts - ensure proper server setup
export default defineConfig({
  server: {
    host: "::",
    port: 8080,
    // ... other config
  },
});
```

3. **Clear Browser Cache**

- Hard refresh: `Ctrl+Shift+R` (Chrome/Edge) or `Cmd+Shift+R` (Safari)
- Open DevTools → Application → Clear Storage

### 🔄 Connection Timeout Issues

**Symptoms**: App shows "Connecting..." for 5+ minutes

**Causes**:

- WebSocket manager not initialized
- Server startup issues
- Port conflicts

**Solutions**:

1. **Check Server Logs**

```bash
npm run dev
# Look for these success messages:
# 🚀 Initializing WebSocket manager...
# ✅ WebSocket manager initialized successfully
# ✅ Real server integrated with Vite development server
```

2. **Verify Port Availability**

```bash
# Check if port 8080 is in use
netstat -an | grep :8080
# or
lsof -i :8080

# Kill conflicting processes if found
kill -9 <PID>
```

3. **Check Express Integration**

```bash
# Verify the development server includes Express routes
curl http://localhost:8080/api/ping
# Should return: {"message":"ping"}
```

## Development Server Problems

### 🛠️ Server Won't Start

**Symptoms**: `npm run dev` fails or hangs

**Common Causes & Solutions**:

1. **Node Version Issues**

```bash
# Check Node version
node --version
# Should be 18.0.0 or higher

# Use nvm to switch versions
nvm use 18
nvm install 18.18.0
```

2. **Dependency Issues**

```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Check for peer dependency warnings
npm ls
```

3. **Port Conflicts**

```bash
# Change port in package.json or vite.config.ts
# Or kill conflicting process
pkill -f ":8080"
```

### 🔥 Hot Reload Not Working

**Symptoms**: Changes not reflected automatically

**Solutions**:

1. **Check File Watching**

```bash
# Increase file watcher limits (Linux/macOS)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

2. **Verify Vite Configuration**

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    watch: {
      usePolling: true, // Enable if on Windows or Docker
    },
  },
});
```

3. **Browser Cache Issues**

- Disable cache in DevTools (Network tab)
- Use incognito mode for testing

### 📦 Import Resolution Errors

**Symptoms**: "Cannot resolve module" errors

**Solutions**:

1. **Check Path Aliases**

```typescript
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./client/*"],
      "@shared/*": ["./shared/*"]
    }
  }
}
```

2. **Verify File Extensions**

```typescript
// ✅ Correct
import { Component } from "@/components/Component";

// ❌ Incorrect
import { Component } from "@/components/Component.tsx";
```

## Build and Deployment Issues

### 🏗️ Build Failures

**Symptoms**: `npm run build` fails with TypeScript errors

**Solutions**:

1. **Type Check Issues**

```bash
# Run type check separately
npm run typecheck

# Fix type errors before building
# Common fixes:
# - Add missing type definitions
# - Fix any type assertions
# - Update @types/* packages
```

2. **Memory Issues**

```bash
# Increase Node memory limit
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

3. **Path Resolution in Build**

```typescript
// vite.config.ts - ensure aliases work in production
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./client"),
      "@shared": path.resolve(__dirname, "./shared"),
    },
  },
});
```

### 🚢 Netlify Deployment Issues

**Symptoms**: Deployment succeeds but app doesn't work

**Solutions**:

1. **Check Build Configuration**

```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist/spa"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

2. **Environment Variables**

```bash
# Set in Netlify dashboard:
NODE_ENV=production
NETLIFY=true
```

3. **Function Configuration**

```typescript
// netlify/functions/api.ts
import { createServer } from "../../server";
import serverless from "serverless-http";

const app = createServer();
export const handler = serverless(app);
```

## Typography and Theming Issues

### 🎨 Fonts Not Loading

**Symptoms**: Fallback fonts displayed instead of custom fonts

**Solutions**:

1. **Check Google Fonts Import**

```css
/* client/global.css - verify import */
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Space+Grotesk:wght@300..700&display=swap");
```

2. **Verify Font Variables**

```css
/* Check CSS custom properties */
:root {
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: "Space Grotesk", "Inter", sans-serif;
}
```

3. **Network Issues**

- Check browser DevTools Network tab for font loading failures
- Verify Content Security Policy allows Google Fonts

### 🌈 Theme Not Applied

**Symptoms**: Default theme shown instead of selected theme

**Solutions**:

1. **Check Theme Class Application**

```html
<!-- Verify theme class on root element -->
<html class="theme-arctic">
  <!-- Should match selected theme -->
</html>
```

2. **CSS Custom Property Issues**

```css
/* Verify theme variables are defined */
.theme-arctic {
  --primary: 200 95% 45%;
  --background: 210 25% 98%;
  /* ... other properties */
}
```

3. **TailwindCSS Configuration**

```typescript
// tailwind.config.ts
export default {
  content: ["./client/**/*.{ts,tsx}"], // Ensure all files included
  // ...
};
```

### 📱 Responsive Typography Issues

**Symptoms**: Text too small/large on certain screen sizes

**Solutions**:

1. **Check Viewport Meta Tag**

```html
<!-- index.html -->
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
```

2. **Verify Responsive Breakpoints**

```css
/* Check media queries in global.css */
@media (min-width: 1024px) {
  :root {
    --text-base: 1rem;
    /* ... responsive scaling */
  }
}
```

## Performance Problems

### 🐌 Slow Initial Load

**Symptoms**: Long loading times, especially splash screen

**Solutions**:

1. **Bundle Analysis**

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist/spa
```

2. **Optimize Imports**

```typescript
// ✅ Tree-shakable imports
import { Button } from "@/components/ui/button";

// ❌ Entire library import
import * as UI from "@/components/ui";
```

3. **Lazy Loading**

```typescript
// Lazy load heavy components
const HeavyComponent = lazy(() => import("@/components/HeavyComponent"));
```

### 🔄 WebSocket Performance Issues

**Symptoms**: Lag in real-time updates

**Solutions**:

1. **Message Frequency**

```typescript
// Debounce frequent updates
const debouncedUpdate = debounce((data) => {
  wsManager.broadcast(data);
}, 100);
```

2. **Connection Monitoring**

```javascript
// Monitor connection performance
ws.addEventListener("message", (event) => {
  const latency = Date.now() - JSON.parse(event.data).timestamp;
  console.log(`WebSocket latency: ${latency}ms`);
});
```

## Common Error Messages

### 🚫 "WebSocket manager not available"

**Cause**: Server initialization failed

**Solution**:

```bash
# Check server logs for initialization errors
npm run dev
# Look for: ✅ WebSocket manager initialized successfully
```

### ❌ "Failed to establish required connection"

**Cause**: Health check failing

**Solution**:

```bash
# Debug health endpoint
curl -v http://localhost:8080/api/ws-health
# Check response status and body
```

### 🔒 "Invalid API key"

**Cause**: Missing or incorrect authentication

**Solution**:

```bash
# For development, API key validation might be disabled
# Check server configuration or provide valid key
curl -H "X-API-Key: test-key" http://localhost:8080/api/progress
```

### 📡 "Connection timeout"

**Cause**: WebSocket connection taking too long

**Solution**:

1. Check network connectivity
2. Verify WebSocket endpoint is accessible
3. Increase timeout values if needed

## Debugging Tools

### Browser DevTools

1. **Network Tab**

   - Monitor API requests
   - Check WebSocket connection status
   - Verify response codes and timing

2. **Console Tab**

   - Application logs and errors
   - WebSocket message debugging
   - Performance timing

3. **Application Tab**
   - Local storage and session data
   - Service worker status
   - Cache inspection

### Server Debugging

```bash
# Enable debug mode
DEBUG=* npm run dev

# Check specific modules
DEBUG=websocket npm run dev
DEBUG=express:* npm run dev
```

### WebSocket Testing Tools

```bash
# Test WebSocket connection with wscat
npm install -g wscat
wscat -c ws://localhost:8080/ws

# Send test message
{"type": "request-initial-data"}
```

### API Testing

```bash
# Test API endpoints
curl -X POST http://localhost:8080/api/progress \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-key" \
  -d '{"id":"test","title":"Test","progress":50,"status":"running","type":"upload"}'
```

## Getting Help

### Before Reporting Issues

1. **Check Logs**

   - Browser console errors
   - Server console output
   - Network requests in DevTools

2. **Reproduce Steps**

   - Document exact steps to reproduce
   - Note environment details (OS, browser, Node version)

3. **Gather Information**
   - Error messages (full text)
   - Screenshots if UI-related
   - Relevant code snippets

### Diagnostic Information

```bash
# Gather system information
node --version
npm --version
curl --version

# Check application health
curl http://localhost:8080/api/ws-health
curl http://localhost:8080/api/ping

# Test WebSocket connectivity
wscat -c ws://localhost:8080/ws
```

### Log Collection

```javascript
// Enable verbose logging in browser
localStorage.setItem("debug", "*");
// Reload page and check console for detailed logs
```

---

## 🔗 Related Documentation

- **[Development Setup](./DEVELOPMENT.md)** - Initial setup and configuration
- **[API Documentation](./API.md)** - API endpoints and WebSocket protocol
- **[Architecture Guide](./ARCHITECTURE.md)** - System design and components

---

_Still having issues? Document your problem with the diagnostic steps above and consult the [API Documentation](./API.md) for endpoint-specific troubleshooting._
