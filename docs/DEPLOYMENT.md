# 🚀 Deployment Guide

Comprehensive guide for deploying the W8 File Importer to various environments.

## 📚 Navigation

- [🏠 Main Documentation](../README.md)
- [🏗️ Architecture](./ARCHITECTURE.md)
- [🚀 Development Setup](./DEVELOPMENT.md)
- [📡 API Documentation](./API.md)
- [🔧 Troubleshooting](./TROUBLESHOOTING.md)
- [📖 User Manual](./USER_MANUAL.md)

---

## 📋 Table of Contents

- [Deployment Overview](#deployment-overview)
- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Netlify Deployment](#netlify-deployment)
- [Manual Server Deployment](#manual-server-deployment)
- [Docker Deployment](#docker-deployment)
- [Production Optimizations](#production-optimizations)
- [Security Considerations](#security-considerations)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Rollback Procedures](#rollback-procedures)

## Deployment Overview

### Architecture

The W8 File Importer is a full-stack application with:

- **Frontend**: React SPA built with Vite
- **Backend**: Express.js server with WebSocket support
- **Build Output**: Static assets + serverless functions
- **Database**: File-based storage (configurable)

### Deployment Options

1. **Netlify (Recommended)**: Serverless deployment with automatic CI/CD
2. **Manual Server**: Traditional server deployment with PM2
3. **Docker**: Containerized deployment for any platform
4. **Vercel**: Alternative serverless platform (with modifications)

## Prerequisites

### System Requirements

**Development Machine:**

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- Git 2.20.0 or higher

**Production Environment:**

- Modern hosting platform or server
- HTTPS support (required for WebSocket connections)
- Environment variable support
- File storage capabilities

### Build Requirements

```bash
# Verify Node.js version
node --version
# Should output v18.0.0 or higher

# Verify npm version
npm --version
# Should output 8.0.0 or higher

# Install dependencies
npm install

# Test build process
npm run build
```

## Environment Configuration

### Environment Variables

Create environment-specific configuration:

**Development (`.env.development`):**

```bash
NODE_ENV=development
VITE_API_URL=http://localhost:8080
VITE_WS_URL=ws://localhost:8080
DEBUG=true
```

**Production (`.env.production`):**

```bash
NODE_ENV=production
VITE_API_URL=https://your-domain.com
VITE_WS_URL=wss://your-domain.com
DEBUG=false
API_RATE_LIMIT=100
UPLOAD_MAX_SIZE=10485760
```

**Netlify Functions:**

```bash
NODE_ENV=production
NETLIFY=true
FUNCTION_TIMEOUT=30
MAX_UPLOAD_SIZE=10485760
CORS_ORIGIN=https://your-app.netlify.app
```

### Configuration Files

**Netlify Configuration (`netlify.toml`):**

```toml
[build]
  command = "npm run build"
  publish = "dist/spa"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  node_bundler = "esbuild"

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-API-Key"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

## Netlify Deployment

### Automatic Deployment

1. **Repository Setup**

   ```bash
   # Initialize git repository
   git init
   git add .
   git commit -m "Initial commit"

   # Push to GitHub/GitLab
   git remote add origin <your-repo-url>
   git push -u origin main
   ```

2. **Netlify Site Creation**

   - Log in to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your repository
   - Configure build settings:
     - Build command: `npm run build`
     - Publish directory: `dist/spa`
     - Functions directory: `netlify/functions`

3. **Environment Variables**
   - Go to Site settings → Environment variables
   - Add production environment variables
   - Deploy the site

### Manual Deployment

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Build the application
npm run build

# Deploy to Netlify
netlify deploy --prod --dir=dist/spa --functions=netlify/functions
```

### Netlify Functions Setup

The serverless functions are automatically configured:

**Function Structure:**

```
netlify/
  functions/
    api.ts          # Main API handler
```

**Function Handler (`netlify/functions/api.ts`):**

```typescript
import { createServer } from "../../server";
import serverless from "serverless-http";

const app = createServer();
export const handler = serverless(app);
```

## Manual Server Deployment

### Server Requirements

- Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- Node.js 18+ installed
- PM2 process manager
- Nginx (recommended) or Apache
- SSL certificate (Let's Encrypt recommended)

### Server Setup

1. **Install Dependencies**

   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Node.js 18
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install PM2
   npm install -g pm2

   # Install Nginx
   sudo apt install nginx -y
   ```

2. **Application Deployment**

   ```bash
   # Clone repository
   git clone <your-repo-url> /var/www/w8-importer
   cd /var/www/w8-importer

   # Install dependencies
   npm ci --production

   # Build application
   npm run build

   # Set proper permissions
   sudo chown -R www-data:www-data /var/www/w8-importer
   ```

3. **PM2 Configuration**

   ```bash
   # Create PM2 ecosystem file
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'w8-importer',
       script: './server/index.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'development',
         PORT: 3000
       },
       env_production: {
         NODE_ENV: 'production',
         PORT: 3000
       }
     }]
   };
   EOF

   # Start application
   pm2 start ecosystem.config.js --env production
   pm2 save
   pm2 startup
   ```

4. **Nginx Configuration**

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name your-domain.com;

       ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

       root /var/www/w8-importer/dist/spa;
       index index.html;

       # API routes
       location /api/ {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }

       # WebSocket routes
       location /ws {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }

       # Static files
       location / {
           try_files $uri $uri/ /index.html;
           add_header Cache-Control "public, max-age=31536000";
       }

       # Security headers
       add_header X-Frame-Options "DENY";
       add_header X-Content-Type-Options "nosniff";
       add_header Referrer-Policy "strict-origin-when-cross-origin";
   }
   ```

## Docker Deployment

### Dockerfile

```dockerfile
# Multi-stage build for optimal image size
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/server ./server
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package*.json ./

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/ping || exit 1

# Start application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server/index.js"]
```

### Docker Compose

```yaml
version: "3.8"

services:
  app:
    build: .
    container_name: w8-importer
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: w8-importer-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - app
    networks:
      - app-network

volumes:
  uploads:
  logs:

networks:
  app-network:
    driver: bridge
```

### Docker Deployment Commands

```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f app

# Scale application
docker-compose up -d --scale app=3

# Update application
docker-compose pull
docker-compose up -d

# Stop services
docker-compose down
```

## Production Optimizations

### Performance Optimizations

1. **Build Optimizations**

   ```json
   {
     "scripts": {
       "build": "vite build --mode production",
       "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/spa"
     }
   }
   ```

2. **Server Optimizations**

   ```typescript
   // server/index.ts
   import compression from "compression";
   import helmet from "helmet";

   app.use(helmet());
   app.use(compression());
   ```

3. **Caching Strategy**
   ```typescript
   // Static file caching
   app.use(
     "/static",
     express.static("dist/spa", {
       maxAge: "1y",
       etag: true,
       lastModified: true,
     }),
   );
   ```

### Database Optimizations

```typescript
// Implement connection pooling
import { Pool } from "pg";

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### CDN Configuration

```bash
# Configure CDN for static assets
# AWS CloudFront, Cloudflare, or similar
```

## Security Considerations

### HTTPS Configuration

```bash
# Let's Encrypt SSL certificate
sudo certbot --nginx -d your-domain.com
```

### Security Headers

```typescript
// server/middleware/security.ts
import helmet from "helmet";

export const securityMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "fonts.googleapis.com"],
      fontSrc: ["'self'", "fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "wss:", "ws:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});
```

### API Security

```typescript
// Rate limiting
import rateLimit from "express-rate-limit";

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP",
});

app.use("/api/", limiter);
```

### Environment Security

```bash
# Secure environment variables
export NODE_ENV=production
export API_SECRET_KEY="your-secret-key"
export JWT_SECRET="your-jwt-secret"
export DATABASE_ENCRYPTION_KEY="your-db-key"
```

## Monitoring & Maintenance

### Application Monitoring

```typescript
// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version,
  });
});
```

### Log Management

```typescript
// Winston logging configuration
import winston from "winston";

const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  ),
  transports: [
    new winston.transports.File({ filename: "logs/error.log", level: "error" }),
    new winston.transports.File({ filename: "logs/combined.log" }),
  ],
});
```

### Monitoring Scripts

```bash
#!/bin/bash
# monitoring/health-check.sh

URL="https://your-domain.com/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Health check passed"
else
    echo "$(date): Health check failed with status $RESPONSE"
    # Send alert notification
fi
```

### Backup Procedures

```bash
#!/bin/bash
# backup/daily-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/w8-importer"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /var/www/w8-importer

# Backup database (if applicable)
pg_dump your_database > $BACKUP_DIR/db_$DATE.sql

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
```

## Rollback Procedures

### Automated Rollback

```bash
#!/bin/bash
# scripts/rollback.sh

PREVIOUS_VERSION=$1

if [ -z "$PREVIOUS_VERSION" ]; then
    echo "Usage: ./rollback.sh <version>"
    exit 1
fi

echo "Rolling back to version: $PREVIOUS_VERSION"

# Stop current application
pm2 stop w8-importer

# Restore previous version
git checkout $PREVIOUS_VERSION
npm ci --production
npm run build

# Restart application
pm2 start w8-importer
pm2 save

echo "Rollback completed successfully"
```

### Database Rollback

```sql
-- Example database rollback script
BEGIN;

-- Backup current state
CREATE TABLE backup_operations_$(date +%Y%m%d) AS SELECT * FROM operations;

-- Restore previous state
TRUNCATE operations;
INSERT INTO operations SELECT * FROM operations_backup_20240101;

COMMIT;
```

### Verification Steps

```bash
# Post-deployment verification
curl -f https://your-domain.com/health
curl -f https://your-domain.com/api/ping
curl -f https://your-domain.com/api/ws-health

# WebSocket connectivity test
wscat -c wss://your-domain.com/ws
```

---

## 🔗 Related Documentation

- **[Development Setup](./DEVELOPMENT.md)** - Local development environment
- **[Architecture Guide](./ARCHITECTURE.md)** - System design and components
- **[API Documentation](./API.md)** - API endpoints and WebSocket protocol
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Common deployment issues

---

_For deployment assistance or advanced configuration requirements, consult the technical documentation or contact your DevOps team._
