<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>W8 File Importer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #87CEEB 0%, #98E4FF 50%, #B8E6FF 100%);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            transition: opacity 2s ease-out;
        }

        body.fade-out {
            opacity: 0;
        }

        .splash-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bee {
            position: absolute;
            width: 80px;
            height: 60px;
            z-index: 10;
            animation: flyPath 8s ease-in-out infinite;
        }

        .bee-body {
            position: relative;
            width: 50px;
            height: 32px;
            background: radial-gradient(ellipse at center, #FFD700 0%, #FFA500 70%, #FF8C00 100%);
            border-radius: 60% 60% 70% 70%;
            margin: 15px;
            box-shadow:
                inset 0 2px 4px rgba(255, 255, 255, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .bee-stripes {
            position: absolute;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(0deg,
                    transparent 0px,
                    transparent 4px,
                    #2C2C2C 4px,
                    #2C2C2C 7px,
                    transparent 7px,
                    transparent 11px,
                    #2C2C2C 11px,
                    #2C2C2C 14px,
                    transparent 14px,
                    transparent 18px,
                    #2C2C2C 18px,
                    #2C2C2C 21px);
            border-radius: 60% 60% 70% 70%;
        }

        .bee-head {
            position: absolute;
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #444 0%, #222 100%);
            border-radius: 50%;
            top: 2px;
            left: -8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .bee-eye {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #000;
            border-radius: 50%;
            top: 4px;
        }

        .bee-eye.left {
            left: 3px;
        }

        .bee-eye.right {
            right: 3px;
        }

        .bee-antenna {
            position: absolute;
            width: 1px;
            height: 8px;
            background: #333;
            top: -6px;
        }

        .bee-antenna.left {
            left: 4px;
            transform: rotate(-15deg);
        }

        .bee-antenna.right {
            right: 4px;
            transform: rotate(15deg);
        }

        .bee-antenna::after {
            content: '';
            position: absolute;
            width: 3px;
            height: 3px;
            background: #444;
            border-radius: 50%;
            top: -2px;
            left: -1px;
        }

        .bee-wing {
            position: absolute;
            width: 28px;
            height: 20px;
            background: radial-gradient(ellipse, rgba(255, 255, 255, 0.9) 0%, rgba(200, 220, 255, 0.7) 70%, rgba(150, 180, 255, 0.4) 100%);
            border-radius: 70% 30% 70% 30%;
            animation: wingFlap 0.08s ease-in-out infinite alternate;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
        }

        .bee-wing::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(45deg,
                    transparent 0px,
                    transparent 3px,
                    rgba(255, 255, 255, 0.3) 3px,
                    rgba(255, 255, 255, 0.3) 4px);
            border-radius: 70% 30% 70% 30%;
        }

        .bee-wing.left {
            top: -8px;
            left: -12px;
            transform-origin: bottom right;
        }

        .bee-wing.right {
            top: -8px;
            right: -12px;
            transform-origin: bottom left;
        }

        .bee-stinger {
            position: absolute;
            width: 6px;
            height: 2px;
            background: linear-gradient(to right, #444 0%, #222 100%);
            border-radius: 0 50% 50% 0;
            bottom: 8px;
            right: -4px;
        }

        .folder {
            position: absolute;
            width: 50px;
            height: 40px;
            opacity: 0;
        }

        .folder-back {
            position: absolute;
            width: 100%;
            height: 80%;
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            border-radius: 4px;
            bottom: 0;
        }

        .folder-front {
            position: absolute;
            width: 70%;
            height: 20%;
            background: linear-gradient(135deg, #5BA0F2 0%, #4A90E2 100%);
            border-radius: 4px 4px 0 0;
            top: 0;
            left: 0;
        }

        .folder1 {
            top: 20%;
            left: 10%;
            animation: floatUp1 6s ease-in-out infinite;
        }

        .folder2 {
            top: 60%;
            right: 15%;
            animation: floatUp2 7s ease-in-out infinite 1s;
        }

        .folder3 {
            bottom: 30%;
            left: 20%;
            animation: floatUp3 5s ease-in-out infinite 2s;
        }

        .folder4 {
            top: 40%;
            right: 30%;
            animation: floatUp4 8s ease-in-out infinite 0.5s;
        }

        .folder5 {
            bottom: 20%;
            right: 10%;
            animation: floatUp5 6.5s ease-in-out infinite 1.5s;
        }

        .title {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            color: #333;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            opacity: 0;
            animation: titleFade 3s ease-in-out 2s forwards;
        }

        .loading-dots {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .activity-log {
            position: absolute;
            bottom: 8%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            max-width: 500px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .log-content {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #333;
            text-align: center;
            transition: all 0.3s ease-in-out;
        }

        .log-content.success {
            color: #22c55e;
        }

        .log-content.info {
            color: #3b82f6;
        }

        .log-content.warning {
            color: #f59e0b;
        }

        .dot {
            width: 8px;
            height: 8px;
            background: #333;
            border-radius: 50%;
            animation: dotBounce 1.5s ease-in-out infinite;
        }

        .dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes flyPath {
            0% {
                transform: translate(-100px, 50px) rotate(0deg);
            }

            25% {
                transform: translate(200px, -30px) rotate(15deg);
            }

            50% {
                transform: translate(400px, 20px) rotate(-10deg);
            }

            75% {
                transform: translate(600px, -50px) rotate(20deg);
            }

            100% {
                transform: translate(800px, 30px) rotate(0deg);
            }
        }

        @keyframes wingFlap {
            0% {
                transform: rotateY(0deg) rotateX(10deg);
            }

            100% {
                transform: rotateY(40deg) rotateX(-5deg);
            }
        }

        @keyframes floatUp1 {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes floatUp2 {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0.8);
                opacity: 0;
            }

            15% {
                opacity: 1;
            }

            85% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(-270deg) scale(1.2);
                opacity: 0;
            }
        }

        @keyframes floatUp3 {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }

            20% {
                opacity: 1;
            }

            80% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(180deg);
                opacity: 0;
            }
        }

        @keyframes floatUp4 {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(1.1);
                opacity: 0;
            }

            12% {
                opacity: 1;
            }

            88% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(450deg) scale(0.9);
                opacity: 0;
            }
        }

        @keyframes floatUp5 {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }

            18% {
                opacity: 1;
            }

            82% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(-180deg);
                opacity: 0;
            }
        }

        @keyframes titleFade {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }

            100% {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        @keyframes dotBounce {

            0%,
            80%,
            100% {
                transform: scale(1);
            }

            40% {
                transform: scale(1.5);
            }
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #FFD700;
            border-radius: 50%;
            pointer-events: none;
        }

        .particle1 {
            top: 25%;
            left: 15%;
            animation: sparkle 3s ease-in-out infinite;
        }

        .particle2 {
            top: 70%;
            right: 20%;
            animation: sparkle 3s ease-in-out infinite 1s;
        }

        .particle3 {
            bottom: 40%;
            left: 30%;
            animation: sparkle 3s ease-in-out infinite 2s;
        }

        @keyframes sparkle {

            0%,
            100% {
                opacity: 0;
                transform: scale(0);
            }

            50% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>

<body>
    <div class="splash-container">
        <!-- Bee -->
        <div class="bee">
            <div class="bee-body">
                <div class="bee-stripes"></div>
                <div class="bee-stinger"></div>
            </div>
            <div class="bee-head">
                <div class="bee-eye left"></div>
                <div class="bee-eye right"></div>
                <div class="bee-antenna left"></div>
                <div class="bee-antenna right"></div>
            </div>
            <div class="bee-wing left"></div>
            <div class="bee-wing right"></div>
        </div>

        <!-- Folders -->
        <div class="folder folder1">
            <div class="folder-front"></div>
            <div class="folder-back"></div>
        </div>

        <div class="folder folder2">
            <div class="folder-front"></div>
            <div class="folder-back"></div>
        </div>

        <div class="folder folder3">
            <div class="folder-front"></div>
            <div class="folder-back"></div>
        </div>

        <div class="folder folder4">
            <div class="folder-front"></div>
            <div class="folder-back"></div>
        </div>

        <div class="folder folder5">
            <div class="folder-front"></div>
            <div class="folder-back"></div>
        </div>

        <!-- Sparkle particles -->
        <div class="particle particle1"></div>
        <div class="particle particle2"></div>
        <div class="particle particle3"></div>

        <!-- Title -->
        <h1 class="title">W8 File Importer</h1>

        <!-- Loading animation -->
        <div class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>

        <!-- Activity Log -->
        <div class="activity-log">
            <div class="log-content" id="logContent">
                Connecting to W8 File Importer Service...
            </div>
        </div>
    </div>

    <script>
        const logMessages = [
            { message: "Initializing BeeFiles system...", type: "info" },
            { message: "Loading file indexing engine", type: "info" },
            { message: "Scanning directory structure", type: "info" },
            { message: "Bee worker threads started", type: "success" },
            { message: "Found 1,247 files to process", type: "info" },
            { message: "Optimizing search algorithms", type: "info" },
            { message: "Cache warming in progress", type: "warning" },
            { message: "Database connections established", type: "success" },
            { message: "File permissions validated", type: "success" },
            { message: "Loading user preferences", type: "info" },
            { message: "Thumbnail generation queued", type: "info" },
            { message: "System ready - welcome to BeeFiles!", type: "success" }
        ];

        const logContent = document.getElementById('logContent');
        let logIndex = 0;

        function updateLogEntry() {
            const entry = logMessages[logIndex];

            // Update the text and color
            logContent.textContent = entry.message;
            logContent.className = `log-content ${entry.type}`;

            // If this is the last message, start fade out after a delay
            if (logIndex === logMessages.length - 1) {
                setTimeout(() => {
                    document.body.classList.add('fade-out');

                    // After fade completes, restart the cycle
                    setTimeout(() => {
                        document.body.classList.remove('fade-out');
                        logIndex = 0;
                        setTimeout(() => {
                            updateLogEntry();
                            startLogCycle();
                        }, 100);
                    }, 2000); // Match the CSS transition duration
                }, 2000); // Show final message for 2 seconds before fading
            } else {
                logIndex++;
                setTimeout(() => {
                    updateLogEntry();
                }, 1500);
            }
        }

        function startLogCycle() {
            // This function is just to start the recursive calls
        }

        // Start the first message
        updateLogEntry();
    </script>
</body>

</html>