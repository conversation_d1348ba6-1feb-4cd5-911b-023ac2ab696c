import { defineConfig, Plugin } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0",
port: 5173,
    strictPort: true,
    fs: {
      allow: ["./client", "./shared"],
      deny: [".env", ".env.*", "*.{crt,pem}", "**/.git/**", "server/**"],
    },
  },
  build: {
    outDir: "dist/spa",
  },
  plugins: [react(), expressPlugin()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./client"),
      "@shared": path.resolve(__dirname, "./shared"),
    },
  },
}));

function expressPlugin(): Plugin {
  return {
    name: "express-plugin",
    apply: "serve", // Only apply during development (serve mode)
    configureServer(viteServer) {
      // Create a proper HTTP server with WebSocket support
      console.log("🚀 Initializing real server with WebSocket support...");

      try {
        // Dynamically import to avoid loading issues
        const serverModule = require("./server");
        const expressServer = serverModule.createServer();

        // Get the Express app from the HTTP server
        const app = expressServer._events?.request || expressServer;

        // Mount the Express app to handle API routes
        viteServer.middlewares.use(app);

        // Handle WebSocket upgrade
        viteServer.httpServer?.on("upgrade", (request, socket, head) => {
          console.log("🔄 WebSocket upgrade request intercepted:", request.url);
          if (request.url === "/ws") {
            // Forward to the real server
            expressServer.emit("upgrade", request, socket, head);
          }
        });

        console.log("✅ Real server integrated with Vite development server");
        console.log("📡 WebSocket support enabled on /ws");
      } catch (error) {
        console.error("💀 Failed to initialize WebSocket server:", error);

        // Fallback: use createExpressApp without WebSocket
        const { createExpressApp } = require("./server");
        console.log("🔄 Falling back to Express app without WebSocket...");
        const expressApp = createExpressApp();
        viteServer.middlewares.use(expressApp);
        console.log("⚠️ WebSocket support disabled - using fallback mode");
      }
    },
  };
}
