# Electron PowerShell Build Scripts

This directory contains PowerShell scripts to build and manage your Electron application.

## Prerequisites

- Node.js and npm installed
- PowerShell 5.1 or PowerShell Core 7+
- Windows, macOS, or Linux (with PowerShell installed)

## Scripts Overview

### Main Script: `electron.ps1`

The main orchestrator script that provides a unified interface for all Electron operations.

```powershell
# Show help
.\scripts\electron.ps1 help

# Start development environment
.\scripts\electron.ps1 dev

# Build for current platform
.\scripts\electron.ps1 build

# Build for all platforms
.\scripts\electron.ps1 build -All

# Clean build and rebuild
.\scripts\electron.ps1 build -Clean

# Run built application
.\scripts\electron.ps1 run

# Clean build artifacts
.\scripts\electron.ps1 clean
```

### Individual Scripts

#### `dev-electron.ps1`

Starts the development environment with hot reload.

- Installs dependencies if needed
- Starts both web dev server and Electron app

#### `build-electron.ps1`

Builds the production Electron application for the current platform.

- Supports clean builds with `-Clean`
- Supports package-only builds with `-PackageOnly`
- Runs type checking before building

#### `build-electron-all.ps1`

Builds distributables for all platforms (Windows, macOS, Linux).

- Supports clean builds with `-Clean`
- Can skip tests with `-SkipTests`
- Creates installers for each platform

#### `run-electron.ps1`

Runs the production-built Electron application.

- Can trigger a build with `-Build` if needed
- Checks for required build artifacts

## Usage Examples

### Development Workflow

```powershell
# Start development
.\scripts\electron.ps1 dev
```

### Production Build Workflow

```powershell
# Clean build for current platform
.\scripts\electron.ps1 build -Clean

# Build for all platforms
.\scripts\electron.ps1 build -All -Clean

# Run the built app
.\scripts\electron.ps1 run
```

### Quick Commands

```powershell
# Clean everything and rebuild
.\scripts\electron.ps1 clean
.\scripts\electron.ps1 build

# Package without rebuilding (if dist exists)
.\scripts\build-electron.ps1 -PackageOnly
```

## Output Locations

- **Development**: App runs directly from source
- **Built Application**: `dist/` directory
- **Electron Distributables**: `dist-electron/` directory
  - Windows: `.exe` installer and unpacked app
  - macOS: `.dmg` installer and `.app` bundle
  - Linux: `.AppImage` and other formats

## Platform-Specific Notes

### Windows

- Creates NSIS installer (`.exe`)
- Unpacked application in `dist-electron/win-unpacked/`

### macOS

- Creates DMG installer (`.dmg`)
- App bundle in `dist-electron/mac/`
- Requires code signing for distribution

### Linux

- Creates AppImage (`.AppImage`)
- Other formats available in `dist-electron/`

## Troubleshooting

### Common Issues

1. **"npm is not recognized"**

   - Ensure Node.js and npm are installed and in PATH

2. **"electron-builder not found"**

   - Scripts will install it automatically
   - Or install manually: `npm install -g electron-builder`

3. **Build fails on macOS/Linux from Windows**

   - Cross-platform building has limitations
   - Use platform-specific builds or CI/CD

4. **Permission errors on Linux/macOS**
   - Make scripts executable: `chmod +x scripts/*.ps1`

### Debug Mode

Add `-Verbose` to any script for detailed output:

```powershell
.\scripts\electron.ps1 build -Verbose
```

## CI/CD Integration

These scripts can be used in CI/CD pipelines:

```yaml
# GitHub Actions example
- name: Build Electron App
  shell: pwsh
  run: .\scripts\electron.ps1 build -All -SkipTests
```

## Customization

### Adding New Build Targets

Edit the electron-builder configuration in `package.json` to add new platforms or modify build settings.

### Environment Variables

Set environment variables before running scripts:

```powershell
$env:NODE_ENV = "production"
.\scripts\electron.ps1 build
```
