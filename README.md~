# W8 File Importer - Professional Desktop Tool

A production-ready full-stack React application with integrated Express server, featuring real-time WebSocket communication, advanced typography system, and modern UI components.

## 📚 Documentation Navigation

- **[📖 User Manual](./docs/USER_MANUAL.md)** - Complete user guide for end users and administrators
- **[🏗️ Architecture](./docs/ARCHITECTURE.md)** - System architecture and design patterns
- **[🚀 Development Setup](./docs/DEVELOPMENT.md)** - Getting started with development
- **[🎨 Typography & Theming](./docs/TYPOGRAPHY.md)** - Design system and typography guide
- **[📡 API Documentation](./docs/API.md)** - Complete API reference and WebSocket guide
- **[🚢 Deployment](./docs/DEPLOYMENT.md)** - Production deployment guide
- **[🧪 Testing](./docs/TESTING.md)** - Testing strategies and guidelines
- **[🔧 Troubleshooting](./docs/TROUBLESHOOTING.md)** - Common issues and solutions

## 🌟 Key Features

### 🔄 Real-Time Communication

- **WebSocket-First Architecture**: All operations use WebSocket for real-time updates
- **Connection Management**: 5-minute timeout with patient retry logic
- **Health Monitoring**: Automatic connection health checks and recovery

### 🎨 Advanced Design System

- **10 Professional Themes**: From Arctic Blue to Cyberpunk Neon
- **Typography Hierarchy**: Semantic typography system with responsive scaling
- **Modern UI Components**: Glass effects, gradients, and sophisticated animations

### 🏗️ Robust Architecture

- **Full-Stack TypeScript**: End-to-end type safety
- **Hot Reload**: Both client and server with instant updates
- **Production Ready**: Self-contained executables and Netlify deployment

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 📁 Project Structure

```
├── client/                 # React SPA frontend
│   ├── components/         # UI components
│   │   ├── ui/            # Base UI component library
│   │   ├── SplashScreen.tsx
│   │   └── ServiceControl.tsx
│   ├── pages/             # Route components
│   ├── hooks/             # Custom React hooks
│   ├── services/          # Business logic services
│   └── global.css         # Typography & theme system
├── server/                # Express API backend
│   ├── routes/            # API endpoints
│   ├── middleware/        # Custom middleware
│   ├── websocket.ts       # WebSocket manager
│   └── index.ts           # Server configuration
├── shared/                # Shared types and utilities
└── docs/                  # Documentation files
```

## 🔗 Quick Links

| Topic               | Description                        | Link                                                            |
| ------------------- | ---------------------------------- | --------------------------------------------------------------- |
| **User Guide**      | Complete user manual and workflows | [User Manual](./docs/USER_MANUAL.md#getting-started)            |
| **Getting Started** | Development setup and first steps  | [Development Guide](./docs/DEVELOPMENT.md#getting-started)      |
| **WebSocket API**   | Real-time communication guide      | [API Reference](./docs/API.md#websocket-api)                    |
| **Theme System**    | Typography and theming guide       | [Typography Guide](./docs/TYPOGRAPHY.md#theme-system)           |
| **Deployment**      | Production deployment steps        | [Deployment Guide](./docs/DEPLOYMENT.md#netlify-deployment)     |
| **Testing**         | Testing strategies and procedures  | [Testing Guide](./docs/TESTING.md#testing-overview)             |
| **Architecture**    | System design and patterns         | [Architecture Overview](./docs/ARCHITECTURE.md#system-overview) |

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript + Vite + TailwindCSS 3
- **Backend**: Express + WebSocket + TypeScript
- **UI**: Radix UI + Custom Design System + Framer Motion
- **Typography**: Inter + Space Grotesk + JetBrains Mono + Playfair Display
- **Deployment**: Netlify + Self-contained executables

## 🎯 Core Concepts

### WebSocket-First Design

Every operation in the application uses WebSocket communication for real-time updates. HTTP endpoints trigger actions, WebSocket delivers live progress and results.

### Professional Typography

A complete typography system with semantic classes, responsive scaling, and 4 carefully selected font families for different use cases.

### Connection Resilience

The application requires WebSocket connection and implements a 5-minute timeout system with intelligent retry logic and user feedback.

## 📋 Getting Help

- **[User Manual](./docs/USER_MANUAL.md)** - Complete user guide and workflows
- **[Troubleshooting Guide](./docs/TROUBLESHOOTING.md)** - Solutions to common issues
- **[API Reference](./docs/API.md)** - Complete API documentation
- **[Development Setup](./docs/DEVELOPMENT.md)** - Development environment setup

## 🤝 Contributing

See the [Development Guide](./docs/DEVELOPMENT.md#contributing) for information on how to contribute to this project.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

---

**🚀 Ready to get started?** Begin with the [Development Setup Guide](./docs/DEVELOPMENT.md) or explore the [Architecture Overview](./docs/ARCHITECTURE.md).
